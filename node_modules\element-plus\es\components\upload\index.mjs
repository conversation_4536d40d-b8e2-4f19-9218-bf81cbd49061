import Upload from './src/upload2.mjs';
export { genFileId, uploadBaseProps, uploadListTypes, uploadProps } from './src/upload.mjs';
export { uploadContentProps } from './src/upload-content2.mjs';
export { uploadListEmits, uploadListProps } from './src/upload-list2.mjs';
export { uploadDraggerEmits, uploadDraggerProps } from './src/upload-dragger.mjs';
export { uploadContextKey } from './src/constants.mjs';
import { withInstall } from '../../utils/vue/install.mjs';

const ElUpload = withInstall(Upload);

export { ElUpload, ElUpload as default };
//# sourceMappingURL=index.mjs.map
