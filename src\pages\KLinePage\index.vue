<template>
  <div class="kline-container">
    <!-- 查询表单 -->
    <el-card class="query-card">
      <el-form :model="queryForm" inline>
        <el-form-item label="股票代码" required>
          <el-input 
            v-model="queryForm.stock_code" 
            placeholder="请输入股票代码，如：000001"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="日期范围" required>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="fetchStockData" :loading="loading">
            查询
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- K线图展示 -->
    <el-card v-if="hasData" class="chart-card">
      <div class="chart-header">
        <div class="chart-title">
          {{ queryForm.stock_code }} K线图 ({{ queryForm.start_date }} 至 {{ queryForm.end_date }})
        </div>
        
        <div class="chart-tools">
          <!-- K线周期切换 -->
          <el-radio-group v-model="klineType" size="small" @change="handleKlineTypeChange">
            <el-radio-button label="day">日K</el-radio-button>
            <el-radio-button label="week">周K</el-radio-button>
            <el-radio-button label="month">月K</el-radio-button>
          </el-radio-group>
          
          <!-- 技术指标切换 -->
          <el-divider direction="vertical" />
          <el-checkbox-group v-model="indicators" size="small" @change="updateChart">
            <el-checkbox-button label="MA5">MA5</el-checkbox-button>
            <el-checkbox-button label="MA10">MA10</el-checkbox-button>
            <el-checkbox-button label="MA20">MA20</el-checkbox-button>
            <el-checkbox-button label="MA60">MA60</el-checkbox-button>
          </el-checkbox-group>
        </div>
      </div>
      
      <div class="chart-wrapper">
        <div ref="chartRef" class="echarts-container"></div>
      </div>
    </el-card>
    
    <!-- 无数据或错误提示 -->
    <el-empty v-else description="暂无数据，请查询股票数据"></el-empty>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { getStockData, calculateMA, formatKLineData, convertToWeekOrMonthData } from '@/api/stockData'
import { ElMessage } from 'element-plus'

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

// 查询表单
const queryForm = reactive({
  stock_code: '',
  start_date: '',
  end_date: ''
})

// 日期范围
const dateRange = ref([])

// 监听日期范围变化
watch(dateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    queryForm.start_date = newValue[0]
    queryForm.end_date = newValue[1]
  } else {
    queryForm.start_date = ''
    queryForm.end_date = ''
  }
})

// 加载状态
const loading = ref(false)

// 图表元素引用
const chartRef = ref(null)

// 图表实例
let chartInstance = null

// K线类型
const klineType = ref('day')

// 技术指标
const indicators = ref(['MA5', 'MA10'])

// 原始数据
const stockData = ref([])

// 处理后的数据
const processedData = ref({
  dates: [],
  values: []
})

// 买入卖出点数据
const tradingPointData = ref({
  buyPoints: {},
  sellPoints: {}
})

// 资金曲线数据
const fundData = ref({
  assetData: {},
  investmentData: {}
})

// 是否有数据
const hasData = computed(() => stockData.value.length > 0)

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  // 销毁旧实例
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  // 创建新实例
  chartInstance = echarts.init(chartRef.value)
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
  
  // 初始显示加载中
  chartInstance.showLoading()
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 重置表单
const resetForm = () => {
  queryForm.stock_code = ''
  dateRange.value = []
  queryForm.start_date = ''
  queryForm.end_date = ''
  
  stockData.value = []
  processedData.value = { dates: [], values: [] }
  tradingPointData.value = { buyPoints: {}, sellPoints: {} }
  fundData.value = { assetData: {}, investmentData: {} }
  
  if (chartInstance) {
    chartInstance.clear()
    chartInstance.showLoading()
  }
}

// 查询股票数据
const fetchStockData = async () => {
  // 表单验证
  if (!queryForm.stock_code) {
    ElMessage.warning('请输入股票代码')
    return
  }
  
  if (!queryForm.start_date || !queryForm.end_date) {
    ElMessage.warning('请选择日期范围')
    return
  }
  
  loading.value = true
  
  try {
    const response = await getStockData(queryForm)
    stockData.value = response || []
    
    if (stockData.value.length === 0) {
      ElMessage.warning('未找到符合条件的数据')
      loading.value = false
      return
    }
    
    // 根据K线类型处理数据
    handleKlineTypeChange(klineType.value)
    
  } catch (error) {
    console.error('获取股票数据失败:', error)
    ElMessage.error('获取股票数据失败，请稍后重试')
    stockData.value = []
    processedData.value = { dates: [], values: [] }
    tradingPointData.value = { buyPoints: {}, sellPoints: {} }
    fundData.value = { assetData: {}, investmentData: {} }
  } finally {
    loading.value = false
  }
}

// 处理K线类型变化
const handleKlineTypeChange = (type) => {
  if (!stockData.value || stockData.value.length === 0) return
  
  let data = [...stockData.value]
  
  if (type !== 'day') {
    data = convertToWeekOrMonthData(data, type)
  }
  
  // 格式化数据
  processedData.value = formatKLineData(data)
  
  // 计算交易点数据（根据均线交叉等条件）
  calculateTradingPoints()
  
  // 计算资金曲线数据
  calculateFundData()
  
  // 更新图表
  updateChart()
}

// 计算交易点数据（示例实现，实际情况可能更复杂）
const calculateTradingPoints = () => {
  // 清空现有数据
  tradingPointData.value = { buyPoints: {}, sellPoints: {} }
  
  if (!processedData.value.dates.length) return
  
  // 为每个指标周期计算买卖点
  indicators.value.forEach(indicator => {
    const period = parseInt(indicator.replace('MA', ''))
    const maData = calculateMA(stockData.value, period)
    
    // 简化的买卖点计算逻辑（实际情况中可能更复杂）
    const buyPoints = []
    const sellPoints = []
    
    // 从第period个数据点开始判断（前面的数据点无法计算MA）
    for (let i = period; i < processedData.value.dates.length; i++) {
      const currentClose = processedData.value.values[i][1] // 当日收盘价
      const currentMA = maData[i]
      const prevClose = processedData.value.values[i-1][1] // 前一日收盘价
      const prevMA = maData[i-1]
      
      // 判断是否为买入信号：价格从下方穿越均线
      if (currentClose > currentMA && prevClose <= prevMA) {
        buyPoints.push({
          date: processedData.value.dates[i],
          price: currentClose
        })
      }
      
      // 判断是否为卖出信号：价格从上方穿越均线
      if (currentClose < currentMA && prevClose >= prevMA) {
        sellPoints.push({
          date: processedData.value.dates[i],
          price: currentClose
        })
      }
    }
    
    // 保存该周期的买卖点
    if (buyPoints.length > 0) {
      tradingPointData.value.buyPoints[period] = buyPoints
    }
    
    if (sellPoints.length > 0) {
      tradingPointData.value.sellPoints[period] = sellPoints
    }
  })
}

// 计算资金曲线数据
const calculateFundData = () => {
  // 清空现有数据
  fundData.value = { assetData: {}, investmentData: {} }
  
  if (!processedData.value.dates.length) return
  
  // 为每个指标周期计算资金曲线
  indicators.value.forEach(indicator => {
    const period = parseInt(indicator.replace('MA', ''))
    
    // 初始资金和持仓状态
    const initialCapital = 100000 // 示例初始资金
    let currentCapital = initialCapital
    let holdingShares = 0
    let isHolding = false
    
    // 资产曲线和投入曲线数据
    const assetData = []
    const investmentData = []
    
    // 从买卖点数据中提取该周期的交易信号
    const buyPoints = tradingPointData.value.buyPoints[period] || []
    const sellPoints = tradingPointData.value.sellPoints[period] || []
    
    // 按日期排序并合并买卖点
    const allTradingPoints = [
      ...buyPoints.map(point => ({ ...point, action: 'buy' })),
      ...sellPoints.map(point => ({ ...point, action: 'sell' }))
    ].sort((a, b) => {
      // 按日期排序
      const dateA = new Date(a.date)
      const dateB = new Date(b.date)
      return dateA - dateB
    })
    
    // 初始投入
    if (processedData.value.dates.length > 0) {
      investmentData.push([
        processedData.value.dates[0],
        initialCapital
      ])
      
      // 初始资产等于初始资金
      assetData.push([
        processedData.value.dates[0],
        initialCapital
      ])
    }
    
    // 遍历所有交易日，更新资金曲线
    for (let i = 1; i < processedData.value.dates.length; i++) {
      const currentDate = processedData.value.dates[i]
      const currentPrice = processedData.value.values[i][1] // 收盘价
      
      // 检查当前日期是否有交易点
      const tradingPoint = allTradingPoints.find(p => p.date === currentDate)
      
      if (tradingPoint) {
        if (tradingPoint.action === 'buy' && !isHolding) {
          // 买入操作：全仓买入
          holdingShares = currentCapital / currentPrice
          isHolding = true
        } else if (tradingPoint.action === 'sell' && isHolding) {
          // 卖出操作：全部卖出
          currentCapital = holdingShares * currentPrice
          holdingShares = 0
          isHolding = false
        }
      }
      
      // 计算当前资产价值
      const currentAsset = isHolding ? holdingShares * currentPrice : currentCapital
      
      // 添加到资产曲线数据
      assetData.push([
        currentDate,
        parseFloat(currentAsset.toFixed(2))
      ])
      
      // 更新投入曲线（投入金额保持不变，直到新的买入）
      investmentData.push([
        currentDate,
        parseFloat(initialCapital.toFixed(2))
      ])
    }
    
    // 保存资金曲线数据
    fundData.value.assetData[period] = assetData
    fundData.value.investmentData[period] = investmentData
  })
}

// 更新图表
const updateChart = () => {
  if (!chartInstance || !processedData.value.dates.length) return
  
  const { dates, values } = processedData.value
  
  // 计算MA数据
  const ma5 = indicators.value.includes('MA5') ? calculateMA(stockData.value, 5) : []
  const ma10 = indicators.value.includes('MA10') ? calculateMA(stockData.value, 10) : []
  const ma20 = indicators.value.includes('MA20') ? calculateMA(stockData.value, 20) : []
  const ma60 = indicators.value.includes('MA60') ? calculateMA(stockData.value, 60) : []
  
  // 准备买入卖出点数据
  const buyPointSeries = []
  const sellPointSeries = []
  
  // 为每个选中的均线周期添加买入卖出点
  indicators.value.forEach(indicator => {
    const period = parseInt(indicator.replace('MA', ''))
    
    // 添加买入点
    if (tradingPointData.value.buyPoints[period]) {
      buyPointSeries.push({
        name: `${indicator}买入点`,
        type: 'scatter',
        data: tradingPointData.value.buyPoints[period].map(p => [p.date, p.price]),
        symbol: 'arrow',
        symbolSize: 10,
        symbolRotate: -90, // 向上箭头
        itemStyle: {
          color: '#FF9800' // 买入点颜色
        },
        label: {
          show: true,
          position: 'top',
          formatter: 'B', // 买入标签
          fontSize: 12
        }
      })
    }
    
    // 添加卖出点
    if (tradingPointData.value.sellPoints[period]) {
      sellPointSeries.push({
        name: `${indicator}卖出点`,
        type: 'scatter',
        data: tradingPointData.value.sellPoints[period].map(p => [p.date, p.price]),
        symbol: 'arrow',
        symbolSize: 10,
        symbolRotate: 90, // 向下箭头
        itemStyle: {
          color: '#2196F3' // 卖出点颜色
        },
        label: {
          show: true,
          position: 'bottom',
          formatter: 'S', // 卖出标签
          fontSize: 12
        }
      })
    }
  })
  
  // 准备资金曲线数据
  const fundSeries = []
  
  // 为每个选中的均线周期添加资金曲线
  indicators.value.forEach(indicator => {
    const period = parseInt(indicator.replace('MA', ''))
    
    // 添加资产曲线
    if (fundData.value.assetData[period]) {
      fundSeries.push({
        name: `${indicator}资产`,
        type: 'line',
        xAxisIndex: 2,
        yAxisIndex: 2,
        data: fundData.value.assetData[period],
        symbol: 'none',
        lineStyle: {
          width: 2,
          color: getMALineColor(period) // 使用与均线相同的颜色
        },
        z: 1
      })
    }
    
    // 添加投入曲线
    if (fundData.value.investmentData[period]) {
      fundSeries.push({
        name: `${indicator}投入`,
        type: 'line',
        xAxisIndex: 2,
        yAxisIndex: 2,
        data: fundData.value.investmentData[period],
        symbol: 'none',
        lineStyle: {
          width: 1,
          type: 'dashed',
          color: getMALineColor(period) // 使用与均线相同的颜色但虚线
        },
        z: 0
      })
    }
  })
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      borderWidth: 1,
      borderColor: '#ccc',
      padding: 10,
      textStyle: {
        color: '#000'
      },
      formatter: (params) => {
        // 找到对应的数据点
        const candleData = params.find(p => p.seriesName === 'K线')
        const date = candleData ? candleData.axisValue : params[0].axisValue
        
        // 格式化内容
        let res = `<div style="font-weight:bold;margin-bottom:5px;">${date}</div>`
        
        // K线数据
        if (candleData) {
          const data = candleData.data
          res += `<div>开盘价: ${data[0]}</div>`
          res += `<div>收盘价: ${data[1]}</div>`
          res += `<div>最低价: ${data[2]}</div>`
          res += `<div>最高价: ${data[3]}</div>`
          res += `<div>成交量: ${data[4]}</div>`
        }
        
        // 添加MA数据
        params.forEach(param => {
          if (param.seriesName.startsWith('MA') && param.seriesName.length <= 4) {
            if (param.value !== '-') {
              res += `<div style="color:${param.color}">${param.seriesName}: ${param.value}</div>`
            }
          }
        })
        
        // 添加资金曲线数据
        const assetParams = params.filter(p => p.seriesName.includes('资产'))
        const investParams = params.filter(p => p.seriesName.includes('投入'))
        
        if (assetParams.length > 0 || investParams.length > 0) {
          res += `<div style="margin-top:5px;border-top:1px solid #ccc;padding-top:5px;">资金曲线:</div>`
          
          assetParams.forEach(param => {
            if (param.data && param.data.length > 1) {
              res += `<div style="color:${param.color}">${param.seriesName}: ${param.data[1]}</div>`
            }
          })
          
          investParams.forEach(param => {
            if (param.data && param.data.length > 1) {
              res += `<div style="color:${param.color}">${param.seriesName}: ${param.data[1]}</div>`
            }
          })
        }
        
        return res
      }
    },
    legend: {
      data: ['K线', 'MA5', 'MA10', 'MA20', 'MA60', ...indicators.value.map(ma => `${ma}资产`), ...indicators.value.map(ma => `${ma}投入`)],
      selected: {
        'K线': true,
        'MA5': indicators.value.includes('MA5'),
        'MA10': indicators.value.includes('MA10'),
        'MA20': indicators.value.includes('MA20'),
        'MA60': indicators.value.includes('MA60')
      }
    },
    grid: [
      {
        left: '10%',
        right: '10%',
        top: '10%',
        height: '40%'
      },
      {
        left: '10%',
        right: '10%',
        top: '55%',
        height: '15%'
      },
      {
        left: '10%',
        right: '10%',
        top: '75%',
        height: '15%'
      }
    ],
    xAxis: [
      {
        type: 'category',
        data: dates,
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        splitLine: { show: false },
        splitNumber: 20,
        min: 'dataMin',
        max: 'dataMax',
        gridIndex: 0
      },
      {
        type: 'category',
        gridIndex: 1,
        data: dates,
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: { show: false },
        splitNumber: 20,
        min: 'dataMin',
        max: 'dataMax'
      },
      {
        type: 'category',
        gridIndex: 2,
        data: dates,
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        axisTick: { show: false },
        splitLine: { show: false },
        splitNumber: 20,
        min: 'dataMin',
        max: 'dataMax'
      }
    ],
    yAxis: [
      {
        scale: true,
        splitArea: {
          show: true
        },
        gridIndex: 0
      },
      {
        scale: true,
        gridIndex: 1,
        splitNumber: 2,
        axisLabel: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false }
      },
      {
        name: '资金',
        nameLocation: 'middle',
        nameGap: 35,
        scale: true,
        gridIndex: 2,
        splitLine: { 
          show: true,
          lineStyle: {
            type: 'dashed'
          }
        }
      }
    ],
    dataZoom: [
      {
        type: 'inside',
        xAxisIndex: [0, 1, 2],
        start: 50,
        end: 100
      },
      {
        show: true,
        xAxisIndex: [0, 1, 2],
        type: 'slider',
        top: '92%',
        start: 50,
        end: 100
      }
    ],
    series: [
      {
        name: 'K线',
        type: 'candlestick',
        data: values,
        itemStyle: {
          color: '#ef232a',
          color0: '#14b143',
          borderColor: '#ef232a',
          borderColor0: '#14b143'
        },
        markPoint: {
          label: {
            formatter: function (param) {
              return param != null ? Math.round(param.value) + '' : '';
            }
          },
          data: [
            {
              name: '最高值',
              type: 'max',
              valueDim: 'highest'
            },
            {
              name: '最低值',
              type: 'min',
              valueDim: 'lowest'
            }
          ]
        }
      },
      {
        name: 'MA5',
        type: 'line',
        data: ma5,
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 1,
          color: getMALineColor(5)
        }
      },
      {
        name: 'MA10',
        type: 'line',
        data: ma10,
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 1,
          color: getMALineColor(10)
        }
      },
      {
        name: 'MA20',
        type: 'line',
        data: ma20,
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 1,
          color: getMALineColor(20)
        }
      },
      {
        name: 'MA60',
        type: 'line',
        data: ma60,
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 1,
          color: getMALineColor(60)
        }
      },
      {
        name: '成交量',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        data: values.map(item => item[4])
      },
      // 添加买入点系列
      ...buyPointSeries,
      // 添加卖出点系列
      ...sellPointSeries,
      // 添加资金曲线系列
      ...fundSeries
    ]
  }
  
  chartInstance.hideLoading()
  chartInstance.setOption(option, true)
}

// 获取均线颜色（从maAnalysis.js导入）
const getMALineColor = (period) => {
  const colors = {
    5: '#FF5722',   // 橙红色
    10: '#2196F3',  // 蓝色
    20: '#4CAF50',  // 绿色
    30: '#9C27B0',  // 紫色
    60: '#FF9800',  // 橙色
    120: '#795548', // 棕色
    250: '#607D8B'  // 蓝灰色
  }
  
  return colors[period] || '#999' // 默认灰色
}

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

// 组件卸载前清理
onUnmounted(() => {
  if (chartInstance) {
    window.removeEventListener('resize', handleResize)
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style scoped>
.kline-container {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 18px;
  font-weight: bold;
}

.chart-tools {
  display: flex;
  align-items: center;
}

.chart-wrapper {
  position: relative;
}

.echarts-container {
  width: 100%;
  height: 600px;
}
</style> 