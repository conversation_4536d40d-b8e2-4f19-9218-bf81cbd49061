{"version": 3, "file": "option.mjs", "sources": ["../../../../../../packages/components/select/src/option.vue"], "sourcesContent": ["<template>\n  <li\n    v-show=\"visible\"\n    :id=\"id\"\n    :class=\"containerKls\"\n    role=\"option\"\n    :aria-disabled=\"isDisabled || undefined\"\n    :aria-selected=\"itemSelected\"\n    @mousemove=\"hoverItem\"\n    @click.stop=\"selectOptionClick\"\n  >\n    <slot>\n      <span>{{ currentLabel }}</span>\n    </slot>\n  </li>\n</template>\n\n<script lang=\"ts\">\n// @ts-nocheck\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  nextTick,\n  onBeforeUnmount,\n  reactive,\n  toRefs,\n  unref,\n} from 'vue'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport { useOption } from './useOption'\nimport type { SelectOptionProxy } from './token'\n\nexport default defineComponent({\n  name: 'ElOption',\n  componentName: 'ElOption',\n\n  props: {\n    /**\n     * @description value of option\n     */\n    value: {\n      required: true,\n      type: [String, Number, Boolean, Object],\n    },\n    /**\n     * @description label of option, same as `value` if omitted\n     */\n    label: [String, Number],\n    created: Boolean,\n    /**\n     * @description whether option is disabled\n     */\n    disabled: Boolean,\n  },\n\n  setup(props) {\n    const ns = useNamespace('select')\n    const id = useId()\n\n    const containerKls = computed(() => [\n      ns.be('dropdown', 'item'),\n      ns.is('disabled', unref(isDisabled)),\n      ns.is('selected', unref(itemSelected)),\n      ns.is('hovering', unref(hover)),\n    ])\n\n    const states = reactive({\n      index: -1,\n      groupDisabled: false,\n      visible: true,\n      hover: false,\n    })\n\n    const {\n      currentLabel,\n      itemSelected,\n      isDisabled,\n      select,\n      hoverItem,\n      updateOption,\n    } = useOption(props, states)\n\n    const { visible, hover } = toRefs(states)\n\n    const vm = getCurrentInstance().proxy as unknown as SelectOptionProxy\n\n    select.onOptionCreate(vm)\n\n    onBeforeUnmount(() => {\n      const key = vm.value\n      const { selected: selectedOptions } = select.states\n      const doesSelected = selectedOptions.some((item) => {\n        return item.value === vm.value\n      })\n      // if option is not selected, remove it from cache\n      nextTick(() => {\n        if (select.states.cachedOptions.get(key) === vm && !doesSelected) {\n          select.states.cachedOptions.delete(key)\n        }\n      })\n      select.onOptionDestroy(key, vm)\n    })\n\n    function selectOptionClick() {\n      if (!isDisabled.value) {\n        select.handleOptionSelect(vm)\n      }\n    }\n\n    return {\n      ns,\n      id,\n      containerKls,\n      currentLabel,\n      itemSelected,\n      isDisabled,\n      select,\n      hoverItem,\n      updateOption,\n      visible,\n      hover,\n      selectOptionClick,\n      states,\n    }\n  },\n})\n</script>\n"], "names": ["_withDirectives", "_openBlock", "_createElementBlock", "_normalizeClass", "_createElementVNode", "_toDisplayString"], "mappings": ";;;;;;AAiCA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,UAAA;AAAA,EACN,aAAe,EAAA,UAAA;AAAA,EAEf,KAAO,EAAA;AAAA,IAAA,KAAA,EAAA;AAAA,MAAA,QAAA,EAAA,IAAA;AAAA,MAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,OAAA,EAAA,MAAA,CAAA;AAAA,KAIE;AAAA,IAAA,KACK,EAAA,CAAA,MAAA,EAAA,MAAA,CAAA;AAAA,IAAA,OACJ,EAAC,OAAQ;AAAuB,IACxC,QAAA,EAAA,OAAA;AAAA,GAAA;AAAA,EAAA,KAAA,CAAA,KAAA,EAAA;AAAA,IAAA,MAAA,EAAA,GAAA,YAAA,CAAA,QAAA,CAAA,CAAA;AAAA,IAIA,MAAA,EAAQ,GAAA,KAAQ,EAAM,CAAA;AAAA,IACtB,MAAS,YAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,MAAA,CAAA;AAAA,MAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAAA,MAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,KAAA,CAAA,YAAA,CAAA,CAAA;AAAA,MAIC,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KACZ,CAAA,CAAA;AAAA,UAEa,MAAA,GAAA,QAAA,CAAA;AACX,MAAM,KAAA,EAAA,CAAA,CAAK;AACX,MAAA,aAAiB,EAAA,KAAA;AAEjB,MAAM,OAAA,EAAA,IAAA;AAA8B,MAClC,KAAM,EAAA,KAAA;AAAkB,KAAA,CACxB,CAAG;AAAgC,IAAA,MAChC;AAAkC,MACrC,YAAkB;AAAY,MAC/B,YAAA;AAED,MAAA;AAAwB,MACtB,MAAO;AAAA,MACP,SAAe;AAAA,MACf,YAAS;AAAA,KAAA,GACF,SAAA,CAAA,KAAA,EAAA,MAAA,CAAA,CAAA;AAAA,IACT,MAAC,EAAA,OAAA,EAAA,KAAA,EAAA,GAAA,MAAA,CAAA,MAAA,CAAA,CAAA;AAED,IAAM,MAAA,EAAA,GAAA,kBAAA,EAAA,CAAA,KAAA,CAAA;AAAA,IACJ,MAAA,CAAA,cAAA,CAAA,EAAA,CAAA,CAAA;AAAA,IACA,eAAA,CAAA,MAAA;AAAA,MACA,MAAA,GAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AAAA,MACA,MAAA,EAAA,QAAA,EAAA,eAAA,EAAA,GAAA,MAAA,CAAA,MAAA,CAAA;AAAA,MACA,MAAA,YAAA,GAAA,eAAA,CAAA,IAAA,CAAA,CAAA,IAAA,KAAA;AAAA,QACA,OAAA,IAAA,CAAA,KAAA,KAAA,EAAA,CAAA,KAAA,CAAA;AAAA,OACF,CAAI,CAAU;AAEd,MAAA,QAAQ,CAAA,MAAS;AAEjB,QAAM,IAAA,2BAA0B,CAAA,GAAA,CAAA,GAAA,CAAA,KAAA,EAAA,IAAA,CAAA,YAAA,EAAA;AAEhC,UAAA,2BAAwB,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA;AAExB,SAAA;AACE,OAAA,CAAA,CAAA;AACA,MAAA,MAAM,CAAE,eAA0B,CAAA,GAAA,EAAA,EAAA,CAAA,CAAA;AAClC,KAAA,CAAA,CAAA;AACE,IAAO,SAAA,iBAAe,GAAG;AAAA,MAC3B,IAAC,CAAA,UAAA,CAAA,KAAA,EAAA;AAED,QAAA,MAAA,CAAS,kBAAM,CAAA,EAAA,CAAA,CAAA;AACb,OAAI;AACF,KAAO;AAA+B,IACxC,OAAA;AAAA,MACF,EAAC;AACD,MAAO,EAAA;AAAuB,MAC/B,YAAA;AAED,MAAA,YAA6B;AAC3B,MAAI;AACF,MAAA;AAA4B,MAC9B,MAAA;AAAA,MACF,SAAA;AAEA,MAAO,YAAA;AAAA,MACL,OAAA;AAAA,MACA,KAAA;AAAA,MACA,iBAAA;AAAA,MACA,MAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CACA,CAAA,CAAA;AACA,SACA,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EACA,OAAAA,cAAA,EAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,IAAA,EAAA;AAAA,IACA,EAAA,EAAA,IAAA,CAAA,EAAA;AAAA,IACA,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,YAAA,CAAA;AAAA,IACA,IAAA,EAAA,QAAA;AAAA,IACF,eAAA,EAAA,IAAA,CAAA,UAAA,IAAA,KAAA,CAAA;AAAA,IACF,eAAA,EAAA,IAAA,CAAA,YAAA;AACF,IAAC,WAAA,EAAA,IAAA,CAAA,SAAA;;;uCA7HC,EAaK,EAAA,MAAA;AAAA,MAXEC,kBAAA,CAAA,MAAA,EAAA,IAAA,EAAAC,eAAA,CAAA,IAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KACJ,CAAA;AAAmB,GAAA,EACf,EAAA,EAAA,CAAA,IAAA,EAAA,eAAA,EAAA,eAAA,EAAA,aAAA,EAAA,SAAA,CAAA,CAAA,GAAA;AAAA,IACJ,oBAA6B,CAAA;AAAA,GAAA,CAC7B,CAAe;AAAA,CAAA;AAEc,aAAA,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,YAAA,CAAA,CAAA,CAAA;;;;"}