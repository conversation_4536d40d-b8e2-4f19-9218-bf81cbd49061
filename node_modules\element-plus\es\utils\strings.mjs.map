{"version": 3, "file": "strings.mjs", "sources": ["../../../../packages/utils/strings.ts"], "sourcesContent": ["import { capitalize as toCapitalize } from '@vue/shared'\nexport {\n  camelize,\n  hyphenate,\n  hyphenate as kebabCase, // alias\n} from '@vue/shared'\n\n/**\n * fork from {@link https://github.com/sindresorhus/escape-string-regexp}\n */\nexport const escapeStringRegexp = (string = '') =>\n  string.replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&').replace(/-/g, '\\\\x2d')\n\n// NOTE: improve capitalize types. Restore previous code after the [PR](https://github.com/vuejs/core/pull/6212) merge\nexport const capitalize = <T extends string>(str: T) =>\n  toCapitalize(str) as Capitalize<T>\n"], "names": ["toCapitalize"], "mappings": ";;;AAMY,MAAC,kBAAkB,GAAG,CAAC,MAAM,GAAG,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE;AAC5G,MAAC,UAAU,GAAG,CAAC,GAAG,KAAKA,YAAY,CAAC,GAAG;;;;"}