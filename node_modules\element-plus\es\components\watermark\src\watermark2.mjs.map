{"version": 3, "file": "watermark2.mjs", "sources": ["../../../../../../packages/components/watermark/src/watermark.vue"], "sourcesContent": ["<template>\n  <div ref=\"containerRef\" :style=\"[style]\">\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  onBeforeUnmount,\n  onMounted,\n  ref,\n  shallowRef,\n  watch,\n} from 'vue'\nimport { useMutationObserver } from '@vueuse/core'\nimport { isArray } from '@element-plus/utils'\nimport { watermarkProps } from './watermark'\nimport { getPixelRatio, getStyleStr, reRendering } from './utils'\nimport useClips, { FontGap } from './useClips'\nimport type { WatermarkProps } from './watermark'\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElWatermark',\n})\n\nconst style: CSSProperties = {\n  position: 'relative',\n}\n\nconst props = defineProps(watermarkProps)\nconst color = computed(() => props.font?.color ?? 'rgba(0,0,0,.15)')\nconst fontSize = computed(() => props.font?.fontSize ?? 16)\nconst fontWeight = computed(() => props.font?.fontWeight ?? 'normal')\nconst fontStyle = computed(() => props.font?.fontStyle ?? 'normal')\nconst fontFamily = computed(() => props.font?.fontFamily ?? 'sans-serif')\nconst textAlign = computed(() => props.font?.textAlign ?? 'center')\nconst textBaseline = computed(() => props.font?.textBaseline ?? 'hanging')\n\nconst gapX = computed(() => props.gap[0])\nconst gapY = computed(() => props.gap[1])\nconst gapXCenter = computed(() => gapX.value / 2)\nconst gapYCenter = computed(() => gapY.value / 2)\nconst offsetLeft = computed(() => props.offset?.[0] ?? gapXCenter.value)\nconst offsetTop = computed(() => props.offset?.[1] ?? gapYCenter.value)\n\nconst getMarkStyle = () => {\n  const markStyle: CSSProperties = {\n    zIndex: props.zIndex,\n    position: 'absolute',\n    left: 0,\n    top: 0,\n    width: '100%',\n    height: '100%',\n    pointerEvents: 'none',\n    backgroundRepeat: 'repeat',\n  }\n\n  /** Calculate the style of the offset */\n  let positionLeft = offsetLeft.value - gapXCenter.value\n  let positionTop = offsetTop.value - gapYCenter.value\n  if (positionLeft > 0) {\n    markStyle.left = `${positionLeft}px`\n    markStyle.width = `calc(100% - ${positionLeft}px)`\n    positionLeft = 0\n  }\n  if (positionTop > 0) {\n    markStyle.top = `${positionTop}px`\n    markStyle.height = `calc(100% - ${positionTop}px)`\n    positionTop = 0\n  }\n  markStyle.backgroundPosition = `${positionLeft}px ${positionTop}px`\n\n  return markStyle\n}\n\nconst containerRef = shallowRef<HTMLDivElement | null>(null)\nconst watermarkRef = shallowRef<HTMLDivElement>()\nconst stopObservation = ref(false)\n\nconst destroyWatermark = () => {\n  if (watermarkRef.value) {\n    watermarkRef.value.remove()\n    watermarkRef.value = undefined\n  }\n}\nconst appendWatermark = (base64Url: string, markWidth: number) => {\n  if (containerRef.value && watermarkRef.value) {\n    stopObservation.value = true\n    watermarkRef.value.setAttribute(\n      'style',\n      getStyleStr({\n        ...getMarkStyle(),\n        backgroundImage: `url('${base64Url}')`,\n        backgroundSize: `${Math.floor(markWidth)}px`,\n      })\n    )\n    containerRef.value?.append(watermarkRef.value)\n    // Delayed execution\n    setTimeout(() => {\n      stopObservation.value = false\n    })\n  }\n}\n\n/**\n * Get the width and height of the watermark. The default values are as follows\n * Image: [120, 64]; Content: It's calculated by content;\n */\nconst getMarkSize = (ctx: CanvasRenderingContext2D) => {\n  let defaultWidth = 120\n  let defaultHeight = 64\n  const image = props.image\n  const content = props.content\n  const width = props.width\n  const height = props.height\n  if (!image && ctx.measureText) {\n    ctx.font = `${Number(fontSize.value)}px ${fontFamily.value}`\n    const contents = isArray(content) ? content : [content]\n    const sizes = contents.map((item) => {\n      const metrics = ctx.measureText(item!)\n\n      return [\n        metrics.width,\n        // Using `actualBoundingBoxAscent` to be compatible with lower version browsers (eg: Firefox < 116)\n        metrics.fontBoundingBoxAscent !== undefined\n          ? metrics.fontBoundingBoxAscent + metrics.fontBoundingBoxDescent\n          : metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent,\n      ]\n    })\n    defaultWidth = Math.ceil(Math.max(...sizes.map((size) => size[0])))\n    defaultHeight =\n      Math.ceil(Math.max(...sizes.map((size) => size[1]))) * contents.length +\n      (contents.length - 1) * FontGap\n  }\n  return [width ?? defaultWidth, height ?? defaultHeight] as const\n}\n\nconst getClips = useClips()\n\nconst renderWatermark = () => {\n  const canvas = document.createElement('canvas')\n  const ctx = canvas.getContext('2d')\n  const image = props.image\n  const content = props.content\n  const rotate = props.rotate\n\n  if (ctx) {\n    if (!watermarkRef.value) {\n      watermarkRef.value = document.createElement('div')\n    }\n\n    const ratio = getPixelRatio()\n    const [markWidth, markHeight] = getMarkSize(ctx)\n\n    const drawCanvas = (\n      drawContent?: NonNullable<WatermarkProps['content']> | HTMLImageElement\n    ) => {\n      const [textClips, clipWidth] = getClips(\n        drawContent || '',\n        rotate,\n        ratio,\n        markWidth,\n        markHeight,\n        {\n          color: color.value,\n          fontSize: fontSize.value,\n          fontStyle: fontStyle.value,\n          fontWeight: fontWeight.value,\n          fontFamily: fontFamily.value,\n          textAlign: textAlign.value,\n          textBaseline: textBaseline.value,\n        },\n        gapX.value,\n        gapY.value\n      )\n\n      appendWatermark(textClips, clipWidth)\n    }\n\n    if (image) {\n      const img = new Image()\n      img.onload = () => {\n        drawCanvas(img)\n      }\n      img.onerror = () => {\n        drawCanvas(content)\n      }\n      img.crossOrigin = 'anonymous'\n      img.referrerPolicy = 'no-referrer'\n      img.src = image\n    } else {\n      drawCanvas(content)\n    }\n  }\n}\n\nonMounted(() => {\n  renderWatermark()\n})\n\nwatch(\n  () => props,\n  () => {\n    renderWatermark()\n  },\n  {\n    deep: true,\n    flush: 'post',\n  }\n)\n\nonBeforeUnmount(() => {\n  destroyWatermark()\n})\n\nconst onMutate = (mutations: MutationRecord[]) => {\n  if (stopObservation.value) {\n    return\n  }\n  mutations.forEach((mutation) => {\n    if (reRendering(mutation, watermarkRef.value)) {\n      destroyWatermark()\n      renderWatermark()\n    }\n  })\n}\n\nuseMutationObserver(containerRef, onMutate, {\n  attributes: true,\n  subtree: true,\n  childList: true,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;mCAuBc,CAAA;AAAA,EACZ,IAAM,EAAA,aAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAEA,IAAA,MAAM,KAAuB,GAAA;AAAA,MAC3B,QAAU,EAAA,UAAA;AAAA,KACZ,CAAA;AAGA,IAAA,MAAM,QAAQ,QAAS,CAAA,MAAM;AAC7B,MAAA,IAAM;AACN,MAAA,mBAA4B,KAAA,CAAA,IAAA,KAAY,IAAA,GAAA,iBAAoB,KAAQ,IAAA,GAAA,EAAA,GAAA,iBAAA,CAAA;AACpE,KAAA,CAAA,CAAA;AACA,IAAA,MAAM,mBAAsB,CAAA,MAAA;AAC5B,MAAA,IAAM;AACN,MAAA,mBAAqB,KAAS,CAAA,IAAA,KAAM,IAAM,GAAA,KAAM,oBAAyB,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA;AAEzE,KAAA,CAAA,CAAA;AACA,IAAA,MAAM,UAAgB,GAAA,QAAA,CAAM,MAAM;AAClC,MAAA,IAAM,EAAa,EAAA,EAAA,CAAA;AACnB,MAAA,OAAmB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAe,KAAA,YAAc,CAAA,GAAA,EAAA,CAAA,UAAA,KAAA,IAAA,GAAA,EAAA,GAAA,QAAA,CAAA;AAChD,KAAM,CAAA,CAAA;AACN,IAAM,MAAA,SAAA,GAAY,SAAS,MAAM;AAEjC,MAAA,IAAM;AACJ,MAAA,OAAiC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,EAAA,GAAA,QAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AACjB,IAAA,MACJ,UAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MAAA,IACJ,EAAA,EAAA,EAAA,CAAA;AAAA,MAAA,OACD,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,KAAA,IAAA,GAAA,EAAA,GAAA,YAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AACE,IAAA,MACC,SAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MAAA,IACO,EAAA,EAAA,EAAA,CAAA;AAAA,MAAA,OACG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,EAAA,GAAA,QAAA,CAAA;AAAA,KACpB,CAAA,CAAA;AAGA,IAAI,MAAA,YAAA,GAAe,QAAW,CAAA,MAAA;AAC9B,MAAI,IAAA,EAAA,EAAA,EAAA,CAAA;AACJ,MAAA,mBAAmB,KAAG,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,KAAA,IAAA,GAAA,EAAA,GAAA,SAAA,CAAA;AACpB,KAAU,CAAA,CAAA;AACV,IAAU,MAAA,IAAA,GAAA,QAAA,CAAQ,eAAe,CAAY,CAAA,CAAA,CAAA,CAAA;AAC7C,IAAe,MAAA,IAAA,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,IACjB,MAAA,UAAA,GAAA,QAAA,CAAA,MAAA,IAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAI,qBAAiB,CAAA,MAAA,IAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACnB,IAAU,MAAA,UAAA,GAAA,QAAoB,CAAA,MAAA;AAC9B,MAAU,IAAA,EAAA,EAAA,EAAA,CAAA;AACV,MAAc,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAAA,KAChB,CAAA,CAAA;AACA,IAAA,MAAA,SAA+B,GAAA,QAAA,CAAA,MAAA;AAE/B,MAAO,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MACT,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAEA,KAAM,CAAA,CAAA;AACN,IAAA,MAAM,eAAe,MAA2B;AAChD,MAAM,MAAA,SAAA,GAAA;AAEN,QAAA;AACE,QAAA,oBAAwB;AACtB,QAAA,IAAA,EAAA,CAAA;AACA,QAAA,GAAA,EAAA,CAAA;AAAqB,QACvB,KAAA,EAAA,MAAA;AAAA,QACF,MAAA,EAAA,MAAA;AACA,QAAM,aAAA,EAAA,MAAmB;AACvB,QAAI,gBAAsB,EAAA,QAAA;AACxB,OAAA,CAAA;AACA,MAAA,IAAA,YAAmB,GAAA,UAAA,CAAA,KAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAAA,MACjB,IAAA,WAAA,GAAA,SAAA,CAAA,KAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAAA,MAAA,IACA,YAAY,GAAA,CAAA,EAAA;AAAA,QAAA,SACM,CAAA,IAAA,GAAA,CAAA,EAAA,YAAA,CAAA,EAAA,CAAA,CAAA;AAAA,QAChB,SAAA,CAAA,KAAA,GAAA,CAAA,YAAkC,EAAA,YAAA,CAAA,GAAA,CAAA,CAAA;AAAA,QAAA,YAClB,GAAA,CAAA,CAAA;AAAwB,OAAA;AACzC,MACH,IAAA,WAAA,GAAA,CAAA,EAAA;AACA,QAAa,SAAA,CAAA,GAAA,GAAA,CAAA,EAAA,WAAc,CAAA,EAAA,CAAA,CAAA;AAE3B,QAAA,SAAA,CAAA,MAAiB,GAAA,CAAA,YAAA,EAAA,WAAA,CAAA,GAAA,CAAA,CAAA;AACf,QAAA,WAAA,GAAA,CAAA,CAAA;AAAwB,OAAA;AACzB,MACH,SAAA,CAAA,kBAAA,GAAA,CAAA,EAAA,YAAA,CAAA,GAAA,EAAA,WAAA,CAAA,EAAA,CAAA,CAAA;AAAA,MACF,OAAA,SAAA,CAAA;AAMA,KAAM,CAAA;AACJ,IAAA,MAAI,YAAe,GAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AACnB,IAAA,MAAI,YAAgB,GAAA,UAAA,EAAA,CAAA;AACpB,IAAA,MAAA,eAAoB,GAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AACpB,IAAA,MAAA,gBAAsB,GAAA,MAAA;AACtB,MAAA,IAAA,YAAoB,CAAA,KAAA,EAAA;AACpB,QAAA,aAAe,KAAM,CAAA,MAAA,EAAA,CAAA;AACrB,QAAI,YAAU,CAAA,KAAiB,GAAA,KAAA,CAAA,CAAA;AAC7B,OAAI;AACJ,KAAA,CAAA;AACA,IAAA,MAAA,eAAc,GAAA,CAAA,SAAuB,EAAA,SAAA,KAAA;AACnC,MAAM,IAAA,EAAA,CAAA;AAEN,MAAO,IAAA,YAAA,CAAA,KAAA,IAAA,YAAA,CAAA,KAAA,EAAA;AAAA,QAAA,eACG,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,QAAA,YAAA,CAAA,KAAA,CAAA,YAAA,CAAA,OAAA,EAAA,WAAA,CAAA;AAAA,UAER,GAAA;AAE8C,UAChD,eAAA,EAAA,CAAA,KAAA,EAAA,SAAA,CAAA,EAAA,CAAA;AAAA,UACD,cAAA,EAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA;AACD,SAAA,CAAA,CAAA,CAAA;AACA,QAAA,CAAA,EAAA,GAAA,kBACY,KAAA,OAAY,KAAA,CAAA,MAAU,MAAC,CAAA,YAAkB,CAAA;AAC3B,QAC5B,UAAA,CAAA,MAAA;AACA,UAAA,eAA+B,CAAA,KAAA,GAAA,KAAA,CAAA;AAAuB,SACxD,CAAA,CAAA;AAEA,OAAA;AAEA,KAAA,CAAA;AACE,IAAM,MAAA,WAAS,GAAS,CAAA,GAAA,KAAA;AACxB,MAAM,IAAA,YAAa,GAAA,GAAA,CAAA;AACnB,MAAA,IAAA,aAAoB,GAAA,EAAA,CAAA;AACpB,MAAA,MAAM,aAAgB,CAAA,KAAA,CAAA;AACtB,MAAA,MAAM,UAAe,KAAA,CAAA,OAAA,CAAA;AAErB,MAAA,MAAS,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA;AACP,MAAI,MAAA,cAAc,CAAO,MAAA,CAAA;AACvB,MAAa,IAAA,CAAA,KAAA,IAAA,GAAA,CAAA,WAAiB,EAAA;AAAmB,QACnD,GAAA,CAAA,IAAA,GAAA,CAAA,EAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAAA,UAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAEA,QAAA,MAAM,QAAQ,GAAc,OAAA,CAAA,OAAA,CAAA,GAAA,OAAA,GAAA,CAAA,OAAA,CAAA,CAAA;AAC5B,QAAA,MAAM,KAAC,GAAA,QAAqB,CAAA,GAAA,CAAA,CAAA,IAAI;AAEhC,UAAM,MAAA,OAAA,GAAA,GAED,CAAA,WAAA,CAAA,IAAA,CAAA,CAAA;AACH,UAAM,OAAC;AAAwB,YAC7B,OAAe,CAAA,KAAA;AAAA,YACf,OAAA,CAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,OAAA,CAAA,qBAAA,GAAA,OAAA,CAAA,sBAAA,GAAA,OAAA,CAAA,uBAAA,GAAA,OAAA,CAAA,wBAAA;AAAA,WACA,CAAA;AAAA,SACA,CAAA,CAAA;AAAA,QACA,YAAA,GAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,QACA,aAAA,GAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,QAAA,CAAA,MAAA,GAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,IAAA,OAAA,CAAA;AAAA,OAAA;AACe,MAAA,OAAA,CACb,aAAmB,GAAA,KAAA,GAAA,YAAA,EAAA,MAAA,IAAA,IAAA,GAAA,MAAA,GAAA,aAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AACE,IAAA,MAAA,mBACE,EAAA,CAAA;AAAA,IAAA,MAAA,kBACA,MAAA;AAAA,MAAA,MAAA,iBACF,CAAA,aAAA,CAAA,QAAA,CAAA,CAAA;AAAA,MAAA,MAAA,uBACM,CAAA,IAAA,CAAA,CAAA;AAAA,MAC7B,MAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA;AAAA,MAAA,MACA,OAAK,GAAA,KAAA,CAAA,OAAA,CAAA;AAAA,MAAA,MACL,MAAK,GAAA,KAAA,CAAA,MAAA,CAAA;AAAA,MACP,IAAA,GAAA,EAAA;AAEA,QAAA,IAAA,CAAA,YAAA,CAAgB;AAAoB,UACtC,YAAA,CAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA;AAEA,SAAA;AACE,QAAM,MAAA,KAAA,GAAM,aAAU,EAAA,CAAA;AACtB,QAAA,MAAI,UAAe,EAAA,UAAA,CAAA,GAAA,WAAA,CAAA,GAAA,CAAA,CAAA;AACjB,QAAA,MAAA,UAAc,GAAA,CAAA,WAAA,KAAA;AAAA,UAChB,MAAA,CAAA,SAAA,EAAA,SAAA,CAAA,GAAA,QAAA,CAAA,WAAA,IAAA,EAAA,EAAA,MAAA,EAAA,KAAA,EAAA,SAAA,EAAA,UAAA,EAAA;AACA,YAAA,YAAc,CAAM,KAAA;AAClB,YAAA,QAAA,EAAA,QAAkB,CAAA,KAAA;AAAA,YACpB,SAAA,EAAA,SAAA,CAAA,KAAA;AACA,YAAA,UAAkB,EAAA,UAAA,CAAA,KAAA;AAClB,YAAA,UAAqB,EAAA,UAAA,CAAA,KAAA;AACrB,YAAA,SAAU,EAAA,SAAA,CAAA,KAAA;AAAA,YACL,YAAA,EAAA,YAAA,CAAA,KAAA;AACL,WAAA,EAAA,IAAA,CAAA,KAAkB,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,UACpB,eAAA,CAAA,SAAA,EAAA,SAAA,CAAA,CAAA;AAAA,SACF,CAAA;AAAA,QACF,IAAA,KAAA,EAAA;AAEA,UAAA,MAAgB,GAAA,GAAA,IAAA,KAAA,EAAA,CAAA;AACd,UAAgB,GAAA,CAAA,MAAA,GAAA,MAAA;AAAA,YACjB,UAAA,CAAA,GAAA,CAAA,CAAA;AAED,WAAA,CAAA;AAAA,UACQ,GAAA,CAAA,OAAA,GAAA,MAAA;AAAA,YACA,UAAA,CAAA,OAAA,CAAA,CAAA;AACJ,WAAgB,CAAA;AAAA,UAClB,GAAA,CAAA,WAAA,GAAA,WAAA,CAAA;AAAA,UACA,GAAA,CAAA,cAAA,GAAA,aAAA,CAAA;AAAA,UACQ,GAAA,CAAA,GAAA,GAAA,KAAA,CAAA;AAAA,SACC,MAAA;AAAA,UACT,UAAA,CAAA,OAAA,CAAA,CAAA;AAAA,SACF;AAEA,OAAA;AACE,KAAiB,CAAA;AAAA,IACnB,SAAC,CAAA,MAAA;AAED,MAAM,eAAW,EAAiC,CAAA;AAChD,KAAA,CAAA,CAAA;AACE,IAAA,KAAA,CAAA,MAAA,KAAA,EAAA,MAAA;AAAA,MACF,eAAA,EAAA,CAAA;AACA,KAAU,EAAA;AACR,MAAA,IAAA,EAAI,IAAY;AACd,MAAiB,KAAA,EAAA,MAAA;AACjB,KAAgB,CAAA,CAAA;AAAA,IAClB,eAAA,CAAA,MAAA;AAAA,MACF,gBAAC,EAAA,CAAA;AAAA,KACH,CAAA,CAAA;AAEA,IAAA,MAAA,QAAA,GAAA,CAAA;AAA4C,MAC1C,IAAY,eAAA,CAAA,KAAA,EAAA;AAAA,QACH,OAAA;AAAA,OACE;AAAA,MACZ,SAAA,CAAA,OAAA,CAAA,CAAA,QAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;"}