import { createRouter, createWebHistory } from 'vue-router'
import PeriodChangeStatsPage from '../pages/PeriodChangeStatsPage/index.vue'
import BatchPeriodChangeStatsPage from '../pages/BatchPeriodChangeStatsPage/index.vue'
import StdDevCrossBacktestPage from '../pages/StdDevCrossBacktestPage/index.vue'

// 路由配置
const routes = [
  {
    path: '/',
    name: 'home',
    component: () => import('../pages/HomePage.vue')
  },
  {
    path: '/kline',
    name: 'kline',
    component: () => import('../pages/KLinePage/index.vue')
  },
  {
    path: '/price-distribution',
    name: 'priceDistribution',
    component: () => import('../pages/PriceDistributionPage/index.vue')
  },
  {
    path: '/ma-analysis',
    name: 'maAnalysis',
    component: () => import('../pages/MAAnalysisPage/index.vue')
  },
  {
    path: '/investment-simulation',
    name: 'InvestmentSimulation',
    component: () => import('@/pages/InvestmentSimulationPage/index.vue'),
    meta: {
      title: '投资模拟分析'
    }
  },
  {
    path: '/batch-stock-analysis',
    name: 'BatchStockAnalysis',
    component: () => import('@/pages/BatchStockAnalysisPage/index.vue'),
    meta: {
      title: '批量股票分析'
    }
  },
  {
    path: '/period-change-stats',
    name: 'PeriodChangeStats',
    component: PeriodChangeStatsPage
  },
  {
    path: '/batch-period-change-stats',
    name: 'BatchPeriodChangeStats',
    component: BatchPeriodChangeStatsPage,
    meta: {
      title: '批量周期涨跌统计'
    }
  },
  {
    path: '/std-dev-cross-backtest',
    name: 'StdDevCrossBacktest',
    component: StdDevCrossBacktestPage,
    meta: {
      title: '标准差交叉回测'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('../pages/NotFound.vue')
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory('/'),
  routes
})

export default router 