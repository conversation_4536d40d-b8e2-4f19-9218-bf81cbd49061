{"version": 3, "file": "rate.mjs", "sources": ["../../../../../../packages/components/rate/src/rate.vue"], "sourcesContent": ["<template>\n  <div\n    :id=\"inputId\"\n    :class=\"[rateClasses, ns.is('disabled', rateDisabled)]\"\n    role=\"slider\"\n    :aria-label=\"!isLabeledByFormItem ? ariaLabel || 'rating' : undefined\"\n    :aria-labelledby=\"\n      isLabeledByFormItem ? formItemContext?.labelId : undefined\n    \"\n    :aria-valuenow=\"currentValue\"\n    :aria-valuetext=\"text || undefined\"\n    aria-valuemin=\"0\"\n    :aria-valuemax=\"max\"\n    tabindex=\"0\"\n    :style=\"rateStyles\"\n    @keydown=\"handleKey\"\n  >\n    <span\n      v-for=\"(item, key) in max\"\n      :key=\"key\"\n      :class=\"ns.e('item')\"\n      @mousemove=\"setCurrentValue(item, $event)\"\n      @mouseleave=\"resetCurrentValue\"\n      @click=\"selectValue(item)\"\n    >\n      <el-icon\n        :class=\"[\n          ns.e('icon'),\n          { hover: hoverIndex === item },\n          ns.is('active', item <= currentValue),\n        ]\"\n      >\n        <template v-if=\"!showDecimalIcon(item)\">\n          <component :is=\"activeComponent\" v-show=\"item <= currentValue\" />\n          <component :is=\"voidComponent\" v-show=\"!(item <= currentValue)\" />\n        </template>\n        <template v-if=\"showDecimalIcon(item)\">\n          <component :is=\"voidComponent\" :class=\"[ns.em('decimal', 'box')]\" />\n          <el-icon\n            :style=\"decimalStyle\"\n            :class=\"[ns.e('icon'), ns.e('decimal')]\"\n          >\n            <component :is=\"decimalIconComponent\" />\n          </el-icon>\n        </template>\n      </el-icon>\n    </span>\n    <span\n      v-if=\"showText || showScore\"\n      :class=\"ns.e('text')\"\n      :style=\"{ color: textColor }\"\n    >\n      {{ text }}\n    </span>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, markRaw, ref, watch } from 'vue'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { hasClass, isArray, isObject, isString } from '@element-plus/utils'\nimport {\n  formContextKey,\n  formItemContextKey,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useNamespace } from '@element-plus/hooks'\nimport { rateEmits, rateProps } from './rate'\nimport type { CSSProperties, Component } from 'vue'\n\nfunction getValueFromMap<T>(\n  value: number,\n  map: Record<string, T | { excluded?: boolean; value: T }>\n) {\n  const isExcludedObject = (\n    val: unknown\n  ): val is { excluded?: boolean } & Record<any, unknown> => isObject(val)\n\n  const matchedKeys = Object.keys(map)\n    .map((key) => +key)\n    .filter((key) => {\n      const val = map[key]\n      const excluded = isExcludedObject(val) ? val.excluded : false\n      return excluded ? value < key : value <= key\n    })\n    .sort((a, b) => a - b)\n  const matchedValue = map[matchedKeys[0]]\n  return (isExcludedObject(matchedValue) && matchedValue.value) || matchedValue\n}\n\ndefineOptions({\n  name: 'ElRate',\n})\n\nconst props = defineProps(rateProps)\nconst emit = defineEmits(rateEmits)\n\nconst formContext = inject(formContextKey, undefined)\nconst formItemContext = inject(formItemContextKey, undefined)\nconst rateSize = useFormSize()\nconst ns = useNamespace('rate')\nconst { inputId, isLabeledByFormItem } = useFormItemInputId(props, {\n  formItemContext,\n})\n\nconst currentValue = ref(props.modelValue)\nconst hoverIndex = ref(-1)\nconst pointerAtLeftHalf = ref(true)\n\nconst rateClasses = computed(() => [ns.b(), ns.m(rateSize.value)])\nconst rateDisabled = computed(() => props.disabled || formContext?.disabled)\nconst rateStyles = computed(() => {\n  return ns.cssVarBlock({\n    'void-color': props.voidColor,\n    'disabled-void-color': props.disabledVoidColor,\n    'fill-color': activeColor.value,\n  }) as CSSProperties\n})\n\nconst text = computed(() => {\n  let result = ''\n  if (props.showScore) {\n    result = props.scoreTemplate.replace(\n      /\\{\\s*value\\s*\\}/,\n      rateDisabled.value ? `${props.modelValue}` : `${currentValue.value}`\n    )\n  } else if (props.showText) {\n    result = props.texts[Math.ceil(currentValue.value) - 1]\n  }\n  return result\n})\nconst valueDecimal = computed(\n  () => props.modelValue * 100 - Math.floor(props.modelValue) * 100\n)\nconst colorMap = computed(() =>\n  isArray(props.colors)\n    ? {\n        [props.lowThreshold]: props.colors[0],\n        [props.highThreshold]: { value: props.colors[1], excluded: true },\n        [props.max]: props.colors[2],\n      }\n    : props.colors\n)\nconst activeColor = computed(() => {\n  const color = getValueFromMap(currentValue.value, colorMap.value)\n  // {value: '', excluded: true} returned\n  return isObject(color) ? '' : color\n})\nconst decimalStyle = computed(() => {\n  let width = ''\n  if (rateDisabled.value) {\n    width = `${valueDecimal.value}%`\n  } else if (props.allowHalf) {\n    width = '50%'\n  }\n  return {\n    color: activeColor.value,\n    width,\n  }\n})\nconst componentMap = computed(() => {\n  let icons = isArray(props.icons) ? [...props.icons] : { ...props.icons }\n  icons = markRaw(icons) as\n    | Array<string | Component>\n    | Record<number, string | Component>\n  return isArray(icons)\n    ? {\n        [props.lowThreshold]: icons[0],\n        [props.highThreshold]: {\n          value: icons[1],\n          excluded: true,\n        },\n        [props.max]: icons[2],\n      }\n    : icons\n})\nconst decimalIconComponent = computed(() =>\n  getValueFromMap(props.modelValue, componentMap.value)\n)\nconst voidComponent = computed(() =>\n  rateDisabled.value\n    ? isString(props.disabledVoidIcon)\n      ? props.disabledVoidIcon\n      : (markRaw(props.disabledVoidIcon) as Component)\n    : isString(props.voidIcon)\n    ? props.voidIcon\n    : (markRaw(props.voidIcon) as Component)\n)\nconst activeComponent = computed(() =>\n  getValueFromMap(currentValue.value, componentMap.value)\n)\n\nfunction showDecimalIcon(item: number) {\n  const showWhenDisabled =\n    rateDisabled.value &&\n    valueDecimal.value > 0 &&\n    item - 1 < props.modelValue &&\n    item > props.modelValue\n  const showWhenAllowHalf =\n    props.allowHalf &&\n    pointerAtLeftHalf.value &&\n    item - 0.5 <= currentValue.value &&\n    item > currentValue.value\n  return showWhenDisabled || showWhenAllowHalf\n}\n\nfunction emitValue(value: number) {\n  // if allow clear, and selected value is same as modelValue, reset value to 0\n  if (props.clearable && value === props.modelValue) {\n    value = 0\n  }\n\n  emit(UPDATE_MODEL_EVENT, value)\n  if (props.modelValue !== value) {\n    emit(CHANGE_EVENT, value)\n  }\n}\n\nfunction selectValue(value: number) {\n  if (rateDisabled.value) {\n    return\n  }\n  if (props.allowHalf && pointerAtLeftHalf.value) {\n    emitValue(currentValue.value)\n  } else {\n    emitValue(value)\n  }\n}\n\nfunction handleKey(e: KeyboardEvent) {\n  if (rateDisabled.value) {\n    return\n  }\n  let _currentValue = currentValue.value\n  const code = e.code\n  if (code === EVENT_CODE.up || code === EVENT_CODE.right) {\n    if (props.allowHalf) {\n      _currentValue += 0.5\n    } else {\n      _currentValue += 1\n    }\n    e.stopPropagation()\n    e.preventDefault()\n  } else if (code === EVENT_CODE.left || code === EVENT_CODE.down) {\n    if (props.allowHalf) {\n      _currentValue -= 0.5\n    } else {\n      _currentValue -= 1\n    }\n    e.stopPropagation()\n    e.preventDefault()\n  }\n  _currentValue = _currentValue < 0 ? 0 : _currentValue\n  _currentValue = _currentValue > props.max ? props.max : _currentValue\n  emit(UPDATE_MODEL_EVENT, _currentValue)\n  emit(CHANGE_EVENT, _currentValue)\n  return _currentValue\n}\n\nfunction setCurrentValue(value: number, event?: MouseEvent) {\n  if (rateDisabled.value) {\n    return\n  }\n  if (props.allowHalf && event) {\n    // TODO: use cache via computed https://github.com/element-plus/element-plus/pull/5456#discussion_r786472092\n    let target = event.target as HTMLElement\n    if (hasClass(target, ns.e('item'))) {\n      target = target.querySelector(`.${ns.e('icon')}`)!\n    }\n    if (target.clientWidth === 0 || hasClass(target, ns.e('decimal'))) {\n      target = target.parentNode as HTMLElement\n    }\n    pointerAtLeftHalf.value = event.offsetX * 2 <= target.clientWidth\n    currentValue.value = pointerAtLeftHalf.value ? value - 0.5 : value\n  } else {\n    currentValue.value = value\n  }\n  hoverIndex.value = value\n}\n\nfunction resetCurrentValue() {\n  if (rateDisabled.value) {\n    return\n  }\n  if (props.allowHalf) {\n    pointerAtLeftHalf.value = props.modelValue !== Math.floor(props.modelValue)\n  }\n  currentValue.value = props.modelValue\n  hoverIndex.value = -1\n}\n\nwatch(\n  () => props.modelValue,\n  (val) => {\n    currentValue.value = val\n    pointerAtLeftHalf.value = props.modelValue !== Math.floor(props.modelValue)\n  }\n)\n\nif (!props.modelValue) {\n  emit(UPDATE_MODEL_EVENT, 0)\n}\n\ndefineExpose({\n  /** @description set current value */\n  setCurrentValue,\n  /** @description reset current value */\n  resetCurrentValue,\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_unref", "_normalizeClass", "_normalizeStyle", "_Fragment", "_renderList"], "mappings": ";;;;;;;;;;;;;mCAgGc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAtBA,IAAS,SAAA,eAAA,CACP,OACA,GACA,EAAA;AACA,MAAA,MAAM,gBAAmB,GAAA,CACvB,GACyD,KAAA,QAAA,CAAS,GAAG,CAAA,CAAA;AAEvE,MAAA,MAAM,WAAc,GAAA,MAAA,CAAO,IAAK,CAAA,GAAG,CAChC,CAAA,GAAA,CAAI,CAAC,GAAA,KAAQ,CAAC,GAAG,CACjB,CAAA,MAAA,CAAO,CAAC,GAAQ,KAAA;AACf,QAAM,MAAA,GAAA,GAAM,IAAI,GAAG,CAAA,CAAA;AACnB,QAAA,MAAM,QAAW,GAAA,gBAAA,CAAiB,GAAG,CAAA,GAAI,IAAI,QAAW,GAAA,KAAA,CAAA;AACxD,QAAO,OAAA,QAAA,GAAW,KAAQ,GAAA,GAAA,GAAM,KAAS,IAAA,GAAA,CAAA;AAAA,OAC1C,CACA,CAAA,IAAA,CAAK,CAAC,CAAG,EAAA,CAAA,KAAM,IAAI,CAAC,CAAA,CAAA;AACvB,MAAA,MAAM,YAAe,GAAA,GAAA,CAAI,WAAY,CAAA,CAAC,CAAC,CAAA,CAAA;AACvC,MAAA,OAAQ,gBAAiB,CAAA,YAAY,CAAK,IAAA,YAAA,CAAa,KAAU,IAAA,YAAA,CAAA;AAAA,KACnE;AASA,IAAM,MAAA,WAAA,GAAc,MAAO,CAAA,cAAA,EAAgB,KAAS,CAAA,CAAA,CAAA;AACpD,IAAM,MAAA,eAAA,GAAkB,MAAO,CAAA,kBAAA,EAAoB,KAAS,CAAA,CAAA,CAAA;AAC5D,IAAA,MAAM,WAAW,WAAY,EAAA,CAAA;AAC7B,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAC9B,IAAA,MAAM,EAAE,OAAA,EAAS,mBAAoB,EAAA,GAAI,mBAAmB,KAAO,EAAA;AAAA,MACjE,eAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,GAAI,CAAA,KAAA,CAAM,UAAU,CAAA,CAAA;AACzC,IAAM,MAAA,UAAA,GAAa,IAAI,CAAE,CAAA,CAAA,CAAA;AACzB,IAAM,MAAA,iBAAA,GAAoB,IAAI,IAAI,CAAA,CAAA;AAElC,IAAA,MAAM,WAAc,GAAA,QAAA,CAAS,MAAM,CAAC,EAAG,CAAA,CAAA,EAAK,EAAA,EAAA,CAAG,CAAE,CAAA,QAAA,CAAS,KAAK,CAAC,CAAC,CAAA,CAAA;AACjE,IAAA,MAAM,eAAe,QAAS,CAAA,MAAM,KAAM,CAAA,QAAA,oBAAiC,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAC3E,IAAM,MAAA,UAAA,GAAa,SAAS,MAAM;AAChC,MAAA,OAAO,GAAG,WAAY,CAAA;AAAA,QACpB,cAAc,KAAM,CAAA,SAAA;AAAA,QACpB,uBAAuB,KAAM,CAAA,iBAAA;AAAA,QAC7B,cAAc,WAAY,CAAA,KAAA;AAAA,OAC3B,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAED,IAAM,MAAA,IAAA,GAAO,SAAS,MAAM;AAC1B,MAAA,IAAI,MAAS,GAAA,EAAA,CAAA;AACb,MAAA,IAAI,MAAM,SAAW,EAAA;AACnB,QAAA,MAAA,GAAS,MAAM,aAAc,CAAA,OAAA,CAAA,iBAAA,EAAA,YAAA,CAAA,KAAA,GAAA,CAAA,EAAA,KAAA,CAAA,UAAA,CAAA,CAAA,GAAA,CAAA,EAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OAC3B,MAAA,IAAA,KAAA,CAAA,QAAA,EAAA;AAAA,QACA,MAAA,GAAA,KAAA,CAAa,UAAW,CAAA,IAAA,CAAA,YAAqB,CAAA,KAAA,CAAG;AAAkB,OACpE;AAAA,MACF,OAAA;AACE,KAAA,CAAA,CAAA;AAAsD,IACxD,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,UAAA,GAAA,GAAA,GAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,UAAA,CAAA,GAAA,GAAA,CAAA,CAAA;AACA,IAAO,MAAA,QAAA,GAAA,QAAA,CAAA,MAAA,OAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA;AAAA,MACR,CAAA,KAAA,CAAA,YAAA,GAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACD,MAAA,CAAA,KAAqB,CAAA,aAAA,GAAA,EAAA,KAAA,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA;AAAA,MACnB,CAAA,KAAM,OAAmB,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAqC,KAChE,GAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA,IAAA,MAAM,WAAW,GAAA,QAAA,CAAA,MAAA;AAAA,MAAS,MACxB,KAAA,GAAQ,eACJ,CAAA,YAAA,CAAA,KAAA,EAAA,QAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MAAA,OACS,QAAA,CAAA,KAAY,CAAG,GAAA,EAAA,GAAM;AAAQ,KACpC,CAAA,CAAA;AAAgE,IAAA,MACzD,YAAM,GAAA,SAAa,MAAC;AAAA,MAC7B,IACA,KAAM,GAAA,EAAA,CAAA;AAAA,MACZ,IAAA,YAAA,CAAA,KAAA,EAAA;AACA,QAAM,KAAA,GAAA,CAAA,EAAA,kBAA6B,CAAA,CAAA,CAAA,CAAA;AACjC,OAAA,MAAc,IAAA,KAAA,CAAA,SAAA,EAAA;AAEd,QAAO,KAAA,GAAA,KAAA,CAAS;AAAc,OAC/B;AACD,MAAM,OAAA;AACJ,QAAA,KAAY,EAAA,WAAA,CAAA,KAAA;AACZ,QAAA;AACE,OAAQ,CAAA;AAAqB,KAC/B,CAAA,CAAA;AACE,IAAQ,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACV,IAAA,KAAA,GAAA,OAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,KAAA,CAAA,KAAA,CAAA,GAAA,EAAA,GAAA,KAAA,CAAA,KAAA,EAAA,CAAA;AACA,MAAO,KAAA,GAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MAAA,cACc,CAAA,KAAA,CAAA,GAAA;AAAA,QACnB,CAAA,KAAA,CAAA,YAAA,GAAA,KAAA,CAAA,CAAA,CAAA;AAAA,QACF,CAAA,KAAA,CAAA,aAAA,GAAA;AAAA,UACD,KAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AACD,UAAM,QAAA,EAAA,IAAA;AACJ,SAAA;AACA,QAAA,CAAA,KAAQ,YAAa,CAAA,CAAA,CAAA;AAGrB,OAAO,GAAA,KAAA,CAAA;AACH,KAAA,CAAA,CAAA;AAC+B,IAC7B,MAAO,oBAAgB,GAAA,QAAA,CAAA,MAAA,eAAA,CAAA,KAAA,CAAA,UAAA,EAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACrB,MAAA,aAAa,GAAC,QAAA,CAAA,MAAA,YAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA,CAAA,gBAAA,CAAA,GAAA,KAAA,CAAA,gBAAA,GAAA,OAAA,CAAA,KAAA,CAAA,gBAAA,CAAA,GAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,GAAA,KAAA,CAAA,QAAA,GAAA,OAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,IAAA,MACd,eAAU,GAAA,QAAA,CAAA,MAAA,eAAA,CAAA,YAAA,CAAA,KAAA,EAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACZ,SAAA,eAAA,CAAA,IAAA,EAAA;AAAA,MAAA,MACC,gBAAmB,GAAA,YAAA,CAAA,KAAA,IAAA,YAAA,CAAA,KAAA,GAAA,CAAA,IAAA,IAAA,GAAA,CAAA,GAAA,KAAA,CAAA,UAAA,IAAA,IAAA,GAAA,KAAA,CAAA,UAAA,CAAA;AAAA,MACtB,MACA,iBAAA,GAAA,KAAA,CAAA,SAAA,IAAA,iBAAA,CAAA,KAAA,IAAA,IAAA,GAAA,GAAA,IAAA,YAAA,CAAA,KAAA,IAAA,IAAA,GAAA,YAAA,CAAA,KAAA,CAAA;AAAA,MACL,OAAA,gBAAA,IAAA,iBAAA,CAAA;AACD,KAAA;AAA6B,IAAA,SAC3B,SAAA,CAAA,KAAgB,EAAM;AAA8B,MACtD,IAAA,KAAA,CAAA,SAAA,IAAA,KAAA,KAAA,KAAA,CAAA,UAAA,EAAA;AACA,QAAA,KAAsB,GAAA,CAAA,CAAA;AAAA,OAAS;AAOF,MAC7B,IAAA,CAAA,kBAAA,EAAA,KAAA,CAAA,CAAA;AACA,MAAA,IAAM,KAAkB,CAAA,UAAA,KAAA,KAAA,EAAA;AAAA,QACtB,IAAA,CAAA,YAAA,EAAA,KAA6B,CAAA,CAAA;AAAyB,OACxD;AAEA,KAAA;AACE,IAAM,SAAA,WAAA,CAAA,KAAA,EACS;AAIf,MAAM,IAAA,YAAA,CAAA,KAAA,EAAA;AAKN,QAAA,OAA2B;AAAA,OAC7B;AAEA,MAAA,IAAA,eAAkC,IAAA,iBAAA,CAAA,KAAA,EAAA;AAEhC,QAAA,SAAU,CAAA,YAAuB,CAAA,KAAA,CAAA,CAAA;AAC/B,OAAQ,MAAA;AAAA,QACV,SAAA,CAAA,KAAA,CAAA,CAAA;AAEA,OAAA;AACA,KAAI;AACF,IAAA,SAAK;AAAmB,MAC1B,IAAA,YAAA,CAAA,KAAA,EAAA;AAAA,QACF,OAAA;AAEA,OAAA;AACE,MAAA,IAAI,aAAa,GAAO,YAAA,CAAA,KAAA,CAAA;AACtB,MAAA,MAAA,IAAA,GAAA,CAAA,CAAA,IAAA,CAAA;AAAA,MACF,IAAA,IAAA,KAAA,UAAA,CAAA,EAAA,IAAA,IAAA,KAAA,UAAA,CAAA,KAAA,EAAA;AACA,QAAI,IAAA,KAAmB,CAAA,SAAA,EAAA;AACrB,UAAA,qBAAuB;AAAK,SACvB,MAAA;AACL,UAAA,aAAe,IAAA,CAAA,CAAA;AAAA,SACjB;AAAA,QACF,CAAA,CAAA,eAAA,EAAA,CAAA;AAEA,QAAA,CAAA,CAAA,cAAqC,EAAA,CAAA;AACnC,OAAA,mBAAwB,UAAA,CAAA,IAAA,IAAA,IAAA,KAAA,UAAA,CAAA,IAAA,EAAA;AACtB,QAAA,IAAA,KAAA,CAAA,SAAA,EAAA;AAAA,UACF,aAAA,IAAA,GAAA,CAAA;AACA,SAAA;AACA,UAAA,aAAe,IAAA,CAAA,CAAA;AACf,SAAA;AACE,QAAA,CAAA,CAAA,eAAqB,EAAA,CAAA;AACnB,QAAiB,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,OAAA;AAEjB,MAAiB,aAAA,GAAA,aAAA,GAAA,CAAA,GAAA,CAAA,GAAA,aAAA,CAAA;AAAA,MACnB,aAAA,GAAA,aAAA,GAAA,KAAA,CAAA,GAAA,GAAA,KAAA,CAAA,GAAA,GAAA,aAAA,CAAA;AACA,MAAA,IAAE,CAAgB,kBAAA,EAAA,aAAA,CAAA,CAAA;AAClB,MAAA,IAAE,CAAe,YAAA,EAAA,aAAA,CAAA,CAAA;AAAA,MACnB,oBAAoB,CAAA;AAClB,KAAA;AACE,IAAiB,SAAA,eAAA,CAAA,KAAA,EAAA,KAAA,EAAA;AAAA,MAAA,IACZ,YAAA,CAAA,KAAA,EAAA;AACL,QAAiB,OAAA;AAAA,OACnB;AACA,MAAA,IAAE,KAAgB,CAAA,SAAA,IAAA,KAAA,EAAA;AAClB,QAAA,IAAiB,MAAA,GAAA,KAAA,CAAA,MAAA,CAAA;AAAA,QACnB,IAAA,QAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA;AACA,UAAgB,MAAA,GAAA,MAAA,CAAA,gBAAoB,EAAI,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACxC,SAAA;AACA,QAAA,2BAAsC,CAAA,IAAA,QAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,EAAA;AACtC,UAAA,eAAmB,CAAa,UAAA,CAAA;AAChC,SAAO;AAAA,QACT,iBAAA,CAAA,KAAA,GAAA,KAAA,CAAA,OAAA,GAAA,CAAA,IAAA,MAAA,CAAA,WAAA,CAAA;AAEA,QAAS,YAAA,CAAA,KAAA,GAAgB,iBAAmC,CAAA,KAAA,GAAA,KAAA,GAAA,GAAA,GAAA,KAAA,CAAA;AAC1D,OAAA;AACE,QAAA,YAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,OACF;AACA,MAAI,UAAM,cAAoB,CAAA;AAE5B,KAAA;AACA,IAAA,0BAAqB,GAAK;AACxB,MAAA,IAAA,kBAA8B,EAAA;AAAkB,QAClD,OAAA;AACA,OAAI;AACF,MAAA,IAAA,KAAA,CAAA,SAAgB,EAAA;AAAA,QAClB,iBAAA,CAAA,KAAA,GAAA,KAAA,CAAA,UAAA,KAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AACA,OAAA;AACA,MAAA,YAAA,CAAA,KAAqB,GAAA,KAAA,CAAA,UAAA,CAAA;AAAwC,MAC/D,UAAO,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACL,KAAA;AAAqB,IACvB,KAAA,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,CAAA,GAAA,KAAA;AACA,MAAA,YAAmB,CAAA,KAAA,GAAA,GAAA,CAAA;AAAA,MACrB,iBAAA,CAAA,KAAA,GAAA,KAAA,CAAA,UAAA,KAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AACE,IAAA,IAAA,CAAA,gBAAwB,EAAA;AACtB,MAAA,IAAA,CAAA,kBAAA,EAAA,CAAA,CAAA,CAAA;AAAA,KACF;AACA,IAAA,MAAI;AACF,MAAA,eAAA;AAA0E,MAC5E,iBAAA;AACA,KAAA,CAAA,CAAA;AACA,IAAA,OAAA,CAAA,IAAA,EAAmB,MAAA,KAAA;AAAA,MACrB,IAAA,EAAA,CAAA;AAEA,MAAA,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,YACQC,KAAM,CAAA,OAAA,CAAA;AAAA,QACH,KAAA,EAAAC,cAAA,CAAA,CAAAD,KAAA,CAAA,WAAA,CAAA,EAAAA,KAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,UAAA,EAAAA,KAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA;AACP,QAAA,IAAA,EAAA,QAAqB;AACrB,QAAA,YAAA,EAAA,CAAAA,yBAAgC,CAAA,GAAA,IAAoB,CAAA,qBAAsB,GAAA,KAAA,CAAA;AAAA,QAC5E,iBAAA,EAAAA,KAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,EAAA,GAAAA,KAAA,CAAA,eAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,GAAA,KAAA,CAAA;AAAA,QACF,eAAA,EAAA,YAAA,CAAA,KAAA;AAEA,QAAI,gBAAmB,EAAAA,KAAA,CAAA,IAAA,CAAA,IAAA,KAAA,CAAA;AACrB,QAAA;AAA0B,QAC5B,eAAA,EAAA,IAAA,CAAA,GAAA;AAEA,QAAa,QAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAAE,cAAA,CAAAF,KAAA,CAAA,UAAA,CAAA,CAAA;AAAA,QAEX,SAAA,EAAA,SAAA;AAAA,OAAA,EAAA;AAAA,SAEAF,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAAAI,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,GAAA,KAAA;AAAA,UACD,OAAAN,SAAA,EAAA,EAAAC,kBAAA,CAAA,MAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}