<template>
  <div class="statistics-table-container">
    <el-card shadow="hover" class="statistics-card">
      <template #header>
        <div class="card-header">
          <span>模拟结果统计</span>
          <el-dropdown @command="handleSortCommand" trigger="click">
            <span class="el-dropdown-link">
              排序方式: {{ getSortLabel() }}
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{field: 'profitRate', ascending: false}">收益率 (降序)</el-dropdown-item>
                <el-dropdown-item :command="{field: 'profitRate', ascending: true}">收益率 (升序)</el-dropdown-item>
                <el-dropdown-item :command="{field: 'annualizedReturn', ascending: false}">年化收益率 (降序)</el-dropdown-item>
                <el-dropdown-item :command="{field: 'winRate', ascending: false}">胜率 (降序)</el-dropdown-item>
                <el-dropdown-item :command="{field: 'maxDrawdown', ascending: true}">最大回撤 (升序)</el-dropdown-item>
                <el-dropdown-item :command="{field: 'transactionCount', ascending: false}">交易次数 (降序)</el-dropdown-item>
                <el-dropdown-item :command="{field: 'period', ascending: true}">周期 (升序)</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
      
      <el-table
        :data="sortedData"
        stripe
        style="width: 100%"
        :row-class-name="tableRowClassName"
        @row-click="handleRowClick"
      >
        <el-table-column prop="period" label="周期" width="80" sortable>
          <template #default="scope">
            {{ scope.row.period }}天
          </template>
        </el-table-column>
        <el-table-column prop="profitRate" label="收益率" width="110" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.profitRate)">
              {{ scope.row.profitRate }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="annualizedReturn" label="年化收益" width="110" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.annualizedReturn)">
              {{ scope.row.annualizedReturn }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="winRate" label="胜率" width="110" sortable>
          <template #default="scope">
            {{ scope.row.winRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="maxDrawdown" label="最大回撤" width="110" sortable>
          <template #default="scope">
            <span class="negative-value">
              {{ scope.row.maxDrawdown }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="transactionCount" label="交易次数" width="110" sortable>
          <template #default="scope">
            {{ scope.row.transactionCount }}
          </template>
        </el-table-column>
        <el-table-column prop="avgHoldDays" label="平均持仓" sortable>
          <template #default="scope">
            {{ scope.row.avgHoldDays }}天
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'
import { sortPeriodResults, getPeriodStatistics } from '../utils/dataProcessor'

export default {
  name: 'StatisticsTable',
  components: {
    ArrowDown
  },
  props: {
    // 模拟结果数据
    simulationResult: {
      type: Object,
      default: () => ({})
    },
    // 选中的周期
    selectedPeriod: {
      type: [Number, String],
      default: null
    }
  },
  emits: ['update:selectedPeriod'],
  setup(props, { emit }) {
    // 排序字段
    const sortConfig = ref({
      field: 'profitRate',
      ascending: false
    })

    // 获取排序后的数据
    const sortedData = computed(() => {
      if (!props.simulationResult || !props.simulationResult.period_results) {
        return []
      }

      const results = sortPeriodResults(
        props.simulationResult, 
        sortConfig.value.field, 
        sortConfig.value.ascending
      )

      // 转换成表格需要的格式
      return results.map(item => {
        return {
          period: item.period,
          ...getPeriodStatistics(props.simulationResult, item.period)
        }
      })
    })

    // 处理排序命令
    const handleSortCommand = (command) => {
      sortConfig.value = {
        field: command.field,
        ascending: command.ascending
      }
    }

    // 获取排序标签
    const getSortLabel = () => {
      const { field, ascending } = sortConfig.value
      const direction = ascending ? '升序' : '降序'
      
      const fieldMap = {
        'profitRate': '收益率',
        'annualizedReturn': '年化收益率',
        'winRate': '胜率',
        'maxDrawdown': '最大回撤',
        'transactionCount': '交易次数',
        'period': '周期'
      }
      
      return `${fieldMap[field] || field} (${direction})`
    }

    // 处理行点击
    const handleRowClick = (row) => {
      emit('update:selectedPeriod', row.period)
    }

    // 设置表格行的类名
    const tableRowClassName = ({row}) => {
      if (props.selectedPeriod !== null && row.period === props.selectedPeriod) {
        return 'selected-row'
      }
      return ''
    }

    // 获取数值的样式类
    const getValueClass = (value) => {
      const numValue = parseFloat(value)
      if (numValue > 0) return 'positive-value'
      if (numValue < 0) return 'negative-value'
      return 'neutral-value'
    }

    // 监听模拟结果变化，如果结果变化且没有选中周期，则自动选择第一个
    watch(() => props.simulationResult, (newVal) => {
      if (newVal && props.selectedPeriod === null && sortedData.value.length > 0) {
        emit('update:selectedPeriod', sortedData.value[0].period)
      }
    }, { deep: true })

    return {
      sortedData,
      handleSortCommand,
      getSortLabel,
      handleRowClick,
      tableRowClassName,
      getValueClass
    }
  }
}
</script>

<style scoped>
.statistics-table-container {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
  display: flex;
  align-items: center;
}

.positive-value {
  color: #67C23A;
  font-weight: bold;
}

.negative-value {
  color: #F56C6C;
  font-weight: bold;
}

.neutral-value {
  color: #909399;
}

:deep(.selected-row) {
  background-color: #ecf5ff !important;
}

.statistics-card {
  margin-bottom: 20px;
}
</style> 