import request from '@/api/request'
import { getMALineColor } from '@/api/maAnalysis'

/**
 * 批量模拟多只股票的投资情况，通过循环调用单个股票的分析接口实现
 * @param {Object} params 请求参数
 * @param {Array} params.stock_codes 股票代码数组，如 ['000001', '000002']
 * @param {string} params.start_date 开始日期，格式为YYYY-MM-DD
 * @param {string} params.end_date 结束日期，格式为YYYY-MM-DD
 * @param {number} params.initial_capital 初始资金，默认100000
 * @param {number} params.min_period 最小均线周期，默认5
 * @param {number} params.max_period 最大均线周期，默认60
 * @param {number} params.step 均线周期步长，默认5
 * @returns {Promise<Array>} 包含所有股票模拟结果的Promise
 */
export async function batchSimulateInvestment(params) {
  console.log('批量分析参数:', params)
  
  // 确保股票代码是数组
  const stockCodes = Array.isArray(params.stock_codes) 
    ? params.stock_codes 
    : [params.stock_codes]
  
  try {
    // 创建一个结果数组
    let allResults = []
    
    // 显示当前进度的日志
    console.log(`开始分析 ${stockCodes.length} 只股票...`)
    
    // 依次处理每只股票
    for (let i = 0; i < stockCodes.length; i++) {
      const stockCode = stockCodes[i]
      console.log(`正在分析第 ${i+1}/${stockCodes.length} 只股票: ${stockCode}`)
      
      // 通知UI更新进度 - 开始分析
      if (window.stockAnalysisProgress && typeof window.stockAnalysisProgress.onStockAnalysisStart === 'function') {
        window.stockAnalysisProgress.onStockAnalysisStart(stockCode)
      }
      
      // 为每只股票构造请求参数
      const singleStockParams = {
        stock_code: stockCode,
        start_date: params.start_date,
        end_date: params.end_date,
        initial_capital: parseFloat(params.initial_capital),
        min_period: parseInt(params.min_period),
        max_period: parseInt(params.max_period),
        step: parseInt(params.step)
      }
      
      // 调用单只股票分析接口
      try {
        const result = await simulateSingleStock(singleStockParams)
        
        // 保存原始数据，用于交易信号分析
        if (window.stockAnalysisProgress && typeof window.stockAnalysisProgress.onStockDataReceived === 'function') {
          window.stockAnalysisProgress.onStockDataReceived(stockCode, result)
        }
        
        // 处理返回的结果
        if (result && result.period_results && Array.isArray(result.period_results)) {
          // 从period_results中提取每个周期的数据，并添加股票代码和名称
          const processedResults = result.period_results.map(periodResult => ({
            stock_code: stockCode,
            stock_name: result.stock_name || '未知',
            period: periodResult.period,
            profit_rate: periodResult.profit_rate || 0,
            annualized_return: periodResult.annualized_return || 0,
            max_drawdown: periodResult.max_drawdown || 0,
            transaction_count: periodResult.transaction_count || 0,
            win_rate: periodResult.win_rate || 0,
            average_holding_days: periodResult.average_holding_days || 0,
            // 计算额外字段
            profit_drawdown_ratio: periodResult.max_drawdown > 0 
              ? Math.abs(periodResult.profit_rate / periodResult.max_drawdown) 
              : 0,
            sharpe_ratio: calculateSharpeRatio(periodResult.annualized_return, periodResult.max_drawdown)
          }))
          
          // 将处理后的结果添加到总结果数组
          allResults = [...allResults, ...processedResults]
          
          // 更新股票名称（首次获取到）
          if (window.stockAnalysisProgress && typeof window.stockAnalysisProgress.onStockAnalysisStart === 'function') {
            window.stockAnalysisProgress.onStockAnalysisStart(stockCode, result.stock_name || '')
          }
        } else {
          console.warn(`股票 ${stockCode} 没有返回有效的分析结果`)
        }
        
        // 通知UI更新进度 - 完成分析
        if (window.stockAnalysisProgress && typeof window.stockAnalysisProgress.onStockAnalysisComplete === 'function') {
          window.stockAnalysisProgress.onStockAnalysisComplete()
        }
      } catch (error) {
        console.error(`股票 ${stockCode} 分析失败:`, error)
        // 通知UI更新进度 - 分析出错
        if (window.stockAnalysisProgress && typeof window.stockAnalysisProgress.onStockAnalysisError === 'function') {
          window.stockAnalysisProgress.onStockAnalysisError(stockCode, error)
        }
        // 继续分析下一只股票，不中断整个流程
      }
    }
    
    console.log(`分析完成，共处理 ${stockCodes.length} 只股票，得到 ${allResults.length} 条结果数据`)
    return allResults
    
  } catch (error) {
    console.error('批量分析过程中出错:', error)
    throw error
  }
}

/**
 * 计算夏普比率
 * @param {number} annualizedReturn 年化收益率
 * @param {number} maxDrawdown 最大回撤
 * @returns {number} 夏普比率
 */
function calculateSharpeRatio(annualizedReturn, maxDrawdown) {
  if (!annualizedReturn || !maxDrawdown || maxDrawdown <= 0) return 0
  
  // 使用简化的夏普比率计算，假设无风险利率为3%
  const riskFreeRate = 3
  const excessReturn = annualizedReturn - riskFreeRate
  
  // 使用最大回撤作为风险度量
  return excessReturn / maxDrawdown
}

/**
 * 获取单只股票的投资模拟分析数据
 * @param {Object} params 请求参数
 * @param {string} params.stock_code 股票代码
 * @param {string} params.start_date 开始日期，格式为YYYY-MM-DD
 * @param {string} params.end_date 结束日期，格式为YYYY-MM-DD
 * @param {number} params.initial_capital 初始资金，默认100000
 * @param {number} params.min_period 最小均线周期，默认5
 * @param {number} params.max_period 最大均线周期，默认60
 * @param {number} params.step 均线周期步长，默认5
 * @returns {Promise<Object>} 包含模拟结果的Promise
 */
export function simulateSingleStock(params) {
  // 确保参数格式正确
  const cleanParams = {
    stock_code: params.stock_code,
    start_date: params.start_date,
    end_date: params.end_date,
    initial_capital: parseFloat(params.initial_capital),
    min_period: parseInt(params.min_period),
    max_period: parseInt(params.max_period),
    step: parseInt(params.step)
  }
  
  console.log('发送单只股票投资模拟请求，参数:', cleanParams)
  
  // 发起请求
  return request({
    url: '/ma_analysis/simulate_investment',
    method: 'get',
    params: cleanParams,
    timeout: 60000 // 增加超时时间为60秒
  })
  .then(data => {
    console.log('单只股票模拟分析结果:', data)
    return data
  })
  .catch(error => {
    console.error('单只股票模拟请求失败:', error)
    throw error
  })
}

/**
 * 获取多只股票最佳均线周期的排名
 * @param {Object} params 请求参数
 * @param {Array} params.stock_codes 股票代码数组
 * @param {string} params.metric 排序指标，如 profit_rate, max_drawdown, win_rate等
 * @param {number} params.limit 返回数量限制，默认20
 * @returns {Promise<Array>} 包含排名结果的Promise
 */
export function getBestPerformingStocks(params) {
  // 构造请求参数
  const requestParams = {
    stock_codes: Array.isArray(params.stock_codes) 
      ? params.stock_codes.join(',') 
      : params.stock_codes,
    metric: params.metric || 'profit_rate',
    limit: params.limit || 20,
    order: params.order || 'desc' // 默认降序排列
  }
  
  return request({
    url: '/ma_analysis/best_performing_stocks',
    method: 'get',
    params: requestParams
  })
  .then(response => {
    console.log('最佳表现股票结果:', response)
    return response || []
  })
  .catch(error => {
    console.error('获取最佳表现股票失败:', error)
    return []
  })
} 