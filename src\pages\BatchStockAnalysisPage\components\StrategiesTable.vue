<template>
  <div class="strategies-table">
    <div class="table-header">
      <h3>各周期详细表现</h3>
    </div>
    
    <!-- 搜索过滤区域 -->
    <div class="filter-container">
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="搜索股票代码或名称"
          clearable
          prefix-icon="Search"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <div class="search-stats" v-if="searchQuery">
          找到 {{ filteredResults.length }} 条结果
        </div>
      </div>
      
      <div class="period-filter">
        <el-select v-model="selectedPeriod" placeholder="选择周期" clearable>
          <el-option
            v-for="period in periodOptions"
            :key="period"
            :label="`MA${period}`"
            :value="period"
          />
        </el-select>
      </div>
    </div>
    
    <!-- 详细结果表格 -->
    <el-table
      :data="filteredResults"
      border
      style="width: 100%"
      height="550"
      :default-sort="{ prop: 'profit_rate', order: 'descending' }"
    >
      <!-- 股票基本信息 -->
      <el-table-column label="股票信息" fixed="left" width="140">
        <template #default="scope">
          <div class="stock-info">
            <span class="stock-code">{{ scope.row.stock_code }}</span>
            <span class="stock-name">{{ scope.row.stock_name }}</span>
            <el-tag size="small" class="period-tag">MA{{ scope.row.period }}</el-tag>
          </div>
        </template>
      </el-table-column>
      
      <!-- 策略表现 -->
      <el-table-column label="收益指标" align="center">
        <el-table-column prop="profit_rate" label="收益率(%)" width="100" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.profit_rate)">
              {{ formatNumber(scope.row.profit_rate) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="annual_return" label="年化收益率(%)" width="120" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.annual_return)">
              {{ formatNumber(scope.row.annual_return) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="max_drawdown" label="最大回撤(%)" width="120" sortable>
          <template #default="scope">
            <span class="negative-value">
              {{ formatNumber(scope.row.max_drawdown) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="profit_drawdown_ratio" label="收益回撤比" width="120" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.profit_drawdown_ratio)">
              {{ formatNumber(scope.row.profit_drawdown_ratio) }}
            </span>
          </template>
        </el-table-column>
      </el-table-column>
      
      <!-- 交易数据 -->
      <el-table-column label="交易数据" align="center">
        <el-table-column prop="transaction_count" label="交易次数" width="100" sortable />
        <el-table-column prop="win_rate" label="胜率(%)" width="100" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.win_rate)">
              {{ formatNumber(scope.row.win_rate) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="profit_factor" label="利润因子" width="100" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.profit_factor)">
              {{ formatNumber(scope.row.profit_factor) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="average_profit" label="平均收益(%)" width="120" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.average_profit)">
              {{ formatNumber(scope.row.average_profit) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="average_loss" label="平均亏损(%)" width="120" sortable>
          <template #default="scope">
            <span class="negative-value">
              {{ formatNumber(scope.row.average_loss) }}
            </span>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Search } from '@element-plus/icons-vue'

const props = defineProps({
  results: {
    type: Array,
    default: () => []
  }
})

// 搜索筛选
const searchQuery = ref('')
const selectedPeriod = ref('')

// 获取所有可用的周期选项
const periodOptions = computed(() => {
  const periods = new Set()
  props.results.forEach(result => {
    if (result.period) {
      periods.add(result.period)
    }
  })
  return Array.from(periods).sort((a, b) => a - b)
})

// 过滤后的结果
const filteredResults = computed(() => {
  let filtered = [...props.results]
  
  // 按股票代码或名称搜索
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(item => 
      (item.stock_code && item.stock_code.toLowerCase().includes(query)) ||
      (item.stock_name && item.stock_name.toLowerCase().includes(query))
    )
  }
  
  // 按周期筛选
  if (selectedPeriod.value) {
    filtered = filtered.filter(item => item.period === selectedPeriod.value)
  }
  
  return filtered
})

// 格式化数字
const formatNumber = (value) => {
  if (value === undefined || value === null) return '-'
  return value.toFixed(2)
}

// 数值显示样式
const getValueClass = (value) => {
  if (value === undefined || value === null) return ''
  return value > 0 ? 'positive-value' : value < 0 ? 'negative-value' : ''
}
</script>

<style scoped>
.strategies-table {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
}

.filter-container {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.search-box {
  flex-grow: 1;
}

.search-stats {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

.stock-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stock-code {
  font-weight: bold;
}

.stock-name {
  font-size: 0.8em;
  color: #888;
}

.period-tag {
  margin-top: 3px;
  align-self: flex-start;
}

.positive-value {
  color: #f56c6c;
  font-weight: bold;
}

.negative-value {
  color: #67c23a;
  font-weight: bold;
}
</style> 