<template>
  <div class="batch-analysis-container">
    <h1 class="page-title">批量股票分析</h1>
    
    <!-- 参数设置表单 -->
    <AnalysisParameterForm 
      :loading="loading" 
      @start-analysis="handleStartAnalysis" 
      @reset="resetAnalysis"
    />
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-card shadow="hover">
        <template #header>
          <div class="progress-header">
            <span>分析进度</span>
            <el-tag type="info" v-if="progress.total > 0">
              {{ progress.current }}/{{ progress.total }} 只股票
            </el-tag>
          </div>
        </template>
        <div class="progress-content">
          <el-progress 
            :percentage="progressPercentage" 
            :format="progressFormat"
            :status="progress.error ? 'exception' : ''"
            stripe
          />
          <div class="current-stock" v-if="progress.currentStock">
            <span>当前分析: {{ progress.currentStock }}</span>
            <span v-if="progress.currentStockName" class="stock-name">({{ progress.currentStockName }})</span>
          </div>
          <el-skeleton :rows="5" animated v-if="loading" />
        </div>
      </el-card>
    </div>
    
    <!-- 分析结果 -->
    <div v-if="analysisResults.length > 0 && !loading" class="results-container">
      <AnalysisResultsSummary 
        :results="analysisResults" 
        :stockCodes="stockCodes"
        :periodRange="periodRange"
        :rawResults="rawAnalysisData"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import AnalysisParameterForm from './components/AnalysisParameterForm.vue'
import AnalysisResultsSummary from './components/AnalysisResultsSummary.vue'
import { batchSimulateInvestment } from './api/batchAnalysis'

// 状态变量
const loading = ref(false)
const stockCodes = ref([])
const analysisResults = ref([])
const rawAnalysisData = ref({}) // 保存原始分析数据，包含daily_data
const periodRange = ref({
  min: 2,
  max: 20,
  step: 2
})

// 进度状态
const progress = reactive({
  current: 0,
  total: 0,
  currentStock: '',
  currentStockName: '',
  error: false
})

// 计算进度百分比
const progressPercentage = computed(() => {
  if (progress.total === 0) return 0
  return Math.floor((progress.current / progress.total) * 100)
})

// 进度格式化
const progressFormat = (percentage) => {
  return progress.total > 0 ? `${percentage}%` : '准备中...'
}

// 开始分析
const handleStartAnalysis = async (formData) => {
  // 保存表单数据
  stockCodes.value = formData.stockCodes
  periodRange.value = {
    min: formData.minPeriod,
    max: formData.maxPeriod,
    step: formData.step
  }
  
  // 参数验证
  if (stockCodes.value.length === 0) {
    ElMessage.warning('请至少输入一个股票代码')
    return
  }
  
  // 重置进度状态
  progress.current = 0
  progress.total = stockCodes.value.length
  progress.currentStock = ''
  progress.currentStockName = ''
  progress.error = false
  
  // 重置结果数据
  analysisResults.value = []
  rawAnalysisData.value = {}
  
  loading.value = true
  try {
    const params = {
      stock_codes: stockCodes.value,
      start_date: formData.startDate,
      end_date: formData.endDate,
      initial_capital: formData.initialCapital,
      min_period: formData.minPeriod,
      max_period: formData.maxPeriod,
      step: formData.step
    }
    
    // 设置进度监听
    window.stockAnalysisProgress = {
      onStockAnalysisStart: (stockCode, stockName) => {
        progress.currentStock = stockCode
        progress.currentStockName = stockName || ''
      },
      onStockAnalysisComplete: () => {
        progress.current++
      },
      onStockAnalysisError: () => {
        progress.error = true
        progress.current++
      },
      // 添加新的回调处理原始数据
      onStockDataReceived: (stockCode, data) => {
        if (stockCode && data) {
          rawAnalysisData.value[stockCode] = data
        }
      }
    }
    
    // 发送批量分析请求
    const results = await batchSimulateInvestment(params)
    analysisResults.value = results
    
    if (results.length === 0) {
      ElMessage.warning('未获取到分析结果，请检查参数后重试')
    } else {
      ElMessage.success(`成功分析 ${results.length} 条股票-周期组合数据`)
    }
  } catch (error) {
    console.error('批量分析失败:', error)
    ElMessage.error('分析过程中出现错误: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
    
    // 清理进度监听
    window.stockAnalysisProgress = null
  }
}

// 重置分析
const resetAnalysis = () => {
  analysisResults.value = []
  stockCodes.value = []
  rawAnalysisData.value = {}
  
  // 重置进度状态
  progress.current = 0
  progress.total = 0
  progress.currentStock = ''
  progress.currentStockName = ''
  progress.error = false
}
</script>

<style scoped>
.batch-analysis-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-title {
  margin-bottom: 20px;
  color: #303133;
  text-align: center;
}

.loading-container {
  margin-top: 30px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.results-container {
  margin-top: 30px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-content {
  padding: 10px 0;
}

.current-stock {
  margin: 10px 0 20px;
  font-size: 14px;
  color: #606266;
}

.current-stock .stock-name {
  margin-left: 5px;
  color: #909399;
}
</style> 