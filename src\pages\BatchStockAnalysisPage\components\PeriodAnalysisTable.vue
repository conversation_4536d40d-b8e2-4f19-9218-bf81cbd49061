<template>
  <div class="period-analysis-table">
    <div class="table-header">
      <h3>均线周期表现分析</h3>
      <div class="table-actions">
        <el-select v-model="selectedMetric" placeholder="选择排序指标" style="width: 150px">
          <el-option
            v-for="item in sortMetrics"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button type="primary" plain size="small" @click="handleExport">
          导出数据
        </el-button>
      </div>
    </div>
    
    <!-- 周期汇总表 -->
    <el-table
      :data="sortedPeriodResults"
      border
      style="width: 100%"
      height="500"
      :default-sort="{ prop: selectedMetric, order: 'descending' }"
    >
      <el-table-column prop="period" label="均线周期" width="100" fixed="left">
        <template #default="scope">
          <el-tag type="info">MA{{ scope.row.period }}</el-tag>
        </template>
      </el-table-column>
      
      <!-- 收益分组 -->
      <el-table-column label="收益表现" align="center">
        <el-table-column prop="averageProfitRate" label="平均收益率(%)" width="130" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.averageProfitRate)">
              {{ formatNumber(scope.row.averageProfitRate) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="bestStock" label="最佳表现股票" width="150" sortable>
          <template #default="scope">
            <div>
              <span>{{ scope.row.bestStock }}</span>
              <div class="stock-profit">{{ formatNumber(scope.row.bestStockProfit) }}%</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="worstStock" label="最差表现股票" width="150" sortable>
          <template #default="scope">
            <div>
              <span>{{ scope.row.worstStock }}</span>
              <div class="stock-profit">{{ formatNumber(scope.row.worstStockProfit) }}%</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="profitableStockRatio" label="盈利股票占比(%)" width="150" sortable>
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.profitableStockRatio" 
              :color="getProgressColor(scope.row.profitableStockRatio)"
            />
          </template>
        </el-table-column>
      </el-table-column>
      
      <!-- 风险分组 -->
      <el-table-column label="风险指标" align="center">
        <el-table-column prop="averageMaxDrawdown" label="平均最大回撤(%)" width="140" sortable>
          <template #default="scope">
            <span class="negative-value">
              {{ formatNumber(scope.row.averageMaxDrawdown) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="profitDrawdownRatio" label="收益回撤比" width="120" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.profitDrawdownRatio)">
              {{ formatNumber(scope.row.profitDrawdownRatio) }}
            </span>
          </template>
        </el-table-column>
      </el-table-column>
      
      <!-- 交易数据分组 -->
      <el-table-column label="交易数据" align="center">
        <el-table-column prop="averageTransactionCount" label="平均交易次数" width="120" sortable />
        <el-table-column prop="averageWinRate" label="平均胜率(%)" width="120" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.averageWinRate)">
              {{ formatNumber(scope.row.averageWinRate) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="averageHoldingDays" label="平均持仓天数" width="120" sortable />
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  results: {
    type: Array,
    default: () => []
  },
  periodRange: {
    type: Object,
    default: () => ({
      min: 2,
      max: 20,
      step: 2
    })
  }
})

// 排序指标
const sortMetrics = [
  { label: '平均收益率', value: 'averageProfitRate' },
  { label: '盈利股票占比', value: 'profitableStockRatio' },
  { label: '收益回撤比', value: 'profitDrawdownRatio' },
  { label: '平均最大回撤', value: 'averageMaxDrawdown' },
  { label: '平均胜率', value: 'averageWinRate' }
]

const selectedMetric = ref('averageProfitRate')

// 处理周期汇总数据
const periodResults = computed(() => {
  if (!props.results.length) return []
  
  // 存储每个周期的汇总数据
  const periodSummary = {}
  
  // 生成所有周期
  for (let period = props.periodRange.min; period <= props.periodRange.max; period += props.periodRange.step) {
    periodSummary[period] = {
      period,
      stockResults: [], // 存储每只股票在该周期的结果
      profitRates: [],
      maxDrawdowns: [],
      transactionCounts: [],
      winRates: [],
      holdingDays: []
    }
  }
  
  // 遍历所有结果，按周期归类
  props.results.forEach(result => {
    const period = result.period
    const stockCode = result.stock_code
    
    if (periodSummary[period]) {
      // 收集该周期内每只股票的表现
      periodSummary[period].stockResults.push({
        stockCode,
        stockName: result.stock_name || '未知',
        profitRate: result.profit_rate || 0,
        maxDrawdown: result.max_drawdown || 0,
        transactionCount: result.transaction_count || 0,
        winRate: result.win_rate || 0,
        avgHoldingDays: result.average_holding_days || 0
      })
      
      periodSummary[period].profitRates.push(result.profit_rate || 0)
      periodSummary[period].maxDrawdowns.push(result.max_drawdown || 0)
      periodSummary[period].transactionCounts.push(result.transaction_count || 0)
      periodSummary[period].winRates.push(result.win_rate || 0)
      periodSummary[period].holdingDays.push(result.average_holding_days || 0)
    }
  })
  
  // 计算每个周期的统计数据
  return Object.values(periodSummary)
    .filter(period => period.stockResults.length > 0) // 仅保留有结果的周期
    .map(period => {
      // 计算平均值
      const averageProfitRate = period.profitRates.reduce((sum, rate) => sum + rate, 0) / period.profitRates.length
      const averageMaxDrawdown = period.maxDrawdowns.reduce((sum, dd) => sum + dd, 0) / period.maxDrawdowns.length
      const averageTransactionCount = period.transactionCounts.reduce((sum, count) => sum + count, 0) / period.transactionCounts.length
      const averageWinRate = period.winRates.reduce((sum, rate) => sum + rate, 0) / period.winRates.length
      const averageHoldingDays = period.holdingDays.reduce((sum, days) => sum + days, 0) / period.holdingDays.length
      
      // 计算收益回撤比
      const profitDrawdownRatio = averageMaxDrawdown !== 0 ? Math.abs(averageProfitRate / averageMaxDrawdown) : 0
      
      // 找出最佳表现股票和最差表现股票
      const bestStockInfo = period.stockResults.reduce((best, current) => 
        current.profitRate > best.profitRate ? current : best, { profitRate: -Infinity })
      
      const worstStockInfo = period.stockResults.reduce((worst, current) => 
        current.profitRate < worst.profitRate ? current : worst, { profitRate: Infinity })
      
      // 计算盈利股票占比
      const profitableStockCount = period.stockResults.filter(stock => stock.profitRate > 0).length
      const profitableStockRatio = (profitableStockCount / period.stockResults.length) * 100
      
      return {
        period: period.period,
        averageProfitRate,
        bestStock: `${bestStockInfo.stockCode}`,
        bestStockProfit: bestStockInfo.profitRate,
        worstStock: `${worstStockInfo.stockCode}`,
        worstStockProfit: worstStockInfo.profitRate,
        profitableStockRatio,
        averageMaxDrawdown,
        profitDrawdownRatio,
        averageTransactionCount,
        averageWinRate,
        averageHoldingDays
      }
    })
})

// 排序后的结果
const sortedPeriodResults = computed(() => {
  const results = [...periodResults.value]
  
  if (selectedMetric.value === 'averageMaxDrawdown') {
    // 对于回撤，值越小越好
    return results.sort((a, b) => a[selectedMetric.value] - b[selectedMetric.value])
  } else {
    // 对于其他指标，值越大越好
    return results.sort((a, b) => b[selectedMetric.value] - a[selectedMetric.value])
  }
})

// 格式化数字
const formatNumber = (value) => {
  if (value === undefined || value === null) return '-'
  return value.toFixed(2)
}

// 获取数值显示的CSS类
const getValueClass = (value) => {
  if (value === undefined || value === null) return ''
  return value > 0 ? 'positive-value' : value < 0 ? 'negative-value' : ''
}

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage < 40) return '#F56C6C' // 红色
  if (percentage < 60) return '#E6A23C' // 黄色
  if (percentage < 80) return '#409EFF' // 蓝色
  return '#67C23A' // 绿色
}

// 导出数据
const handleExport = () => {
  try {
    // 创建CSV内容
    let csvContent = "均线周期,平均收益率(%),最佳表现股票,最佳股票收益率(%),最差表现股票,最差股票收益率(%),盈利股票占比(%),平均最大回撤(%),收益回撤比,平均交易次数,平均胜率(%),平均持仓天数\n"
    
    // 添加每行数据
    periodResults.value.forEach(period => {
      csvContent += `MA${period.period},${formatNumber(period.averageProfitRate)},${period.bestStock},${formatNumber(period.bestStockProfit)},${period.worstStock},${formatNumber(period.worstStockProfit)},${formatNumber(period.profitableStockRatio)},${formatNumber(period.averageMaxDrawdown)},${formatNumber(period.profitDrawdownRatio)},${formatNumber(period.averageTransactionCount)},${formatNumber(period.averageWinRate)},${formatNumber(period.averageHoldingDays)}\n`
    })
    
    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    
    // 创建下载链接
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `均线周期分析_${new Date().toISOString().split('T')[0]}.csv`)
    
    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + error.message)
  }
}
</script>

<style scoped>
.period-analysis-table {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.stock-profit {
  font-size: 0.85em;
  margin-top: 3px;
}

.positive-value {
  color: #f56c6c;
  font-weight: bold;
}

.negative-value {
  color: #67c23a;
  font-weight: bold;
}
</style> 