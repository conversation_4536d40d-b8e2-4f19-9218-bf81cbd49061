import request from './request'
import { ElMessage } from 'element-plus'

/**
 * 获取价格区间涨跌幅分布数据
 * @param {Object} params 请求参数
 * @param {string} params.stock_code 股票代码
 * @param {string} params.start_date 开始日期，格式为YYYY-MM-DD
 * @param {string} params.end_date 结束日期，格式为YYYY-MM-DD
 * @param {number} [params.price_interval=10.0] 价格区间间隔，单位为元
 * @param {number} [params.change_pct_interval=1.0] 涨跌幅区间间隔，单位为百分比
 * @returns {Promise<Object>} 价格区间涨跌幅分布数据
 */
export function getPriceChangePctDistribution(params) {
  return request({
    url: '/price_analysis/price_change_pct_distribution',
    method: 'get',
    params
  })
}

/**
 * 转换价格区间涨跌幅分布数据为图表所需格式
 * @param {Object} data 原始价格区间涨跌幅分布数据
 * @returns {Object} 格式化后的柱状图数据
 */
export function formatDistributionData(data) {
  // 初始结果对象，无论发生什么情况都会返回这个结构
  const defaultResult = {
    priceRanges: [],
    changePctRanges: [],
    series: [],
    statistics: [],
    rawData: null // 添加原始数据，用于详细展示
  };
  
  // 基础数据验证
  if (!data) {
    console.error('数据为空');
    ElMessage.warning('数据格式错误：数据为空');
    return defaultResult;
  }
  
  console.log('后端返回的原始数据:', data);
  
  try {
    // 保存原始数据，用于详细展示
    defaultResult.rawData = data;
    
    // 检查是否有新格式的price_ranges数据
    if (data.price_ranges && Object.keys(data.price_ranges).length > 0) {
      // 新格式数据处理 - 有多个价格区间
      console.log('检测到新格式数据，包含price_ranges');
      
      // 获取所有价格区间
      const priceRanges = Object.keys(data.price_ranges);
      defaultResult.priceRanges = priceRanges;
      
      // 获取所有涨跌幅区间（从第一个价格区间中提取）
      const firstPriceRangeKey = priceRanges[0];
      const firstPriceRange = data.price_ranges[firstPriceRangeKey];
      
      if (firstPriceRange && firstPriceRange.distributions) {
        const changePctRanges = Object.keys(firstPriceRange.distributions).sort((a, b) => {
          try {
            const [aMin] = a.split('-').map(parseFloat);
            const [bMin] = b.split('-').map(parseFloat);
            return aMin - bMin;
          } catch (error) {
            console.error('涨跌幅区间排序错误:', error);
            return 0;
          }
        });
        
        defaultResult.changePctRanges = changePctRanges;
        
        // 为每个价格区间构建系列数据
        const series = [];
        const statistics = [];
        
        for (const priceRange of priceRanges) {
          const priceRangeData = data.price_ranges[priceRange];
          
          if (!priceRangeData || !priceRangeData.distributions) {
            console.warn(`价格区间 ${priceRange} 没有有效的分布数据`);
            continue;
          }
          
          // 构建系列数据
          series.push({
            name: `${priceRange}价格`,
            type: 'bar',
            emphasis: {
              focus: 'series'
            },
            data: changePctRanges.map(range => priceRangeData.distributions[range] || 0)
          });
          
          // 计算上涨和下跌的天数
          let upDays = 0;
          let downDays = 0;
          
          for (const range of changePctRanges) {
            const count = priceRangeData.distributions[range] || 0;
            const [min] = range.split('-').map(parseFloat);
            
            if (min >= 0) {
              upDays += count;
            } else {
              downDays += count;
            }
          }
          
          // 构建统计信息
          const minPrice = priceRangeData.price_range_info?.min_price?.toFixed(2) || '0.00';
          const maxPrice = priceRangeData.price_range_info?.max_price?.toFixed(2) || '0.00';
          const avgPrice = priceRangeData.price_range_info?.avg_price?.toFixed(2) || '0.00';
          const dataCount = priceRangeData.price_range_info?.data_count || 0;
          const totalDays = priceRangeData.total_days || 0;
          const minChangePct = priceRangeData.min_change_pct?.toFixed(2) || '0.00';
          const maxChangePct = priceRangeData.max_change_pct?.toFixed(2) || '0.00';
          const avgChangePct = priceRangeData.avg_change_pct?.toFixed(2) || '0.00';
          
          statistics.push({
            priceRange,
            minPrice,
            maxPrice,
            avgPrice,
            dataCount,
            upDays,
            downDays,
            upDownRatio: totalDays > 0 ? (upDays / totalDays * 100).toFixed(2) + '%' : '0.00%',
            avgChangePct,
            maxChangePct,
            minChangePct,
            totalDays
          });
        }
        
        defaultResult.series = series;
        defaultResult.statistics = statistics;
        
        console.log('格式化后的完整数据:', defaultResult);
        return defaultResult;
      }
    }
    
    // 旧格式数据处理 - 只有一个全部价格区间
    console.log('使用旧格式数据处理');
    
    // 检查是否有有效的分布数据
    if (!data.distribution || Object.keys(data.distribution).length === 0) {
      console.error('无效的分布数据格式:', data);
      ElMessage.warning('数据格式错误：缺少分布数据');
      return defaultResult;
    }
    
    // 对于没有价格区间的数据，我们创建一个默认的价格区间
    const defaultPriceRange = '全部';
    
    // 获取并排序所有涨跌幅区间
    const changePctRanges = Object.keys(data.distribution).sort((a, b) => {
      try {
        const [aMin] = a.split('-').map(parseFloat);
        const [bMin] = b.split('-').map(parseFloat);
        return aMin - bMin;
      } catch (error) {
        console.error('涨跌幅区间排序错误:', error);
        return 0; // 如果排序出错，保持原始顺序
      }
    });
    
    console.log('排序后的涨跌幅区间:', changePctRanges);
    
    if (changePctRanges.length === 0) {
      console.warn('没有有效的涨跌幅区间');
      ElMessage.warning('数据异常：没有有效的涨跌幅区间');
      return defaultResult;
    }
    
    // 计算上涨和下跌的天数
    let upDays = 0;
    let downDays = 0;
    
    for (const range of changePctRanges) {
      const count = data.distribution[range];
      const [min] = range.split('-').map(parseFloat);
      
      if (min >= 0) {
        upDays += count;
      } else {
        downDays += count;
      }
    }
    
    // 构建系列数据，只有一个系列（全部价格）
    const series = [{
      name: `${defaultPriceRange}价格`,
      type: 'bar',
      emphasis: {
        focus: 'series'
      },
      data: changePctRanges.map(range => data.distribution[range] || 0) // 使用0处理可能的undefined值
    }];
    
    console.log('图表系列数据:', series);
    
    // 检查并解析统计数据，提供默认值以防数据缺失
    const avgChangePct = typeof data.avg_change_pct === 'number' ? data.avg_change_pct.toFixed(2) : '0.00';
    const maxChangePct = typeof data.max_change_pct === 'number' ? data.max_change_pct.toFixed(2) : '0.00';
    const minChangePct = typeof data.min_change_pct === 'number' ? data.min_change_pct.toFixed(2) : '0.00';
    const totalDays = typeof data.total_days === 'number' ? data.total_days : 0;
    
    // 构建统计信息
    const statistics = [{
      priceRange: defaultPriceRange,
      avgPrice: '不适用', // 这里没有具体价格区间的平均价格
      dataCount: totalDays,
      upDays,
      downDays,
      upDownRatio: totalDays > 0 ? (upDays / totalDays * 100).toFixed(2) + '%' : '0.00%',
      avgChangePct,
      maxChangePct,
      minChangePct,
      totalDays
    }];
    
    console.log('统计信息:', statistics);
    
    defaultResult.priceRanges = [defaultPriceRange];
    defaultResult.changePctRanges = changePctRanges;
    defaultResult.series = series;
    defaultResult.statistics = statistics;
    
    console.log('格式化后的完整数据:', defaultResult);
    
    return defaultResult;
  } catch (error) {
    console.error('格式化分布数据时发生错误:', error);
    ElMessage.error('处理数据时发生错误');
    return defaultResult;
  }
} 