{"version": 3, "file": "popconfirm2.js", "sources": ["../../../../../../packages/components/popconfirm/src/popconfirm.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"tooltipRef\"\n    trigger=\"click\"\n    effect=\"light\"\n    v-bind=\"$attrs\"\n    :popper-class=\"`${ns.namespace.value}-popover`\"\n    :popper-style=\"style\"\n    :teleported=\"teleported\"\n    :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n    :hide-after=\"hideAfter\"\n    :persistent=\"persistent\"\n  >\n    <template #content>\n      <div :class=\"ns.b()\">\n        <div :class=\"ns.e('main')\">\n          <el-icon\n            v-if=\"!hideIcon && icon\"\n            :class=\"ns.e('icon')\"\n            :style=\"{ color: iconColor }\"\n          >\n            <component :is=\"icon\" />\n          </el-icon>\n          {{ title }}\n        </div>\n        <div :class=\"ns.e('action')\">\n          <slot name=\"actions\" :confirm=\"confirm\" :cancel=\"cancel\">\n            <el-button\n              size=\"small\"\n              :type=\"cancelButtonType === 'text' ? '' : cancelButtonType\"\n              :text=\"cancelButtonType === 'text'\"\n              @click=\"cancel\"\n            >\n              {{ finalCancelButtonText }}\n            </el-button>\n            <el-button\n              size=\"small\"\n              :type=\"confirmButtonType === 'text' ? '' : confirmButtonType\"\n              :text=\"confirmButtonType === 'text'\"\n              @click=\"confirm\"\n            >\n              {{ finalConfirmButtonText }}\n            </el-button>\n          </slot>\n        </div>\n      </div>\n    </template>\n    <template v-if=\"$slots.reference\">\n      <slot name=\"reference\" />\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref } from 'vue'\nimport ElButton from '@element-plus/components/button'\nimport ElIcon from '@element-plus/components/icon'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { addUnit } from '@element-plus/utils'\nimport { popconfirmEmits, popconfirmProps } from './popconfirm'\n\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\ndefineOptions({\n  name: 'ElPopconfirm',\n})\n\nconst props = defineProps(popconfirmProps)\nconst emit = defineEmits(popconfirmEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('popconfirm')\nconst tooltipRef = ref<TooltipInstance>()\n\nconst hidePopper = () => {\n  tooltipRef.value?.onClose?.()\n}\n\nconst style = computed(() => {\n  return {\n    width: addUnit(props.width),\n  }\n})\n\nconst confirm = (e: MouseEvent) => {\n  emit('confirm', e)\n  hidePopper()\n}\nconst cancel = (e: MouseEvent) => {\n  emit('cancel', e)\n  hidePopper()\n}\n\nconst finalConfirmButtonText = computed(\n  () => props.confirmButtonText || t('el.popconfirm.confirmButtonText')\n)\nconst finalCancelButtonText = computed(\n  () => props.cancelButtonText || t('el.popconfirm.cancelButtonText')\n)\n</script>\n"], "names": ["useLocale", "useNamespace", "ref", "style", "computed", "addUnit", "_openBlock", "_createBlock", "_unref", "ElTooltip", "_mergeProps"], "mappings": ";;;;;;;;;;;;;;uCAgEc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAIA,eAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAKC,qBAAa,YAAY,CAAA,CAAA;AACpC,IAAA,MAAM,aAAaC,OAAqB,EAAA,CAAA;AAExC,IAAA,MAAM,aAAa,MAAM;AACvB,MAAA,IAAA,EAAA,EAAA,EAAA,CAAW;AAAiB,MAC9B,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAO,MAAAC,OAAA,GAAAC,YAAA,CAAA,MAAA;AAAA,MACL,OAAA;AAA0B,QAC5B,KAAA,EAAAC,aAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AAAA,OACD,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,YAAgB,KAAC;AACjB,MAAW,IAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA;AAAA,MACb,UAAA,EAAA,CAAA;AACA,KAAM,CAAA;AACJ,IAAA,MAAA,WAAe,KAAC;AAChB,MAAW,IAAA,CAAA,QAAA,EAAA,CAAA,CAAA,CAAA;AAAA,MACb,UAAA,EAAA,CAAA;AAEA,KAAA,CAAA;AAA+B,IAAA,MACvB,sBAA2B,GAAAD,YAAmC,CAAA,MAAA,KAAA,CAAA,iBAAA,IAAA,CAAA,CAAA,iCAAA,CAAA,CAAA,CAAA;AAAA,IACtE,MAAA,qBAAA,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,gBAAA,IAAA,CAAA,CAAA,gCAAA,CAAA,CAAA,CAAA;AACA,IAAA,OAA8B,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAC5B,OAAME,aAA0B,EAAA,EAAAC,eAAA,CAAEC,SAAgC,CAAAC,iBAAA,CAAA,EAAAC,cAAA,CAAA;AAAA,QACpE,OAAA,EAAA,YAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}