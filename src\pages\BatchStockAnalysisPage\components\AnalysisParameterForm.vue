<template>
  <div class="parameter-form-container">
    <el-card shadow="hover">
      <template #header>
        <div class="form-header">
          <span>分析参数设置</span>
          <el-tooltip content="批量分析多只股票在不同均线周期下的表现" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <el-form 
        :model="formData" 
        :rules="rules" 
        ref="formRef" 
        label-width="120px" 
        class="parameter-form"
      >
        <!-- 股票代码输入 -->
        <el-form-item label="股票代码" prop="stockCodesInput">
          <el-input 
            v-model="formData.stockCodesInput" 
            type="textarea" 
            :autosize="{ minRows: 2, maxRows: 5 }" 
            placeholder="请输入股票代码，多个代码用逗号或空格分隔，如：000002,000514"
          ></el-input>
          <div class="form-tip">
            <el-icon><InfoFilled /></el-icon>
            <span>支持多个股票代码，用逗号、空格或换行符分隔，如：000002,000514</span>
          </div>
        </el-form-item>
        
        <!-- 日期范围选择 -->
        <el-form-item label="日期范围" required>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item prop="startDate">
                <el-date-picker
                  v-model="formData.startDate"
                  type="date"
                  placeholder="开始日期"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="endDate">
                <el-date-picker
                  v-model="formData.endDate"
                  type="date"
                  placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        
        <!-- 初始资金 -->
        <el-form-item label="初始资金" prop="initialCapital">
          <el-input-number 
            v-model="formData.initialCapital" 
            :min="10000" 
            :step="10000"
            :precision="2"
            style="width: 180px"
          ></el-input-number>
          <span class="form-tip-inline">初始投资资金金额</span>
        </el-form-item>
        
        <!-- 均线周期范围 -->
        <el-form-item label="均线周期范围">
          <el-row :gutter="12">
            <el-col :span="8">
              <el-form-item prop="minPeriod">
                <el-input-number 
                  v-model="formData.minPeriod" 
                  :min="2" 
                  :max="formData.maxPeriod"
                  style="width: 100%"
                  placeholder="最小周期"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="maxPeriod">
                <el-input-number 
                  v-model="formData.maxPeriod" 
                  :min="formData.minPeriod"
                  :max="120"
                  style="width: 100%"
                  placeholder="最大周期"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="step">
                <el-input-number 
                  v-model="formData.step" 
                  :min="1"
                  :max="10"
                  style="width: 100%"
                  placeholder="步长"
                ></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="form-tip">
            <el-icon><InfoFilled /></el-icon>
            <span>例如：从2到20步长为2，将分析MA2、MA4、MA6...MA20</span>
          </div>
        </el-form-item>
        
        <!-- 按钮组 -->
        <el-form-item>
          <el-button 
            type="primary" 
            @click="handleSubmit" 
            :loading="loading"
            :disabled="!isFormValid"
          >
            开始分析
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { InfoFilled, QuestionFilled } from '@element-plus/icons-vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['start-analysis', 'reset'])

// 表单引用
const formRef = ref(null)

// 表单数据
const formData = reactive({
  stockCodesInput: '600320,002554,000571,000890,600243,600759,600892,002427,600467,002086,002498,002306,002523,600968,600871,000796,601106,601989,600665,600569,601015,002628,002210,601399,000573,600518,601218,600282,002224,600339,600751,601678,000955,600208,002069,000035,002110,000898,601068,000592,600169,600117,601003,600798,002694,002256,002682,601515,600575,000959,002047,600187,600581,600231,600782,600382,002408,601008,000010,601000,600017,000428,601228,601996,600433,000518,600308,000692,002638,000755,000637,601866,601518,600020,002154,002471,600180,002377,600368,000720,601880,601016,601975,600082,601700,600805,600496,601107,000972,002266,000523,000407,601588,002060,000825,601018,600500,600863,001213,002076,002397,603797,000900,600572,600103,000709,601868,600726,000912,000897,000088,002641,000078,002495,600252,600170,000927,000767,600939,601991,600179,600815,002132,002589,000820,600008,002356,601618,601326,002081,601188,600681,600928,600794,000778,600033,002375,001896,600649,600035,600507,002135,600468,600717,002107,002062,600744,002157,000572,000659,000949,601005,000717,600490,601368,600578,000883,600683,603843,002958,600248,600502,000965,002565,600526,600935,000059,000552,002061,002206,002663,600491,600512,002573,600684,601718,000926,601158,000966,600617,002613,600395,600333,000591,002386,600802,000514,000690,002307,002839,000090,601568,600853,002191,000790,600115,003816,002108,002303,002109,000558,601333,600293,600448,000797,601916,000631,600108,600477,600844,600675,002133,600545,002506,002445,000069,600168,000539,001227,000718,600149,002562,600795,002672,600905,000055,002807,002948,002385,000517,000652,002620,002482,000863,603077,600010,600594,600708,000632,600881,600790,600261,002666,002578,600688,601992,002567,601968,603336,000520,601669,000812,601010,002687,600307,000751,600221,600488,601778,003035,600075,600022,601860,600396,002004,600067,600740,002793,002437,600724,603616,600664,601366,000056,000068,600936,002501,000698,600533,002671,600959,000563,600725,600310,002969,600076,002330,002145,600527,600658,002702,002431,600808,600800,002084,002269,600121,000639,000677,600543,000826,000060,002936,600626,002548,002390,600219,600963,002012,601599,002426,002476,002688,000031,002775,600162,000620,600052,600370,002218,002263,000850,000838,002344,002370,600981,600567,600748,603778,000782,002285,600159,002413,600016,002160,600966,002678,002102,002228,600635,000545,603818,002305,002146,002496,600157,000839,600227,000402,603601,002127,600691,600429,002486,601212,000564,000725,002524,600226,600604,601086,600266,600540,600300,002717,002607,002798,600828,600094,601099,002247,601929,600207,600255,600239,600651,601818,600369,000892,600515,600643,002021,601113,600854,000931,600537,002470,600606,600166,600322,600400,002188,002314,002662,600982,001330,600791,000599,000813,600816,000629,600193,000630,002551,002631,600792,000627,000008,000415,000802,000981,002374,601162,601908,600653,600824,600984,002219,603669,000607,002513,600383,603188,000750,002494,600110,002652,002489,600770,000546,000509,600654,600657,600880,000727,002321,603021,002177,600340,002716,603980,603183,000560,600337,002542,600743,601375,600503,600425,600172,600376,002323,000882,000510,002121,002659,601388,600130,002131,600622,601279,002418,000420,603030,000100,002622,600439,600423,600361,002211,600807,600676,600280,002535,002596,601011,000816,002239,600355,002522,601828,002640,002634,000036,002421,600868,000691,002172,000723,600281,002067,000761,002183,600758,002630,601616,603335,600386,000785,600666,000980,000903,002348,002526,600525,600595,000608,601933,601619,600960,600408,600063,600495,002689,002329,600716,600705,002122,002072,000757,002162,002570,600403', // 默认示例代码
  startDate: '2023-04-01', // 默认开始日期
  endDate: '2025-12-31',   // 默认结束日期
  initialCapital: 10000,   // 默认初始资金
  minPeriod: 2,            // 最小均线周期
  maxPeriod: 30,           // 最大均线周期
  step: 2                  // 步长
})

// 表单验证规则
const rules = {
  stockCodesInput: [
    { required: true, message: '请输入至少一个股票代码', trigger: 'blur' }
  ],
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择结束日期', trigger: 'change' }
  ],
  initialCapital: [
    { required: true, message: '请输入初始资金', trigger: 'blur' }
  ],
  minPeriod: [
    { required: true, message: '请输入最小周期', trigger: 'blur' }
  ],
  maxPeriod: [
    { required: true, message: '请输入最大周期', trigger: 'blur' }
  ],
  step: [
    { required: true, message: '请输入步长', trigger: 'blur' }
  ]
}

// 处理股票代码字符串，转换为数组
const parseStockCodes = (codesStr) => {
  if (!codesStr) return []
  
  // 将输入的股票代码分割成数组
  // 支持多种分隔符：逗号、空格、分号、换行符
  return codesStr
    .split(/[\s,;\n]+/) // 使用正则分割
    .map(code => code.trim()) // 去除首尾空格
    .filter(code => !!code) // 过滤空字符串
    .map(code => {
      // 确保股票代码为6位数字，不足6位前面补0
      let formattedCode = code.replace(/\D/g, '') // 去除非数字字符
      while (formattedCode.length < 6) {
        formattedCode = '0' + formattedCode
      }
      return formattedCode.slice(0, 6) // 取前6位
    })
}

// 表单是否有效
const isFormValid = computed(() => {
  const { stockCodesInput, startDate, endDate } = formData
  return stockCodesInput && startDate && endDate && parseStockCodes(stockCodesInput).length > 0
})

// 提交表单
const handleSubmit = async () => {
  if (props.loading) return
  
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 处理股票代码
    const stockCodes = parseStockCodes(formData.stockCodesInput)
    
    if (stockCodes.length === 0) {
      // 虽然通过了验证，但解析后没有有效的代码
      return
    }
    
    // 发送事件
    emit('start-analysis', {
      ...formData,
      stockCodes
    })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  emit('reset')
}

// 监听最小/最大周期变化，保持合理范围
watch(() => formData.minPeriod, (newValue) => {
  if (newValue > formData.maxPeriod) {
    formData.maxPeriod = newValue
  }
})

watch(() => formData.maxPeriod, (newValue) => {
  if (newValue < formData.minPeriod) {
    formData.minPeriod = newValue
  }
})
</script>

<style scoped>
.parameter-form-container {
  margin-bottom: 20px;
}

.form-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.form-header .el-icon {
  margin-left: 8px;
  font-size: 16px;
  color: #909399;
}

.parameter-form {
  padding: 10px;
}

.form-tip {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
}

.form-tip .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

.form-tip-inline {
  margin-left: 10px;
  font-size: 13px;
  color: #909399;
}
</style> 