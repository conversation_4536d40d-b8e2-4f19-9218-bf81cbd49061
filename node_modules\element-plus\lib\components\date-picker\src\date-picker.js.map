{"version": 3, "file": "date-picker.js", "sources": ["../../../../../../packages/components/date-picker/src/date-picker.tsx"], "sourcesContent": ["import { defineComponent, provide, reactive, ref, toRef } from 'vue'\nimport dayjs from 'dayjs'\nimport customParseFormat from 'dayjs/plugin/customParseFormat.js'\nimport advancedFormat from 'dayjs/plugin/advancedFormat.js'\nimport localeData from 'dayjs/plugin/localeData.js'\nimport weekOfYear from 'dayjs/plugin/weekOfYear.js'\nimport weekYear from 'dayjs/plugin/weekYear.js'\nimport dayOfYear from 'dayjs/plugin/dayOfYear.js'\nimport isSameOrAfter from 'dayjs/plugin/isSameOrAfter.js'\nimport isSameOrBefore from 'dayjs/plugin/isSameOrBefore.js'\nimport { useNamespace } from '@element-plus/hooks'\nimport {\n  CommonPicker,\n  DEFAULT_FORMATS_DATE,\n  DEFAULT_FORMATS_DATEPICKER,\n  type DateModelType,\n  type SingleOrRange,\n} from '@element-plus/components/time-picker'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { ROOT_PICKER_INJECTION_KEY } from './constants'\n\nimport { datePickerProps } from './props/date-picker'\nimport { getPanel } from './panel-utils'\nimport type { DatePickerExpose } from './instance'\n\ndayjs.extend(localeData)\ndayjs.extend(advancedFormat)\ndayjs.extend(customParseFormat)\ndayjs.extend(weekOfYear)\ndayjs.extend(weekYear)\ndayjs.extend(dayOfYear)\ndayjs.extend(isSameOrAfter)\ndayjs.extend(isSameOrBefore)\n\nexport default defineComponent({\n  name: 'ElDatePicker',\n  install: null,\n  props: datePickerProps,\n  emits: [UPDATE_MODEL_EVENT],\n  setup(props, { expose, emit, slots }) {\n    const ns = useNamespace('picker-panel')\n\n    provide('ElPopperOptions', reactive(toRef(props, 'popperOptions')))\n    provide(ROOT_PICKER_INJECTION_KEY, {\n      slots,\n      pickerNs: ns,\n    })\n\n    const commonPicker = ref<InstanceType<typeof CommonPicker>>()\n    const refProps: DatePickerExpose = {\n      focus: () => {\n        commonPicker.value?.focus()\n      },\n      blur: () => {\n        commonPicker.value?.blur()\n      },\n      handleOpen: () => {\n        commonPicker.value?.handleOpen()\n      },\n      handleClose: () => {\n        commonPicker.value?.handleClose()\n      },\n    }\n\n    expose(refProps)\n\n    const onModelValueUpdated = (val: SingleOrRange<DateModelType> | null) => {\n      emit(UPDATE_MODEL_EVENT, val)\n    }\n\n    return () => {\n      // since props always have all defined keys on it, {format, ...props} will always overwrite format\n      // pick props.format or provide default value here before spreading\n      const format =\n        props.format ??\n        (DEFAULT_FORMATS_DATEPICKER[props.type] || DEFAULT_FORMATS_DATE)\n\n      const Component = getPanel(props.type)\n\n      return (\n        <CommonPicker\n          {...props}\n          format={format}\n          type={props.type}\n          ref={commonPicker}\n          onUpdate:modelValue={onModelValueUpdated}\n        >\n          {{\n            default: (scopedProps: /**FIXME: remove any type */ any) => (\n              <Component {...scopedProps}>\n                {{\n                  'prev-month': slots['prev-month'],\n                  'next-month': slots['next-month'],\n                  'prev-year': slots['prev-year'],\n                  'next-year': slots['next-year'],\n                }}\n              </Component>\n            ),\n            'range-separator': slots['range-separator'],\n          }}\n        </CommonPicker>\n      )\n    }\n  },\n})\n"], "names": ["dayjs", "extend", "localeData", "advancedFormat", "customParseFormat", "weekOfYear", "weekYear", "dayOfYear", "isSameOrAfter", "isSameOrBefore", "defineComponent", "name", "install", "props", "datePickerProps", "emits", "UPDATE_MODEL_EVENT", "expose", "emit", "slots", "useNamespace", "provide", "ROOT_PICKER_INJECTION_KEY", "reactive", "pickerNs", "ref", "commonPicker", "focus", "blur", "handleOpen", "handleClose", "refProps", "onModelValueUpdated", "format", "DEFAULT_FORMATS_DATEPICKER", "Component", "getPanel", "type", "_createVNode", "CommonPicker", "_mergeProps", "default", "scopedProps"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBAA,yBAAK,CAACC,MAAN,CAAaC,8BAAb,CAAA,CAAA;AACAF,yBAAK,CAACC,MAAN,CAAaE,kCAAb,CAAA,CAAA;AACAH,yBAAK,CAACC,MAAN,CAAaG,qCAAb,CAAA,CAAA;AACAJ,yBAAK,CAACC,MAAN,CAAaI,8BAAb,CAAA,CAAA;AACAL,yBAAK,CAACC,MAAN,CAAaK,4BAAb,CAAA,CAAA;AACAN,yBAAK,CAACC,MAAN,CAAaM,6BAAb,CAAA,CAAA;AACAP,yBAAK,CAACC,MAAN,CAAaO,iCAAb,CAAA,CAAA;AACAR,yBAAK,CAACC,MAAN,CAAaQ,kCAAb,CAAA,CAAA;AAEA,iBAAeC,mBAAe,CAAC;AAC7BC,EAAAA,IAAI,EAAE,cADuB;AAE7BC,EAAAA,OAAO,EAAE,IAFoB;AAG7BC,EAAAA,KAAK,EAAEC,0BAHsB;EAI7BC,KAAK,EAAE,CAACC,wBAAD,CAJsB;;IAKxB;IAAUC,IAAF;IAAUC,KAAV;AAAgBC,GAAAA,EAAAA;AAAhB,IAAyB,MAAA,EAAA,GAAAC,kBAAA,CAAA,cAAA,CAAA,CAAA;AACpC,IAAAC,WAAQ,CAAA,yCAAR,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA,CAAA,CAAA;AAEAA,IAAAA,WAAO,CAACC,mCAAmBC,EAAQ;MAC5B,KAAA;MACLJ,QADiC,EAAA,EAAA;AAEjCK,KAAAA,CAAAA,CAAAA;AAFiC,IAAA,MAAnC,YAAA,GAAAC,OAAA,EAAA,CAAA;IAKA,MAAMC,QAAAA,GAAAA;AACN,MAAA,aAAmC;AACjCC,QAAAA,MAAa,CAAA;QACXD,CAAY,EAAA,GAAA,YAAZ,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;OAF+B;AAIjCE,MAAAA,IAAI,EAAE,MAAM;QACVF,IAAY,EAAA,CAAA;QALmB,CAAA,EAAA,GAAA,YAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,CAAA;AAOjCG,OAAAA;gBACc,EAAA,MAAZ;QAR+B,IAAA,EAAA,CAAA;AAUjCC,QAAAA,CAAAA,EAAAA,GAAAA,YAAmB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,EAAA,CAAA;;AAElB,MAAA,WAAA,EAAA,MAAA;QAZH,IAAA,EAAA,CAAA;QAeM,CAAA,EAACC,eAAP,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAA,EAAA,CAAA;;KAEMC,CAAAA;AACJd,IAAAA,MAAI,CAACF,QAAAA,CAAAA,CAAAA;IACN,MAFD,mBAAA,GAAA,CAAA,GAAA,KAAA;;AAIA,KAAA,CAAA;AACE,IAAA,OAAA,MAAA;AACA,MAAA,IAAA,EAAA,CAAA;AACA,MAAA,MAAMiB,MAAM,GACVpB,CAAK,EAAA,GAACoB,MAAN,MACCC,KAAAA,IAAAA,GAAAA,EAAAA,GAAAA,sFAFH,CAAA;AAIA,MAAA,MAAMC,SAAS,GAAGC,mBAAQ,CAACvB,KAAK,CAACwB,IAAP,CAA1B,CAAA;AAEA,MAAA,OAAAC,eAAA,CAAAC,iBAAA,EAAAC,cAAA,CAEQ3B,KAFR,EAAA;AAAA,QAAA,QAAA,EAGYoB,MAHZ;QAAA,MAIUpB,EAAAA,KAAK,CAACwB,IAJhB;AAAA,QAAA,KAAA,EAKSX,YALT;QAAA,qBAMyBM,EAAAA,mBAAAA;AANzB,OAAA,CAAA,EAAA;QASMS,OAAO,EAAGC,CAAD,WAAA,KAAAJ,eAAA,CAAA,SACQI,EADR,WAAA,EAAA;UAGH,YAAcvB,EAAAA,KAAK,CAAC,YAAD,CAHhB;UAIH,YAAcA,EAAAA,KAAK,CAAC,YAAD,CAJhB;UAKH,WAAaA,EAAAA,KAAK,CAAC,WAAD,CALf;UAMH,WAAaA,EAAAA,KAAK,CAAC,WAAD,CAAA;SAf9B,CAAA;QAmBM,iBAAmBA,EAAAA,KAAK,CAAC,iBAAD,CAAA;AAnB9B,OAAA,CAAA,CAAA;KATF,CAAA;AAiCD,GAAA;;;;;"}