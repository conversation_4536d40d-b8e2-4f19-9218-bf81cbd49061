import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const service = axios.create({
  baseURL: `${import.meta.env.VITE_API_BASE_URL}${import.meta.env.VITE_API_PREFIX}`,
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || 10000)
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 这里可以添加认证信息，如token等
    // config.headers['Authorization'] = `Bearer ${getToken()}`
    console.log('发送请求:', config.url, config.params); // 添加请求日志
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    // 添加响应日志
    console.log('接收响应:', response.config.url, res);
    // 这里可以根据实际接口返回格式进行调整
    return res
  },
  error => {
    console.error('响应错误:', error)
    // 详细记录错误信息
    if (error.response) {
      console.error('错误状态码:', error.response.status)
      console.error('错误数据:', error.response.data)
    } else if (error.request) {
      console.error('没有收到响应:', error.request)
    } else {
      console.error('请求配置错误:', error.message)
    }
    
    // 处理错误信息
    const message = error.response?.data?.detail || '请求失败，请稍后重试'
    ElMessage({
      message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service 