{"version": 3, "file": "index.js", "sources": ["../../../../../../../packages/components/table/src/table-footer/index.ts"], "sourcesContent": ["// @ts-nocheck\nimport { defineComponent, h, inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport useLayoutObserver from '../layout-observer'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport useStyle from './style-helper'\nimport type { Store } from '../store'\n\nimport type { PropType } from 'vue'\nimport type { DefaultRow, Sort, SummaryMethod } from '../table/defaults'\nexport interface TableFooter<T> {\n  fixed: string\n  store: Store<T>\n  summaryMethod: SummaryMethod<T>\n  sumText: string\n  border: boolean\n  defaultSort: Sort\n}\n\nexport default defineComponent({\n  name: 'ElTableFooter',\n\n  props: {\n    fixed: {\n      type: String,\n      default: '',\n    },\n    store: {\n      required: true,\n      type: Object as PropType<TableFooter<DefaultRow>['store']>,\n    },\n    summaryMethod: Function as PropType<\n      TableFooter<DefaultRow>['summaryMethod']\n    >,\n    sumText: String,\n    border: Boolean,\n    defaultSort: {\n      type: Object as PropType<TableFooter<DefaultRow>['defaultSort']>,\n      default: () => {\n        return {\n          prop: '',\n          order: '',\n        }\n      },\n    },\n  },\n  setup(props) {\n    const parent = inject(TABLE_INJECTION_KEY)\n    const ns = useNamespace('table')\n    const { getCellClasses, getCellStyles, columns } = useStyle(\n      props as TableFooter<DefaultRow>\n    )\n    const { onScrollableChange, onColumnsChange } = useLayoutObserver(parent!)\n\n    return {\n      ns,\n      onScrollableChange,\n      onColumnsChange,\n      getCellClasses,\n      getCellStyles,\n      columns,\n    }\n  },\n  render() {\n    const { columns, getCellStyles, getCellClasses, summaryMethod, sumText } =\n      this\n    const data = this.store.states.data.value\n    let sums = []\n    if (summaryMethod) {\n      sums = summaryMethod({\n        columns,\n        data,\n      })\n    } else {\n      columns.forEach((column, index) => {\n        if (index === 0) {\n          sums[index] = sumText\n          return\n        }\n        const values = data.map((item) => Number(item[column.property]))\n        const precisions = []\n        let notNumber = true\n        values.forEach((value) => {\n          if (!Number.isNaN(+value)) {\n            notNumber = false\n            const decimal = `${value}`.split('.')[1]\n            precisions.push(decimal ? decimal.length : 0)\n          }\n        })\n        const precision = Math.max.apply(null, precisions)\n        if (!notNumber) {\n          sums[index] = values.reduce((prev, curr) => {\n            const value = Number(curr)\n            if (!Number.isNaN(+value)) {\n              return Number.parseFloat(\n                (prev + curr).toFixed(Math.min(precision, 20))\n              )\n            } else {\n              return prev\n            }\n          }, 0)\n        } else {\n          sums[index] = ''\n        }\n      })\n    }\n    return h(\n      h('tfoot', [\n        h('tr', {}, [\n          ...columns.map((column, cellIndex) =>\n            h(\n              'td',\n              {\n                key: cellIndex,\n                colspan: column.colSpan,\n                rowspan: column.rowSpan,\n                class: getCellClasses(columns, cellIndex),\n                style: getCellStyles(column, cellIndex),\n              },\n              [\n                h(\n                  'div',\n                  {\n                    class: ['cell', column.labelClassName],\n                  },\n                  [sums[cellIndex]]\n                ),\n              ]\n            )\n          ),\n        ]),\n      ])\n    )\n  },\n})\n"], "names": ["defineComponent", "inject", "TABLE_INJECTION_KEY", "useNamespace", "useStyle", "useLayoutObserver", "h"], "mappings": ";;;;;;;;;;AAKA,kBAAeA,mBAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,eAAe;AACvB,EAAE,KAAK,EAAE;AACT,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE,EAAE;AACjB,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,IAAI,EAAE,MAAM;AAClB,KAAK;AACL,IAAI,aAAa,EAAE,QAAQ;AAC3B,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,MAAM,EAAE,OAAO;AACnB,IAAI,WAAW,EAAE;AACjB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO;AACf,UAAU,IAAI,EAAE,EAAE;AAClB,UAAU,KAAK,EAAE,EAAE;AACnB,SAAS,CAAC;AACV,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,KAAK,CAAC,KAAK,EAAE;AACf,IAAI,MAAM,MAAM,GAAGC,UAAM,CAACC,0BAAmB,CAAC,CAAC;AAC/C,IAAI,MAAM,EAAE,GAAGC,kBAAY,CAAC,OAAO,CAAC,CAAC;AACrC,IAAI,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,OAAO,EAAE,GAAGC,sBAAQ,CAAC,KAAK,CAAC,CAAC;AACvE,IAAI,MAAM,EAAE,kBAAkB,EAAE,eAAe,EAAE,GAAGC,yBAAiB,CAAC,MAAM,CAAC,CAAC;AAC9E,IAAI,OAAO;AACX,MAAM,EAAE;AACR,MAAM,kBAAkB;AACxB,MAAM,eAAe;AACrB,MAAM,cAAc;AACpB,MAAM,aAAa;AACnB,MAAM,OAAO;AACb,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;AACpF,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9C,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,IAAI,GAAG,aAAa,CAAC;AAC3B,QAAQ,OAAO;AACf,QAAQ,IAAI;AACZ,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,KAAK;AACzC,QAAQ,IAAI,KAAK,KAAK,CAAC,EAAE;AACzB,UAAU,IAAI,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;AAChC,UAAU,OAAO;AACjB,SAAS;AACT,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzE,QAAQ,MAAM,UAAU,GAAG,EAAE,CAAC;AAC9B,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC;AAC7B,QAAQ,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AAClC,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;AACrC,YAAY,SAAS,GAAG,KAAK,CAAC;AAC9B,YAAY,MAAM,OAAO,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,YAAY,UAAU,CAAC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC1D,WAAW;AACX,SAAS,CAAC,CAAC;AACX,QAAQ,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAC3D,QAAQ,IAAI,CAAC,SAAS,EAAE;AACxB,UAAU,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,KAAK;AACtD,YAAY,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AACvC,YAAY,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;AACvC,cAAc,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACvF,aAAa,MAAM;AACnB,cAAc,OAAO,IAAI,CAAC;AAC1B,aAAa;AACb,WAAW,EAAE,CAAC,CAAC,CAAC;AAChB,SAAS,MAAM;AACf,UAAU,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AAC3B,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,OAAOC,KAAC,CAACA,KAAC,CAAC,OAAO,EAAE;AACxB,MAAMA,KAAC,CAAC,IAAI,EAAE,EAAE,EAAE;AAClB,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,KAAKA,KAAC,CAAC,IAAI,EAAE;AACtD,UAAU,GAAG,EAAE,SAAS;AACxB,UAAU,OAAO,EAAE,MAAM,CAAC,OAAO;AACjC,UAAU,OAAO,EAAE,MAAM,CAAC,OAAO;AACjC,UAAU,KAAK,EAAE,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC;AACnD,UAAU,KAAK,EAAE,aAAa,CAAC,MAAM,EAAE,SAAS,CAAC;AACjD,SAAS,EAAE;AACX,UAAUA,KAAC,CAAC,KAAK,EAAE;AACnB,YAAY,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,cAAc,CAAC;AAClD,WAAW,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AAC/B,SAAS,CAAC,CAAC;AACX,OAAO,CAAC;AACR,KAAK,CAAC,CAAC,CAAC;AACR,GAAG;AACH,CAAC,CAAC;;;;"}