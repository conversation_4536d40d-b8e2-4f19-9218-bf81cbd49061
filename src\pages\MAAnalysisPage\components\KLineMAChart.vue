<template>
  <div ref="chartRef" :style="{ height: '700px', width: '100%' }"></div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, defineExpose } from 'vue'
import * as echarts from 'echarts'
import { getMALineColor } from '@/api/maAnalysis'

const props = defineProps({
  dates: {
    type: Array,
    default: () => []
  },
  klineData: {
    type: Array,
    default: () => []
  },
  maData: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: '股票K线与均线分析'
  },
  periods: {
    type: Array,
    default: () => [5, 10, 20, 30, 60]
  },
  analysisData: {
    type: Object,
    default: () => ({})
  }
})

const chartRef = ref(null)
let chartInstance = null

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  // 销毁旧实例（如果存在），防止重复初始化和内存泄漏
  if (chartInstance) {
    chartInstance.dispose();
  }
  // 创建图表实例
  chartInstance = echarts.init(chartRef.value)
  
  // 窗口大小改变时，调整图表大小
  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
  
  // 首次渲染图表
  updateChart()
}

// 准备距离数据
const prepareDistanceData = (selectedPeriod) => {
  if (!props.analysisData || !props.analysisData.period_analyses) return []
  
  const periodAnalysis = props.analysisData.period_analyses[selectedPeriod]
  if (!periodAnalysis || !periodAnalysis.distance_details || !periodAnalysis.date_position_details) return []
  
  const distanceDetails = periodAnalysis.distance_details
  const positionDetails = periodAnalysis.date_position_details
  
  // 创建交易日期到位置的映射，方便查找
  const dateToPositionMap = {}
  positionDetails.forEach(detail => {
    dateToPositionMap[detail.trade_date] = detail.position
  })
  
  // 从距离详情中提取数据，并根据位置信息调整距离值
  const distanceData = distanceDetails.map(detail => {
    const tradeDate = detail.trade_date
    const position = dateToPositionMap[tradeDate] || 'cross' // 默认为cross如果找不到
    
    // 如果价格跨越均线，则将距离置为0
    let adjustedDistance = detail.distance
    if (position === 'cross') {
      adjustedDistance = 0
    }
    
    return [
      tradeDate,
      adjustedDistance, // 调整后的距离值（跨越均线时为0）
      adjustedDistance > 0 ? 1 : (adjustedDistance < 0 ? -1 : 0) // 区分颜色的值（0表示跨越）
    ]
  })
  
  return distanceData
}

// 准备标签数据
const prepareLabelData = (selectedPeriod) => {
  if (!props.analysisData || !props.analysisData.period_analyses) return []
  
  const periodAnalysis = props.analysisData.period_analyses[selectedPeriod]
  if (!periodAnalysis || !periodAnalysis.date_position_details) return []
  
  return periodAnalysis.date_position_details
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return
  
  console.log('更新图表:', { 
    dates: props.dates.length, 
    klineData: props.klineData.length, 
    maData: props.maData,
    periods: props.periods,
    analysisData: props.analysisData
  })
  
  // 筛选要显示的周期数据
  const selectedPeriods = Object.keys(props.maData)
    .filter(period => props.periods.includes(Number(period)))
    .sort((a, b) => Number(a) - Number(b))
  
  console.log('筛选后的均线周期:', selectedPeriods)
  
  // 创建直接使用日期的映射
  const dateSet = new Set(props.dates)
  
  // 准备均线数据系列
  const maSeries = selectedPeriods.map(period => {
    const periodNum = Number(period)
    const color = getMALineColor(periodNum)
    const maData = props.maData[period] || []
    
    console.log(`MA${periodNum} 数据:`, maData.length > 0 ? maData.slice(0, 3) : '无数据')
    
    // 过滤掉日期不在 K 线日期集合中的数据
    const validData = Array.isArray(maData) && maData.length > 0 
      ? maData.filter(point => Array.isArray(point) && point.length === 2 && dateSet.has(point[0]))
      : []
    
    console.log(`MA${periodNum} 有效数据:`, validData.length > 0 ? validData.slice(0, 3) : '无数据')
    
    // 为均线创建数据系列
    return {
      name: `MA${periodNum}`,
      type: 'line',
      data: validData,
      smooth: true,
      lineStyle: {
        opacity: 0.8,
        width: 2,
        color: color
      },
      symbol: 'none',
      // 添加标签
      label: {
        show: true,
        position: 'right',
        formatter: `MA${periodNum}`,
        backgroundColor: color,
        color: '#fff',
        padding: [3, 6],
        borderRadius: 3,
        // 只在右边缘显示标签
        distance: 10
      },
      // 仅在最后一个点显示标签
      endLabel: {
        show: true,
        formatter: `MA${periodNum}`,
        backgroundColor: color,
        color: '#fff',
        padding: [3, 6],
        borderRadius: 3
      }
    }
  })
  
  // 准备距离数据（当有选中的周期时）
  let distanceData = []
  if (selectedPeriods.length > 0) {
    // 使用第一个选中的周期来展示距离数据
    const firstSelectedPeriod = selectedPeriods[0]
    distanceData = prepareDistanceData(firstSelectedPeriod)
    console.log(`距离数据 (MA${firstSelectedPeriod}):`, distanceData.length > 0 ? distanceData.slice(0, 3) : '无数据')
  }
  
  // 图表配置
  const option = {
    title: {
      text: props.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      borderWidth: 1,
      borderColor: '#ccc',
      padding: 10,
      textStyle: {
        color: '#000'
      },
      formatter: (params) => {
        const date = params[0].name
        let tooltip = `<div style="font-weight:bold;margin-bottom:5px;">${date}</div>`
        
        // K线数据
        const klineData = params.find(item => item.seriesName === 'K线')
        if (klineData) {
          const color = klineData.data[1] > klineData.data[0] 
            ? '#F56C6C' // 收盘价 > 开盘价，红色
            : '#67C23A' // 收盘价 <= 开盘价，绿色
          
          tooltip += `<div style="margin-bottom:3px;">
            <span style="display:inline-block;width:10px;height:10px;background-color:${color};border-radius:50%;margin-right:5px;"></span>
            <span>开盘价：${klineData.data[0]}</span>
          </div>`
          tooltip += `<div style="margin-bottom:3px;">
            <span style="display:inline-block;width:10px;height:10px;background-color:${color};border-radius:50%;margin-right:5px;"></span>
            <span>收盘价：${klineData.data[1]}</span>
          </div>`
          tooltip += `<div style="margin-bottom:3px;">
            <span style="display:inline-block;width:10px;height:10px;background-color:${color};border-radius:50%;margin-right:5px;"></span>
            <span>最低价：${klineData.data[2]}</span>
          </div>`
          tooltip += `<div style="margin-bottom:5px;">
            <span style="display:inline-block;width:10px;height:10px;background-color:${color};border-radius:50%;margin-right:5px;"></span>
            <span>最高价：${klineData.data[3]}</span>
          </div>`
        }
        
        // 均线数据
        params.forEach(param => {
          if (param.seriesName.startsWith('MA')) {
            tooltip += `<div style="margin-bottom:3px;">
              <span style="display:inline-block;width:10px;height:10px;background-color:${param.color};border-radius:50%;margin-right:5px;"></span>
              <span>${param.seriesName}：${typeof param.data[1] !== 'undefined' ? param.data[1] : param.value}</span>
            </div>`
          }
        })
        
        // 距离数据
        const distanceItem = params.find(item => item.seriesName === '均线距离')
        if (distanceItem && typeof distanceItem.data[1] !== 'undefined') {
          const distance = distanceItem.data[1]
          let distanceColor = '#909399' // 默认灰色（跨越均线）
          let positionText = '跨越'
          
          if (distance > 0) {
            distanceColor = '#F56C6C' // 高于均线，红色
            positionText = '高于'
          } else if (distance < 0) {
            distanceColor = '#67C23A' // 低于均线，绿色
            positionText = '低于'
          }
          
          tooltip += `<div style="margin-bottom:3px;">
            <span style="display:inline-block;width:10px;height:10px;background-color:${distanceColor};border-radius:50%;margin-right:5px;"></span>
            <span>价格${positionText}均线：${Math.abs(distance).toFixed(2)}</span>
          </div>`
        }
        
        return tooltip
      }
    },
    legend: {
      data: ['K线', ...selectedPeriods.map(period => `MA${period}`), '均线距离'],
      bottom: 10
    },
    grid: [
      {
        left: '10%',
        right: '10%',
        height: '60%', // 主图占60%的高度
        top: '10%'
      },
      {
        left: '10%',
        right: '10%',
        top: '75%', // 副图在主图下方
        height: '15%' // 副图占15%的高度
      }
    ],
    xAxis: [
      {
        type: 'category',
        data: props.dates,
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        splitLine: { show: false },
        splitNumber: 20,
        min: 'dataMin',
        max: 'dataMax',
        gridIndex: 0
      },
      {
        type: 'category',
        gridIndex: 1,
        data: props.dates,
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        splitLine: { show: false }
      }
    ],
    yAxis: [
      {
        scale: true,
        splitLine: { show: true },
        gridIndex: 0
      },
      {
        scale: true,
        gridIndex: 1,
        splitLine: { show: false },
        axisLabel: { show: true },
        axisLine: { show: false },
        axisTick: { show: false },
        name: '均线距离'
      }
    ],
    dataZoom: [
      {
        type: 'inside',
        xAxisIndex: [0, 1], // 同时缩放两个图表的x轴
        start: 0,
        end: 100
      },
      {
        show: true,
        type: 'slider',
        top: '92%',
        xAxisIndex: [0, 1], // 同时缩放两个图表的x轴
        start: 0,
        end: 100
      }
    ],
    series: [
      {
        name: 'K线',
        type: 'candlestick',
        data: props.klineData,
        itemStyle: {
          color: '#F56C6C',
          color0: '#67C23A',
          borderColor: '#F56C6C',
          borderColor0: '#67C23A'
        },
        xAxisIndex: 0,
        yAxisIndex: 0
      },
      ...maSeries,
      {
        name: '均线距离',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        data: distanceData,
        itemStyle: {
          // 根据距离是正数、负数或零决定颜色
          // 红色表示高于均线，绿色表示低于均线，灰色表示跨越均线
          color: (params) => {
            const value = params.data[2] // 使用第三个值来决定颜色
            if (value > 0) {
              return '#F56C6C' // 高于均线，红色
            } else if (value < 0) {
              return '#67C23A' // 低于均线，绿色
            } else {
              return '#909399' // 跨越均线，灰色
            }
          }
        },
        // 使用指定的方向（向上或向下）来显示柱状图
        barWidth: '70%'
      }
    ]
  }
  
  // 设置图表选项
  chartInstance.setOption(option, true)
  console.log('图表配置已更新')
}

// 新增：定义一个方法来返回 ECharts 实例
const getChartInstance = () => {
  return chartInstance; 
}

// 新增：使用 defineExpose 暴露该方法
defineExpose({
  getChartInstance,
  updateChart // 也可选择性暴露 updateChart 如果外部需要强制重绘图表
})

// 监听数据变化
watch(
  [() => props.dates, () => props.klineData, () => props.maData, () => props.periods, () => props.analysisData],
  () => {
    nextTick(() => {
      updateChart()
    })
  },
  { deep: true }
)

// 组件挂载时初始化图表
onMounted(() => {
  initChart()
})
</script>

<style scoped>
/* 可以添加自定义样式 */
</style> 