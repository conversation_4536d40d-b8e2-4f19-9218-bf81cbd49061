{"version": 3, "file": "dialog2.js", "sources": ["../../../../../../packages/components/dialog/src/dialog.vue"], "sourcesContent": ["<template>\n  <el-teleport\n    :to=\"appendTo\"\n    :disabled=\"appendTo !== 'body' ? false : !appendToBody\"\n  >\n    <transition\n      name=\"dialog-fade\"\n      @after-enter=\"afterEnter\"\n      @after-leave=\"afterLeave\"\n      @before-leave=\"beforeLeave\"\n    >\n      <el-overlay\n        v-show=\"visible\"\n        custom-mask-event\n        :mask=\"modal\"\n        :overlay-class=\"modalClass\"\n        :z-index=\"zIndex\"\n      >\n        <div\n          role=\"dialog\"\n          aria-modal=\"true\"\n          :aria-label=\"title || undefined\"\n          :aria-labelledby=\"!title ? titleId : undefined\"\n          :aria-describedby=\"bodyId\"\n          :class=\"`${ns.namespace.value}-overlay-dialog`\"\n          :style=\"overlayDialogStyle\"\n          @click=\"overlayEvent.onClick\"\n          @mousedown=\"overlayEvent.onMousedown\"\n          @mouseup=\"overlayEvent.onMouseup\"\n        >\n          <el-focus-trap\n            loop\n            :trapped=\"visible\"\n            focus-start-el=\"container\"\n            @focus-after-trapped=\"onOpenAutoFocus\"\n            @focus-after-released=\"onCloseAutoFocus\"\n            @focusout-prevented=\"onFocusoutPrevented\"\n            @release-requested=\"onCloseRequested\"\n          >\n            <el-dialog-content\n              v-if=\"rendered\"\n              ref=\"dialogContentRef\"\n              v-bind=\"$attrs\"\n              :center=\"center\"\n              :align-center=\"alignCenter\"\n              :close-icon=\"closeIcon\"\n              :draggable=\"draggable\"\n              :overflow=\"overflow\"\n              :fullscreen=\"fullscreen\"\n              :header-class=\"headerClass\"\n              :body-class=\"bodyClass\"\n              :footer-class=\"footerClass\"\n              :show-close=\"showClose\"\n              :title=\"title\"\n              :aria-level=\"headerAriaLevel\"\n              @close=\"handleClose\"\n            >\n              <template #header>\n                <slot\n                  v-if=\"!$slots.title\"\n                  name=\"header\"\n                  :close=\"handleClose\"\n                  :title-id=\"titleId\"\n                  :title-class=\"ns.e('title')\"\n                />\n                <slot v-else name=\"title\" />\n              </template>\n              <slot />\n              <template v-if=\"$slots.footer\" #footer>\n                <slot name=\"footer\" />\n              </template>\n            </el-dialog-content>\n          </el-focus-trap>\n        </div>\n      </el-overlay>\n    </transition>\n  </el-teleport>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, provide, ref, useSlots } from 'vue'\nimport { ElOverlay } from '@element-plus/components/overlay'\nimport { useDeprecated, useNamespace, useSameTarget } from '@element-plus/hooks'\nimport ElFocusTrap from '@element-plus/components/focus-trap'\nimport ElTeleport from '@element-plus/components/teleport'\nimport ElDialogContent from './dialog-content.vue'\nimport { dialogInjectionKey } from './constants'\nimport { dialogEmits, dialogProps } from './dialog'\nimport { useDialog } from './use-dialog'\n\ndefineOptions({\n  name: 'ElDialog',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(dialogProps)\ndefineEmits(dialogEmits)\nconst slots = useSlots()\n\nuseDeprecated(\n  {\n    scope: 'el-dialog',\n    from: 'the title slot',\n    replacement: 'the header slot',\n    version: '3.0.0',\n    ref: 'https://element-plus.org/en-US/component/dialog.html#slots',\n  },\n  computed(() => !!slots.title)\n)\n\nconst ns = useNamespace('dialog')\nconst dialogRef = ref<HTMLElement>()\nconst headerRef = ref<HTMLElement>()\nconst dialogContentRef = ref()\n\nconst {\n  visible,\n  titleId,\n  bodyId,\n  style,\n  overlayDialogStyle,\n  rendered,\n  zIndex,\n  afterEnter,\n  afterLeave,\n  beforeLeave,\n  handleClose,\n  onModalClick,\n  onOpenAutoFocus,\n  onCloseAutoFocus,\n  onCloseRequested,\n  onFocusoutPrevented,\n} = useDialog(props, dialogRef)\n\nprovide(dialogInjectionKey, {\n  dialogRef,\n  headerRef,\n  bodyId,\n  ns,\n  rendered,\n  style,\n})\n\nconst overlayEvent = useSameTarget(onModalClick)\n\nconst draggable = computed(() => props.draggable && !props.fullscreen)\n\nconst resetPosition = () => {\n  dialogContentRef.value?.resetPosition()\n}\n\ndefineExpose({\n  /** @description whether the dialog is visible */\n  visible,\n  dialogContentRef,\n  resetPosition,\n})\n</script>\n"], "names": ["useSlots", "useDeprecated", "computed", "useNamespace", "ref", "useDialog", "provide", "dialogInjectionKey", "useSameTarget", "_openBlock", "_createBlock", "_unref", "ElTeleport"], "mappings": ";;;;;;;;;;;;;;;;;uCA0Fc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAA,MAAM,QAAQA,YAAS,EAAA,CAAA;AAEvB,IAAAC,mBAAA,CAAA;AAAA,MACE,KAAA,EAAA,WAAA;AAAA,MAAA,IACS,EAAA,gBAAA;AAAA,MAAA,WACD,EAAA,iBAAA;AAAA,MAAA,OACO,EAAA,OAAA;AAAA,MAAA,GACJ,EAAA,4DAAA;AAAA,KAAA,EAAAC,YACJ,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACP,MAAA,EAAA,GAAAC,oBAAA,CAAA,QAAA,CAAA,CAAA;AAAA,IAAA,MACS,SAAA,GAAOC;AAAY,IAC9B,MAAA,SAAA,GAAAA,OAAA,EAAA,CAAA;AAEA,IAAM,MAAA,mBAA0BA,OAAA,EAAA,CAAA;AAChC,IAAA,MAAM;AACN,MAAA;AACA,MAAA;AAEA,MAAM,MAAA;AAAA,MACJ,KAAA;AAAA,MACA,kBAAA;AAAA,MACA,QAAA;AAAA,MACA,MAAA;AAAA,MACA,UAAA;AAAA,MACA,UAAA;AAAA,MACA,WAAA;AAAA,MACA,WAAA;AAAA,MACA,YAAA;AAAA,MACA,eAAA;AAAA,MACA,gBAAA;AAAA,MACA,gBAAA;AAAA,MACA,mBAAA;AAAA,KACA,GAAAC,mBAAA,CAAA,KAAA,EAAA,SAAA,CAAA,CAAA;AAAA,IACAC,WAAA,CAAAC,4BAAA,EAAA;AAAA,MACA,SAAA;AAAA,MACF,SAAc;AAEd,MAAA,MAAQ;AAAoB,MAC1B,EAAA;AAAA,MACA,QAAA;AAAA,MACA,KAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,MAAA,YAAA,GAAAC,qBAAA,CAAA,YAAA,CAAA,CAAA;AAAA,IACA,MAAA,SAAA,GAAAN,YAAA,CAAA,MAAA,KAAA,CAAA,SAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAAA,IACF,MAAC,aAAA,GAAA,MAAA;AAED,MAAM,IAAA,EAAA,CAAA;AAEN,MAAA,CAAA,EAAA,mBAA2B,CAAA,KAAA,SAAY,GAAa,KAAA,CAAA,GAAA,EAAC,cAAgB,EAAA,CAAA;AAErE,KAAA,CAAA;AACE,IAAA,MAAA,CAAA;AAAsC,MACxC,OAAA;AAEA,MAAa,gBAAA;AAAA,MAAA,aAAA;AAAA,KAEX,CAAA,CAAA;AAAA,IACA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACA,OAAAO,aAAA,EAAA,EAAAC,eAAA,CAAAC,SAAA,CAAAC,kBAAA,CAAA,EAAA;AAAA,QACD,EAAA,EAAA,IAAA,CAAA,QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}