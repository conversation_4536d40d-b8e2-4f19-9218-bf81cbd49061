{"version": 3, "file": "defaults.js", "sources": ["../../../../../../packages/components/virtual-list/src/defaults.ts"], "sourcesContent": ["export const DEFAULT_DYNAMIC_LIST_ITEM_SIZE = 50\n\nexport const ITEM_RENDER_EVT = 'itemRendered'\nexport const SCROLL_EVT = 'scroll'\n\nexport const FORWARD = 'forward'\nexport const BACKWARD = 'backward'\n\nexport const AUTO_ALIGNMENT = 'auto'\nexport const SMART_ALIGNMENT = 'smart'\nexport const START_ALIGNMENT = 'start'\nexport const CENTERED_ALIGNMENT = 'center'\nexport const END_ALIGNMENT = 'end'\n\nexport const HORIZONTAL = 'horizontal'\nexport const VERTICAL = 'vertical'\n\nexport const LTR = 'ltr'\nexport const RTL = 'rtl'\n\nexport const RTL_OFFSET_NAG = 'negative'\nexport const RTL_OFFSET_POS_ASC = 'positive-ascending'\nexport const RTL_OFFSET_POS_DESC = 'positive-descending'\n\nexport const PageKey = {\n  [HORIZONTAL]: 'pageX',\n  [VERTICAL]: 'pageY',\n}\n\nexport const ScrollbarSizeKey = {\n  [HORIZONTAL]: 'height',\n  [VERTICAL]: 'width',\n}\n\nexport const ScrollbarDirKey = {\n  [HORIZONTAL]: 'left',\n  [VERTICAL]: 'top',\n}\n\nexport const SCROLLBAR_MIN_SIZE = 20\n"], "names": [], "mappings": ";;;;AAAY,MAAC,8BAA8B,GAAG,GAAG;AACrC,MAAC,eAAe,GAAG,eAAe;AAClC,MAAC,UAAU,GAAG,SAAS;AACvB,MAAC,OAAO,GAAG,UAAU;AACrB,MAAC,QAAQ,GAAG,WAAW;AACvB,MAAC,cAAc,GAAG,OAAO;AACzB,MAAC,eAAe,GAAG,QAAQ;AAC3B,MAAC,eAAe,GAAG,QAAQ;AAC3B,MAAC,kBAAkB,GAAG,SAAS;AAC/B,MAAC,aAAa,GAAG,MAAM;AACvB,MAAC,UAAU,GAAG,aAAa;AAC3B,MAAC,QAAQ,GAAG,WAAW;AACvB,MAAC,GAAG,GAAG,MAAM;AACb,MAAC,GAAG,GAAG,MAAM;AACb,MAAC,cAAc,GAAG,WAAW;AAC7B,MAAC,kBAAkB,GAAG,qBAAqB;AAC3C,MAAC,mBAAmB,GAAG,sBAAsB;AAC7C,MAAC,OAAO,GAAG;AACvB,EAAE,CAAC,UAAU,GAAG,OAAO;AACvB,EAAE,CAAC,QAAQ,GAAG,OAAO;AACrB,EAAE;AACU,MAAC,gBAAgB,GAAG;AAChC,EAAE,CAAC,UAAU,GAAG,QAAQ;AACxB,EAAE,CAAC,QAAQ,GAAG,OAAO;AACrB,EAAE;AACU,MAAC,eAAe,GAAG;AAC/B,EAAE,CAAC,UAAU,GAAG,MAAM;AACtB,EAAE,CAAC,QAAQ,GAAG,KAAK;AACnB,EAAE;AACU,MAAC,kBAAkB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;"}