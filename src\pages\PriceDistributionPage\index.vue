<template>
  <div class="price-distribution-container">
    <!-- 查询表单 -->
    <el-card class="query-card">
      <el-form :model="queryForm" inline>
        <el-form-item label="股票代码" required>
          <el-input 
            v-model="queryForm.stock_code" 
            placeholder="请输入股票代码，如：000001"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="日期范围" required>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
          />
        </el-form-item>
        
        <el-form-item label="价格区间间隔">
          <el-input-number 
            v-model="queryForm.price_interval" 
            :min="0.01" 
            :max="100"
            :step="0.01"
            :precision="2"
          />
        </el-form-item>
        
        <el-form-item label="涨跌幅区间间隔">
          <el-input-number 
            v-model="queryForm.change_pct_interval" 
            :min="0.01" 
            :max="5"
            :step="0.01"
            :precision="2"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="fetchDistributionData" :loading="loading">
            查询
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 图表展示 -->
    <template v-if="hasData">
      <!-- 价格区间选择 -->
      <el-card class="filter-card">
        <div class="filter-header">
          <h3>价格区间选择</h3>
        </div>
        <div class="filter-content">
          <p v-if="priceRanges.length > 1">请选择要显示的价格区间：</p>
          <p v-else>当前仅有一个价格区间可供显示</p>
          
          <el-checkbox-group v-model="selectedPriceRanges">
            <el-checkbox 
              v-for="range in priceRanges" 
              :key="range" 
              :label="range"
            >
              {{ range }}元
            </el-checkbox>
          </el-checkbox-group>
          
          <div class="filter-actions" v-if="priceRanges.length > 1">
            <el-button size="small" type="primary" @click="selectAllPriceRanges">全选</el-button>
            <el-button size="small" @click="clearPriceRangeSelection">清空</el-button>
          </div>
        </div>
      </el-card>
      
      <!-- 柱状图 -->
      <el-card class="chart-card">
        <div class="chart-header">
          <h3>
            {{ queryForm.stock_code }} 股票涨跌幅分布 
            ({{ queryForm.start_date }} 至 {{ queryForm.end_date }})
          </h3>
        </div>
        <el-tabs v-model="activeChartTab">
          <el-tab-pane label="总体分布" name="overall">
            <div class="chart-wrapper">
              <div ref="chartRef" class="echarts-container"></div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="分布直方图" name="histogram">
            <div class="histogram-tabs" v-if="selectedPriceRanges.length > 0">
              <el-tabs v-model="activeHistogramTab">
                <el-tab-pane 
                  v-for="range in selectedPriceRanges" 
                  :key="range" 
                  :label="range + '元'" 
                  :name="range"
                >
                  <div class="histogram-wrapper">
                    <div :id="'histogram-' + range" class="histogram-container"></div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
            <el-empty v-else description="请先选择价格区间"></el-empty>
          </el-tab-pane>
        </el-tabs>
      </el-card>
      
      <!-- 统计信息表格 -->
      <el-card class="statistics-card">
        <div class="statistics-header">
          <h3>统计信息</h3>
        </div>
        <el-table :data="filteredStatistics" stripe border>
          <el-table-column prop="priceRange" label="价格区间" width="120" />
          <el-table-column prop="minPrice" label="最低价格" width="120">
            <template #default="scope">
              <span v-if="scope.row.minPrice === '不适用'">{{ scope.row.minPrice }}</span>
              <span v-else>{{ scope.row.minPrice }}元</span>
            </template>
          </el-table-column>
          <el-table-column prop="maxPrice" label="最高价格" width="120">
            <template #default="scope">
              <span v-if="scope.row.maxPrice === '不适用'">{{ scope.row.maxPrice }}</span>
              <span v-else>{{ scope.row.maxPrice }}元</span>
            </template>
          </el-table-column>
          <el-table-column prop="avgPrice" label="平均价格" width="120">
            <template #default="scope">
              <span v-if="scope.row.avgPrice === '不适用'">{{ scope.row.avgPrice }}</span>
              <span v-else>{{ scope.row.avgPrice }}元</span>
            </template>
          </el-table-column>
          <el-table-column prop="dataCount" label="数据点数" width="120" />
          <el-table-column prop="avgChangePct" label="平均涨跌幅" width="120">
            <template #default="scope">{{ scope.row.avgChangePct }}%</template>
          </el-table-column>
          <el-table-column prop="maxChangePct" label="最大涨跌幅" width="120">
            <template #default="scope">{{ scope.row.maxChangePct }}%</template>
          </el-table-column>
          <el-table-column prop="minChangePct" label="最小涨跌幅" width="120">
            <template #default="scope">{{ scope.row.minChangePct }}%</template>
          </el-table-column>
          <el-table-column prop="upDays" label="上涨天数" width="100" />
          <el-table-column prop="downDays" label="下跌天数" width="100" />
          <el-table-column prop="upDownRatio" label="上涨比例" width="100" />
          <el-table-column prop="totalDays" label="总交易天数" width="120" />
        </el-table>
      </el-card>
      
      <!-- 详细分布数据 -->
      <el-card class="detail-card" v-if="chartData.rawData">
        <div class="detail-header">
          <h3>详细涨跌幅分布数据</h3>
        </div>
        
        <el-tabs v-model="activePriceRange" class="detail-tabs">
          <el-tab-pane 
            v-for="priceRange in priceRanges" 
            :key="priceRange" 
            :label="priceRange + '元价格区间'" 
            :name="priceRange"
          >
            <div class="range-info" v-if="getRangeInfo(priceRange)">
              <h4>价格区间基本信息</h4>
              <el-descriptions :column="2" border size="small">
                <el-descriptions-item label="最低价格">{{ getRangeInfo(priceRange).min_price }}元</el-descriptions-item>
                <el-descriptions-item label="最高价格">{{ getRangeInfo(priceRange).max_price }}元</el-descriptions-item>
                <el-descriptions-item label="平均价格">{{ getRangeInfo(priceRange).avg_price.toFixed(2) }}元</el-descriptions-item>
                <el-descriptions-item label="数据点数">{{ getRangeInfo(priceRange).data_count }}个</el-descriptions-item>
                <el-descriptions-item label="最小涨跌幅">{{ chartData.rawData.price_ranges[priceRange].min_change_pct }}%</el-descriptions-item>
                <el-descriptions-item label="最大涨跌幅">{{ chartData.rawData.price_ranges[priceRange].max_change_pct }}%</el-descriptions-item>
                <el-descriptions-item label="平均涨跌幅">{{ chartData.rawData.price_ranges[priceRange].avg_change_pct.toFixed(2) }}%</el-descriptions-item>
                <el-descriptions-item label="总交易天数">{{ chartData.rawData.price_ranges[priceRange].total_days }}天</el-descriptions-item>
              </el-descriptions>
            </div>
            
            <div class="distribution-table">
              <h4>涨跌幅分布详情</h4>
              <el-table 
                :data="getDistributionData(priceRange)" 
                stripe 
                border 
                size="small"
                :default-sort="{prop: 'range', order: 'ascending'}"
                style="width: 100%"
              >
                <el-table-column prop="range" label="涨跌幅区间" sortable width="150">
                  <template #default="scope">{{ scope.row.range }}%</template>
                </el-table-column>
                <el-table-column prop="count" label="出现次数" sortable width="150" />
                <el-table-column prop="percentage" label="占比" sortable width="150" />
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
        
        <!-- 原始JSON数据 -->
        <el-collapse>
          <el-collapse-item title="查看原始数据">
            <pre class="json-data">{{ JSON.stringify(chartData.rawData, null, 2) }}</pre>
          </el-collapse-item>
        </el-collapse>
      </el-card>
    </template>
    
    <!-- 无数据或错误提示 -->
    <el-empty v-else description="暂无数据，请查询涨跌幅分布数据"></el-empty>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { getPriceChangePctDistribution, formatDistributionData } from '@/api/priceAnalysis'
import { ElMessage } from 'element-plus'

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
  {
    text: '最近六个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
      return [start, end]
    },
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
      return [start, end]
    },
  },
]

// 查询表单
const queryForm = reactive({
  stock_code: '',
  start_date: '',
  end_date: '',
  price_interval: 10.0,
  change_pct_interval: 1.0
})

// 日期范围
const dateRange = ref([])

// 监听日期范围变化
watch(dateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    queryForm.start_date = newValue[0]
    queryForm.end_date = newValue[1]
  } else {
    queryForm.start_date = ''
    queryForm.end_date = ''
  }
})

// 加载状态
const loading = ref(false)

// 图表元素引用
const chartRef = ref(null)

// 图表实例
let chartInstance = null

// 原始数据
const distributionData = ref(null)

// 处理后的数据
const chartData = ref({
  priceRanges: [],
  changePctRanges: [],
  series: [],
  statistics: [],
  rawData: null
})

// 已选择的价格区间
const selectedPriceRanges = ref([])

// 当前激活的价格区间标签页
const activePriceRange = ref('')

// 当前激活的图表标签页
const activeChartTab = ref('overall')

// 当前激活的直方图标签页
const activeHistogramTab = ref('')

// 是否有数据
const hasData = computed(() => distributionData.value && (distributionData.value.distribution || 
  (distributionData.value.price_ranges && Object.keys(distributionData.value.price_ranges).length > 0)))

// 价格区间列表
const priceRanges = computed(() => chartData.value.priceRanges || [])

// 筛选后的统计信息
const filteredStatistics = computed(() => {
  if (!chartData.value.statistics) return []
  return chartData.value.statistics.filter(item => 
    selectedPriceRanges.value.includes(item.priceRange)
  )
})

// 获取指定价格区间的涨跌幅分布数据
const getDistributionData = (priceRange) => {
  if (!chartData.value.rawData) return [];
  
  const rawData = chartData.value.rawData;
  
  // 检查新格式数据
  if (rawData.price_ranges && rawData.price_ranges[priceRange]) {
    const rangeData = rawData.price_ranges[priceRange];
    const distributions = rangeData.distributions || {};
    const totalDays = rangeData.total_days || 0;
    
    // 转换为表格数据
    return Object.entries(distributions)
      .map(([range, count]) => ({
        range,
        count,
        percentage: totalDays > 0 ? ((count / totalDays) * 100).toFixed(2) + '%' : '0.00%',
        // 添加一个数值字段用于排序
        rangeValue: parseFloat(range.split('-')[0])
      }))
      .sort((a, b) => b.rangeValue - a.rangeValue); // 按照区间起始值从大到小排序
  }
  
  // 旧格式数据
  if (priceRange === '全部' && rawData.distribution) {
    const distributions = rawData.distribution || {};
    const totalDays = rawData.total_days || 0;
    
    return Object.entries(distributions)
      .map(([range, count]) => ({
        range,
        count,
        percentage: totalDays > 0 ? ((count / totalDays) * 100).toFixed(2) + '%' : '0.00%',
        // 添加一个数值字段用于排序
        rangeValue: parseFloat(range.split('-')[0])
      }))
      .sort((a, b) => b.rangeValue - a.rangeValue); // 按照区间起始值从大到小排序
  }
  
  return [];
};

// 获取价格区间信息
const getRangeInfo = (priceRange) => {
  if (!chartData.value.rawData) return null;
  
  const rawData = chartData.value.rawData;
  
  // 新格式数据
  if (rawData.price_ranges && rawData.price_ranges[priceRange]) {
    return rawData.price_ranges[priceRange].price_range_info || null;
  }
  
  return null;
};

// 全选价格区间
const selectAllPriceRanges = () => {
  selectedPriceRanges.value = [...priceRanges.value];
  // 更新图表
  nextTick(() => {
    updateChartData();
  });
};

// 清空价格区间选择
const clearPriceRangeSelection = () => {
  selectedPriceRanges.value = [];
  // 更新图表
  nextTick(() => {
    updateChartData();
  });
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  // 销毁旧实例
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  // 创建新实例
  chartInstance = echarts.init(chartRef.value)
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
  
  // 初始显示加载中
  chartInstance.showLoading()
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 重置表单
const resetForm = () => {
  queryForm.stock_code = ''
  dateRange.value = []
  queryForm.start_date = ''
  queryForm.end_date = ''
  queryForm.price_interval = 10.0
  queryForm.change_pct_interval = 1.0
  
  distributionData.value = null
  chartData.value = {
    priceRanges: [],
    changePctRanges: [],
    series: [],
    statistics: [],
    rawData: null
  }
  selectedPriceRanges.value = []
  activePriceRange.value = ''
  activeHistogramTab.value = ''
  activeChartTab.value = 'overall'
  
  if (chartInstance) {
    chartInstance.clear()
    chartInstance.showLoading()
  }
}

// 查询涨跌幅分布数据
const fetchDistributionData = async () => {
  // 表单验证
  if (!queryForm.stock_code) {
    ElMessage.warning('请输入股票代码')
    return
  }
  
  if (!queryForm.start_date || !queryForm.end_date) {
    ElMessage.warning('请选择日期范围')
    return
  }
  
  // 价格区间和涨跌幅区间验证
  if (queryForm.price_interval <= 0) {
    ElMessage.warning('价格区间必须大于0')
    return
  }
  
  if (queryForm.change_pct_interval <= 0) {
    ElMessage.warning('涨跌幅区间必须大于0')
    return
  }
  
  loading.value = true
  
  try {
    console.log('开始获取涨跌幅分布数据...');
    console.log('查询参数:', {
      stock_code: queryForm.stock_code,
      start_date: queryForm.start_date,
      end_date: queryForm.end_date,
      price_interval: queryForm.price_interval,
      change_pct_interval: queryForm.change_pct_interval
    });
    
    // 显示加载中提示
    if (chartInstance) {
      chartInstance.showLoading({
        text: '数据加载中...',
        color: '#1890ff',
        textColor: '#000',
        maskColor: 'rgba(255, 255, 255, 0.8)',
      })
    }
    
    const response = await getPriceChangePctDistribution(queryForm)
    console.log('获取到的原始分布数据:', response);
    
    distributionData.value = response
    
    // 使用API中的formatDistributionData函数处理数据
    console.log('准备格式化分布数据...');
    chartData.value = formatDistributionData(response)
    console.log('格式化后的图表数据:', chartData.value);
    
    // 如果处理后没有数据，说明数据格式可能有问题
    if (!chartData.value.priceRanges || chartData.value.priceRanges.length === 0) {
      ElMessage.warning('没有找到可显示的数据，请调整查询条件')
      loading.value = false
      return
    }
    
    // 默认选中所有价格区间
    selectedPriceRanges.value = [...chartData.value.priceRanges]
    console.log('默认选中的价格区间:', selectedPriceRanges.value);
    
    // 设置默认激活的价格区间标签页
    if (chartData.value.priceRanges.length > 0) {
      activePriceRange.value = chartData.value.priceRanges[0];
    }
    
    // 更新图表
    nextTick(() => {
      console.log('DOM更新后准备更新图表');
      updateChartData()
      
      // 成功后显示提示
      ElMessage.success(`已成功加载 ${queryForm.stock_code} 的价格分布数据`)
    });
    
  } catch (error) {
    console.error('获取涨跌幅分布数据失败:', error)
    ElMessage.error(`获取数据失败: ${error.message || '未知错误'}`)
    distributionData.value = null
    chartData.value = {
      priceRanges: [],
      changePctRanges: [],
      series: [],
      statistics: [],
      rawData: null
    }
    selectedPriceRanges.value = []
  } finally {
    loading.value = false
    // 确保图表不再显示加载状态
    if (chartInstance) {
      chartInstance.hideLoading()
    }
  }
}

// 更新图表数据
const updateChartData = () => {
  if (!chartInstance || !chartData.value.series.length) {
    console.error('无法更新图表：图表实例不存在或系列数据为空');
    return;
  }
  
  console.log('更新图表，当前数据:', chartData.value);
  
  // 筛选选中的价格区间
  const filteredSeries = chartData.value.series.filter(series => {
    const seriesName = series.name.replace('价格', '').trim();
    const isSelected = selectedPriceRanges.value.includes(seriesName);
    console.log(`系列名称: ${series.name}, 净系列名: ${seriesName}, 是否选中: ${isSelected}`);
    return isSelected;
  });
  
  console.log('筛选后的系列数据:', filteredSeries);
  
  if (filteredSeries.length === 0 && chartData.value.series.length > 0) {
    ElMessage.warning('没有选中任何价格区间，请至少选择一个价格区间');
    return;
  }
  
  // 为每个系列设置不同的颜色
  const colorPalette = [
    '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
    '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#b07f5c'
  ];
  
  filteredSeries.forEach((series, index) => {
    series.itemStyle = {
      color: colorPalette[index % colorPalette.length]
    };
    
    // 添加标签
    series.label = {
      show: false,
      position: 'top',
      formatter: '{c}'
    };
    
    // 添加鼠标悬停效果
    series.emphasis = {
      focus: 'series',
      label: {
        show: true
      }
    };
  });
  
  // 设置图表配置
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const changePctRange = params[0].axisValue;
        let result = `<div style="font-weight:bold;margin-bottom:5px;">涨跌幅区间: ${changePctRange}</div>`;
        
        params.forEach(param => {
          // 添加对应价格区间的颜色方块
          const colorSpan = `<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background-color:${param.color};"></span>`;
          result += `<div>${colorSpan}${param.seriesName}: ${param.value} 次</div>`;
        });
        
        return result;
      }
    },
    legend: {
      type: 'scroll',
      data: filteredSeries.map(item => item.name),
      top: 10,
      selected: filteredSeries.reduce((acc, item) => {
        acc[item.name] = true;
        return acc;
      }, {})
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          yAxisIndex: 'none'
        },
        magicType: { type: ['line', 'bar'] },
        restore: {},
        saveAsImage: {}
      }
    },
    xAxis: {
      type: 'category',
      data: chartData.value.changePctRanges,
      name: '涨跌幅区间(%)',
      axisLabel: {
        interval: 0,
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '出现次数',
      nameTextStyle: {
        padding: [0, 30, 0, 0]
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        show: true,
        type: 'slider',
        start: 0,
        end: 100
      }
    ],
    series: filteredSeries
  };
  
  console.log('图表配置:', option);
  
  chartInstance.hideLoading();
  chartInstance.setOption(option, true);
  console.log('图表已更新');
}

// 监听已选择的价格区间变化，更新图表
watch(selectedPriceRanges, (newValue) => {
  if (chartInstance && chartData.value.series.length > 0) {
    // 如果所有价格区间都被取消选择，显示警告但不更新图表
    if (newValue.length === 0) {
      ElMessage.warning('至少需要选择一个价格区间');
      // 如果没有选中任何价格区间，自动选择第一个
      if (priceRanges.value.length > 0) {
        selectedPriceRanges.value = [priceRanges.value[0]];
      }
      return;
    }
    
    // 更新图表
    nextTick(() => {
      updateChartData();
    });
  }
});

// 在 script setup 部分添加直方图实例管理
const histogramInstances = ref({});

// 更新初始化直方图的函数
const initHistogram = (priceRange) => {
  if (!priceRange) return;
  
  nextTick(() => {
    const chartDom = document.getElementById('histogram-' + priceRange);
    if (!chartDom) return;
    
    // 如果已存在实例，先销毁
    if (histogramInstances.value[priceRange]) {
      histogramInstances.value[priceRange].dispose();
    }
    
    // 创建新实例
    const histogramChart = echarts.init(chartDom);
    histogramInstances.value[priceRange] = histogramChart;
    
    const data = getDistributionData(priceRange);
    
    const option = {
      title: {
        text: `${priceRange}元价格区间涨跌幅分布`,
        left: 'center',
        top: 10
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params) {
          const item = params[0];
          return `涨跌幅区间: ${item.name}<br/>
                  出现次数: ${item.value}<br/>
                  占比: ${item.data.percentage}`;
        }
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.range),
        name: '涨跌幅区间(%)',
        nameLocation: 'middle',
        nameGap: 35,
        axisLabel: {
          interval: 0,
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: '出现次数',
        nameLocation: 'middle',
        nameGap: 40
      },
      series: [{
        name: '出现次数',
        type: 'bar',
        data: data.map(item => ({
          value: item.count,
          percentage: item.percentage
        })),
        itemStyle: {
          color: '#409EFF'
        },
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          formatter: '{c}'
        }
      }]
    };
    
    histogramChart.setOption(option);
    
    // 添加resize事件监听
    const handleResize = () => {
      histogramChart.resize();
    };
    window.addEventListener('resize', handleResize);
    
    // 在组件卸载时移除事件监听
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize);
    });
  });
};

// 更新监听直方图标签页变化的逻辑
watch(activeHistogramTab, (newRange) => {
  if (newRange) {
    initHistogram(newRange);
  }
});

// 更新组件卸载时的清理逻辑
onUnmounted(() => {
  // 清理所有直方图实例
  Object.values(histogramInstances.value).forEach(instance => {
    if (instance) {
      instance.dispose();
    }
  });
  histogramInstances.value = {};
  
  // 清理K线图实例
  if (chartInstance) {
    window.removeEventListener('resize', handleResize);
    chartInstance.dispose();
    chartInstance = null;
  }
});

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped>
.price-distribution-container {
  padding: 20px;
}

.query-card,
.filter-card,
.chart-card,
.statistics-card,
.detail-card {
  margin-bottom: 20px;
}

.filter-header,
.chart-header,
.statistics-header,
.detail-header {
  margin-bottom: 15px;
}

.filter-header h3,
.chart-header h3,
.statistics-header h3,
.detail-header h3 {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-content p {
  margin: 0 0 5px 0;
}

.filter-content .el-checkbox {
  margin-right: 20px;
  margin-bottom: 10px;
}

.filter-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.chart-card :deep(.el-tabs__content) {
  padding: 15px 0;
}

.chart-wrapper {
  position: relative;
  margin-top: 15px;
}

.echarts-container {
  width: 100%;
  height: 400px;
}

.detail-tabs {
  margin-bottom: 20px;
}

.range-info {
  margin-top: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.range-info h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #409EFF;
}

.distribution-table {
  margin-top: 20px;
  margin-bottom: 20px;
}

.distribution-table h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #409EFF;
}

.json-data {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.histogram-tabs {
  margin-top: 15px;
}

.histogram-tabs :deep(.el-tabs__content) {
  padding: 15px 0;
}

.histogram-wrapper {
  width: 100%;
  height: 400px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.histogram-container {
  width: 100%;
  height: 100%;
}
</style> 