{"version": 3, "file": "option-group.mjs", "sources": ["../../../../../../packages/components/select/src/option-group.vue"], "sourcesContent": ["<template>\n  <ul v-show=\"visible\" ref=\"groupRef\" :class=\"ns.be('group', 'wrap')\">\n    <li :class=\"ns.be('group', 'title')\">{{ label }}</li>\n    <li>\n      <ul :class=\"ns.b('group')\">\n        <slot />\n      </ul>\n    </li>\n  </ul>\n</template>\n\n<script lang=\"ts\">\n// @ts-nocheck\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  toRefs,\n} from 'vue'\nimport { useMutationObserver } from '@vueuse/core'\nimport { ensureArray } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { selectGroupKey } from './token'\n\nexport default defineComponent({\n  name: 'ElOptionGroup',\n  componentName: 'ElOptionGroup',\n\n  props: {\n    /**\n     * @description name of the group\n     */\n    label: String,\n    /**\n     * @description whether to disable all options in this group\n     */\n    disabled: Boolean,\n  },\n  setup(props) {\n    const ns = useNamespace('select')\n    const groupRef = ref(null)\n    const instance = getCurrentInstance()\n    const children = ref([])\n\n    provide(\n      selectGroupKey,\n      reactive({\n        ...toRefs(props),\n      })\n    )\n\n    const visible = computed(() =>\n      children.value.some((option) => option.visible === true)\n    )\n\n    const isOption = (node) =>\n      node.type?.name === 'ElOption' && !!node.component?.proxy\n\n    // get all instances of options\n    const flattedChildren = (node) => {\n      const Nodes = ensureArray(node)\n      const children = []\n\n      Nodes.forEach((child) => {\n        if (isOption(child)) {\n          children.push(child.component.proxy)\n        } else if (child.children?.length) {\n          children.push(...flattedChildren(child.children))\n        } else if (child.component?.subTree) {\n          children.push(...flattedChildren(child.component.subTree))\n        }\n      })\n\n      return children\n    }\n\n    const updateChildren = () => {\n      children.value = flattedChildren(instance.subTree)\n    }\n\n    onMounted(() => {\n      updateChildren()\n    })\n\n    useMutationObserver(groupRef, updateChildren, {\n      attributes: true,\n      subtree: true,\n      childList: true,\n    })\n\n    return {\n      groupRef,\n      visible,\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["ensureArray", "children", "_withDirectives", "_openBlock", "_createElementBlock", "_normalizeClass", "_createElementVNode", "_renderSlot", "_vShow"], "mappings": ";;;;;;;AA4BA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,eAAA;AAAA,EACN,aAAe,EAAA,eAAA;AAAA,EAEf,KAAO,EAAA;AAAA,IAAA,KAAA,EAAA,MAAA;AAAA,IAAA,QAAA,EAAA,OAAA;AAAA,GAAA;AAAA,EAAA,KAIE,CAAA,KAAA,EAAA;AAAA,IAAA,MAAA,EAAA,GAAA,YAAA,CAAA,QAAA,CAAA,CAAA;AAAA,IAAA,MAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA;AAAA,IAAA,MAAA,QAAA,GAAA,kBAAA,EAAA,CAAA;AAAA,IAIP,MAAU,QAAA,GAAA,GAAA,CAAA,EAAA,CAAA,CAAA;AAAA,IACZ,OAAA,CAAA,cAAA,EAAA,QAAA,CAAA;AAAA,SACa,MAAA,CAAA,KAAA,CAAA;AACX,KAAM,CAAA,CAAA,CAAA;AACN,IAAM,MAAA,OAAA,GAAA,QAAmB,CAAA,MAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,MAAA,KAAA,MAAA,CAAA,OAAA,KAAA,IAAA,CAAA,CAAA,CAAA;AACzB,IAAA,MAAM,WAAW,CAAmB,IAAA,KAAA;AACpC,MAAM,IAAA,EAAA,EAAA,EAAA,CAAA;AAEN,MAAA,OAAA,CAAA,CAAA,EAAA,GAAA,IAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,MAAA,UAAA,IAAA,CAAA,EAAA,CAAA,EAAA,GAAA,IAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KACE,CAAA;AAAA,IAAA,MACS,eAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACP,cAAeA,SAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MACjB,MAAC,SAAA,GAAA,EAAA,CAAA;AAAA,MACH,KAAA,CAAA,OAAA,CAAA,CAAA,KAAA,KAAA;AAEA,QAAA,IAAgB,EAAA,EAAA,EAAA,CAAA;AAAA,QAAS,IACvB,SAAS,KAAM,CAAA,EAAA;AAAwC,UACzD,SAAA,CAAA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA;AAEA,SAAM,MAAA,IAAA,CAAA,EAAY,GAAA,KAAA,CAChB,QAAK,YAAe,KAAc,CAAA,GAAA,EAAA,CAAA,MAAE,EAAA;AAGtC,UAAM,SAAA,CAAA,IAAA,CAAA,GAAkB,eAAU,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAChC,SAAM,MAAA,IAAA,CAAQ,oBAAgB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,EAAA;AAC9B,UAAA,cAAiB,CAAC,GAAA,eAAA,CAAA,KAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AAElB,SAAM;AACJ,OAAI,CAAA,CAAA;AACF,MAAA,OAAS,SAAA,CAAA;AAA0B,KACrC,CAAA;AACE,IAAA,MAAAC,cAAc,GAAA,MAAmB;AAAe,MAClD,QAAA,CAAA,KAAiB,GAAA,eAAoB,CAAA,QAAA,CAAA,OAAA,CAAA,CAAA;AACnC,KAAA,CAAA;AAAyD,IAC3D,SAAA,CAAA,MAAA;AAAA,MACF,cAAC,EAAA,CAAA;AAED,KAAOA,CAAAA,CAAAA;AAAA,IACT,mBAAA,CAAA,QAAA,EAAA,cAAA,EAAA;AAEA,MAAA;AACE,MAAS,OAAA,EAAA,IAAA;AAAwC,MACnD,SAAA,EAAA,IAAA;AAEA,KAAA,CAAA,CAAA;AACE,IAAe,OAAA;AAAA,MAChB,QAAA;AAED,MAAA,OAAA;AAA8C,MAC5C,EAAY;AAAA,KAAA,CACZ;AAAS,GAAA;AACE,CAAA,CAAA,CAAA;AAGN,SACL,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EACA,OAAAC,cAAA,EAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,IAAA,EAAA;AAAA,IACA,GAAA,EAAA,UAAA;AAAA,IACF,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;AAAA,GACF,EAAA;AACF,IAACC,kBAAA,CAAA,IAAA,EAAA;;;AAnGC,IAAAA,kBAAA,CAAA,IAAA,EAAA,IAAA,EAAA;AAAA,MAOKA,kBAAA,CAAA,IAAA,EAAA;AAAA,QAAA,KAAA,EAAAD,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AAAA,OAPoB,EAAA;AAAA,QAAYE,UAAO,CAAA,IAAA,CAAA,MAAA,EAAA,SAAK,CAAA;AAAA,OAAA,EAAA,CAAA,CAAA;;AAC/C,GAAA,EAAA,CAAA,CAAA,GAAA;AAAA,IAAqD,CAAAC,KAAA,EAAA,IAAA,CAAA,OAAA,CAAA;AAAA,GAAA,CAAA,CAAA;AAAA,CAAhD;kCAAwC,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,kBAAA,CAAA,CAAA,CAAA;;;;"}