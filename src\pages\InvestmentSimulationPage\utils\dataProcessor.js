/**
 * 投资模拟数据处理工具
 * 负责处理和格式化投资模拟数据
 */

/**
 * 从模拟结果中提取交易明细
 * @param {Object} simulationResult 模拟结果
 * @returns {Array} 处理后的交易明细列表
 */
export function extractTransactionList(simulationResult) {
  if (!simulationResult || !simulationResult.transaction_list) {
    return []
  }

  // 转换交易明细格式
  return simulationResult.transaction_list.map(transaction => {
    return {
      id: transaction.id,
      date: transaction.date,
      type: transaction.type === 'buy' ? '买入' : '卖出',
      price: transaction.price.toFixed(2),
      amount: transaction.amount.toFixed(2),
      value: transaction.value.toFixed(2),
      hold_days: transaction.hold_days || 0
    }
  })
}

/**
 * 从模拟结果中提取基本统计信息
 * @param {Object} simulationResult 模拟结果
 * @returns {Object} 包含各种统计信息的对象
 */
export function extractStatistics(simulationResult) {
  if (!simulationResult) {
    return {
      totalProfit: 0,
      profitRate: 0,
      annualizedReturn: 0,
      transactionCount: 0,
      winCount: 0,
      lossCount: 0,
      winRate: 0,
      avgHoldDays: 0,
      maxDrawdown: 0
    }
  }

  const statistics = {
    // 总收益
    totalProfit: simulationResult.total_profit ? parseFloat(simulationResult.total_profit).toFixed(2) : '0.00',
    // 收益率
    profitRate: simulationResult.profit_rate ? (simulationResult.profit_rate * 100).toFixed(2) : '0.00',
    // 年化收益率
    annualizedReturn: simulationResult.annualized_return ? (simulationResult.annualized_return * 100).toFixed(2) : '0.00',
    // 交易次数
    transactionCount: simulationResult.transaction_count || 0,
    // 盈利次数
    winCount: simulationResult.win_count || 0,
    // 亏损次数
    lossCount: simulationResult.loss_count || 0,
    // 胜率
    winRate: simulationResult.win_rate ? (simulationResult.win_rate * 100).toFixed(2) : '0.00',
    // 平均持仓天数
    avgHoldDays: simulationResult.avg_hold_days ? parseFloat(simulationResult.avg_hold_days).toFixed(1) : '0.0',
    // 最大回撤
    maxDrawdown: simulationResult.max_drawdown ? (simulationResult.max_drawdown * 100).toFixed(2) : '0.00'
  }

  return statistics
}

/**
 * 格式化投资模拟表单数据
 * @param {Object} formData 表单数据
 * @returns {Object} 处理后的表单数据，适合发送API请求
 */
export function formatSimulationFormData(formData) {
  // 确保数值类型正确
  const formatted = {
    ...formData,
    // 转换数值类型
    initialCapital: parseFloat(formData.initialCapital),
    periods: formData.periods.map(period => parseInt(period, 10)),
    // 如果有其他特殊处理可以在这里添加
  }

  return formatted
}

/**
 * 从模拟结果中提取图表数据
 * @param {Object} simulationResult 模拟结果
 * @returns {Object} 包含图表所需的所有数据
 */
export function extractChartData(simulationResult) {
  if (!simulationResult || !simulationResult.stock_data) {
    return {
      dates: [],
      klineData: [],
      maData: {},
      buyPoints: {},
      sellPoints: {},
      assetData: {},
      investmentData: {}
    }
  }

  // 提取日期
  const dates = simulationResult.stock_data.map(item => item.date)

  // 提取K线数据 [开盘, 收盘, 最低, 最高]
  const klineData = simulationResult.stock_data.map(item => [
    item.date,
    item.open,
    item.close,
    item.low,
    item.high
  ])

  // 提取均线数据
  const maData = {}
  
  // 处理MA数据
  if (simulationResult.ma_data) {
    Object.entries(simulationResult.ma_data).forEach(([period, data]) => {
      maData[period] = data.map((value, index) => [
        dates[index],
        value
      ])
    })
  }

  // 提取买入点
  const buyPoints = {}
  const sellPoints = {}
  
  // 处理买卖点
  if (simulationResult.transaction_points) {
    Object.entries(simulationResult.transaction_points).forEach(([period, points]) => {
      buyPoints[period] = points.filter(p => p.type === 'buy')
      sellPoints[period] = points.filter(p => p.type === 'sell')
    })
  }

  // 提取资产和投入数据
  const assetData = {}
  const investmentData = {}
  
  // 处理资产和投入数据
  if (simulationResult.period_results) {
    Object.entries(simulationResult.period_results).forEach(([period, result]) => {
      if (result.asset_curve && result.asset_curve.length > 0) {
        assetData[period] = result.asset_curve.map((value, index) => [
          dates[index] || 'unknown',
          value
        ])
      }
      
      if (result.investment_curve && result.investment_curve.length > 0) {
        investmentData[period] = result.investment_curve.map((value, index) => [
          dates[index] || 'unknown',
          value
        ])
      }
    })
  }

  return {
    dates,
    klineData,
    maData,
    buyPoints,
    sellPoints,
    assetData,
    investmentData
  }
}

/**
 * 生成周期选择的选项
 * @param {number} minPeriod 最小周期
 * @param {number} maxPeriod 最大周期
 * @param {number} step 步长
 * @returns {Array} 选项数组
 */
export function generatePeriodOptions(minPeriod, maxPeriod, step) {
  const options = []
  
  for (let period = minPeriod; period <= maxPeriod; period += step) {
    options.push({
      label: `${period}天`,
      value: period
    })
  }
  
  return options
}

/**
 * 计算特定周期的统计数据
 * @param {Object} simulationResult 模拟结果
 * @param {string|number} period 周期值
 * @returns {Object} 该周期的统计数据
 */
export function getPeriodStatistics(simulationResult, period) {
  if (!simulationResult || !simulationResult.period_results || !simulationResult.period_results[period]) {
    return {
      totalProfit: '0.00',
      profitRate: '0.00',
      annualizedReturn: '0.00',
      transactionCount: 0,
      winRate: '0.00',
      avgHoldDays: '0.0',
      maxDrawdown: '0.00'
    }
  }

  const result = simulationResult.period_results[period]
  
  return {
    totalProfit: parseFloat(result.total_profit || 0).toFixed(2),
    profitRate: ((result.profit_rate || 0) * 100).toFixed(2),
    annualizedReturn: ((result.annualized_return || 0) * 100).toFixed(2),
    transactionCount: result.transaction_count || 0,
    winRate: ((result.win_rate || 0) * 100).toFixed(2),
    avgHoldDays: parseFloat(result.avg_hold_days || 0).toFixed(1),
    maxDrawdown: ((result.max_drawdown || 0) * 100).toFixed(2)
  }
}

/**
 * 对统计结果进行排序
 * @param {Object} simulationResult 模拟结果
 * @param {string} sortField 排序字段
 * @param {boolean} ascending 是否升序
 * @returns {Array} 排序后的周期结果列表
 */
export function sortPeriodResults(simulationResult, sortField, ascending = false) {
  if (!simulationResult || !simulationResult.period_results) {
    return []
  }

  // 映射字段名称
  const fieldMap = {
    totalProfit: 'total_profit',
    profitRate: 'profit_rate',
    annualizedReturn: 'annualized_return',
    transactionCount: 'transaction_count',
    winRate: 'win_rate',
    avgHoldDays: 'avg_hold_days',
    maxDrawdown: 'max_drawdown'
  }

  // 获取实际字段名
  const actualField = fieldMap[sortField] || sortField

  // 转换为数组并排序
  const results = Object.entries(simulationResult.period_results).map(([period, result]) => {
    return {
      period: Number(period),
      ...result
    }
  })

  // 根据字段排序
  results.sort((a, b) => {
    let valueA = a[actualField] || 0
    let valueB = b[actualField] || 0
    
    // 如果是数字字符串，转换为数字
    if (typeof valueA === 'string') valueA = parseFloat(valueA)
    if (typeof valueB === 'string') valueB = parseFloat(valueB)
    
    // 排序
    return ascending ? valueA - valueB : valueB - valueA
  })

  return results
}

/**
 * 提取特定周期的交易列表
 * @param {Object} simulationResult - 模拟结果数据
 * @param {number|string} period - 周期天数
 * @returns {Array} - 格式化后的交易列表
 */
export function extractTransactionList(simulationResult, period) {
  // 如果没有数据或没有指定周期的数据，返回空数组
  if (!simulationResult || !simulationResult.period_results || !simulationResult.period_results[period]) {
    return []
  }
  
  const periodResult = simulationResult.period_results[period]
  const transactions = periodResult.transactions || []
  
  // 格式化交易数据
  return transactions.map((transaction, index) => {
    return {
      id: index + 1,
      date: transaction.date,
      action: transaction.action,
      price: parseFloat(transaction.price).toFixed(2),
      shares: transaction.shares,
      amount: parseFloat(transaction.amount).toFixed(2),
      profit: transaction.profit ? parseFloat(transaction.profit).toFixed(2) : null,
      profitPct: transaction.profit_pct ? parseFloat(transaction.profit_pct).toFixed(2) : null,
      holdDays: transaction.hold_days
    }
  })
}

/**
 * 提取特定周期的统计信息
 * @param {Object} simulationResult - 模拟结果数据
 * @param {number|string} period - 周期天数
 * @returns {Object} - 统计信息对象
 */
export function extractStatistics(simulationResult, period) {
  // 默认返回值
  const defaultStats = {
    transactionCount: 0,
    winCount: 0,
    lossCount: 0,
    winRate: '0.00',
    avgHoldDays: '0.00'
  }
  
  // 如果没有数据或没有指定周期的数据，返回默认值
  if (!simulationResult || !simulationResult.period_results || !simulationResult.period_results[period]) {
    return defaultStats
  }
  
  const periodResult = simulationResult.period_results[period]
  const transactions = periodResult.transactions || []
  
  // 统计交易信息
  let winCount = 0
  let totalHoldDays = 0
  let validHoldDaysCount = 0
  
  transactions.forEach(transaction => {
    // 只有卖出交易才计算盈亏
    if (transaction.action === 'sell' && transaction.profit !== undefined) {
      if (parseFloat(transaction.profit) > 0) {
        winCount++
      }
    }
    
    // 累计持仓天数
    if (transaction.hold_days !== undefined && transaction.hold_days !== null) {
      totalHoldDays += transaction.hold_days
      validHoldDaysCount++
    }
  })
  
  // 计算卖出交易的数量（用于计算胜率）
  const sellTransactions = transactions.filter(t => t.action === 'sell' && t.profit !== undefined)
  const transactionCount = sellTransactions.length
  const lossCount = transactionCount - winCount
  
  // 计算胜率和平均持仓天数
  const winRate = transactionCount > 0 ? ((winCount / transactionCount) * 100).toFixed(2) : '0.00'
  const avgHoldDays = validHoldDaysCount > 0 ? (totalHoldDays / validHoldDaysCount).toFixed(2) : '0.00'
  
  return {
    transactionCount,
    winCount,
    lossCount,
    winRate,
    avgHoldDays
  }
}

/**
 * 提取所有周期的统计数据
 * @param {Object} simulationResult - 模拟结果数据
 * @returns {Array} - 所有周期的统计数据数组
 */
export function extractAllPeriodsStats(simulationResult) {
  if (!simulationResult || !simulationResult.period_results) {
    return []
  }
  
  const periods = Object.keys(simulationResult.period_results)
  
  return periods.map(period => {
    const periodData = simulationResult.period_results[period]
    
    return {
      period: parseInt(period),
      profitRate: parseFloat(periodData.profit_rate).toFixed(2),
      annualReturn: parseFloat(periodData.annual_return).toFixed(2),
      winRate: extractStatistics(simulationResult, period).winRate,
      maxDrawdown: parseFloat(periodData.max_drawdown).toFixed(2),
      transactionCount: periodData.transaction_count,
      avgHoldDays: extractStatistics(simulationResult, period).avgHoldDays
    }
  })
}

/**
 * 按指定字段对数据进行排序
 * @param {Array} data - 要排序的数据数组
 * @param {string} field - 排序字段
 * @param {string} order - 排序顺序，'asc'或'desc'
 * @returns {Array} - 排序后的数组
 */
export function sortDataByField(data, field, order = 'desc') {
  if (!data || !data.length) {
    return []
  }
  
  return [...data].sort((a, b) => {
    let valueA = a[field]
    let valueB = b[field]
    
    // 将字符串转换为数字进行比较（如果可能）
    if (!isNaN(valueA)) valueA = parseFloat(valueA)
    if (!isNaN(valueB)) valueB = parseFloat(valueB)
    
    if (order === 'asc') {
      return valueA > valueB ? 1 : -1
    } else {
      return valueA < valueB ? 1 : -1
    }
  })
} 