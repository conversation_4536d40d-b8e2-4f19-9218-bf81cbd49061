/*! Element Plus v2.9.7 */var e={name:"sv",el:{breadcrumb:{label:"Breadcrumb"},colorpicker:{confirm:"OK",clear:"T\xF6m"},datepicker:{now:"Nu",today:"Idag",cancel:"Avbryt",clear:"T\xF6m",confirm:"OK",selectDate:"V\xE4lj datum",selectTime:"V\xE4lj tid",startDate:"Startdatum",startTime:"Starttid",endDate:"Slutdatum",endTime:"Sluttid",prevYear:"F\xF6reg\xE5ende \xE5r",nextYear:"N\xE4sta \xE5r",prevMonth:"F\xF6reg\xE5ende m\xE5nad",nextMonth:"N\xE4sta m\xE5nad",year:"",month1:"Januari",month2:"Februari",month3:"Mars",month4:"April",month5:"Maj",month6:"Juni",month7:"Juli",month8:"Augusti",month9:"September",month10:"Oktober",month11:"November",month12:"December",weeks:{sun:"S\xF6n",mon:"M\xE5n",tue:"Tis",wed:"Ons",thu:"Tor",fri:"Fre",sat:"L\xF6r"},months:{jan:"Jan",feb:"Feb",mar:"Mar",apr:"Apr",may:"Maj",jun:"Jun",jul:"Jul",aug:"Aug",sep:"Sep",oct:"Okt",nov:"Nov",dec:"Dec"}},select:{loading:"Laddar",noMatch:"Hittade inget",noData:"Ingen data",placeholder:"V\xE4lj"},mention:{loading:"Laddar"},cascader:{noMatch:"Hittade inget",loading:"Laddar",placeholder:"V\xE4lj",noData:"Ingen data"},pagination:{goto:"G\xE5 till",pagesize:"/sida",total:"Totalt {total}",pageClassifier:"",page:"Page",prev:"Go to previous page",next:"Go to next page",currentPage:"page {pager}",prevPages:"Previous {pager} pages",nextPages:"Next {pager} pages"},messagebox:{title:"Meddelande",confirm:"OK",cancel:"Avbryt",error:"Felaktig inmatning"},upload:{deleteTip:"press delete to remove",delete:"Radera",preview:"F\xF6rhandsvisa",continue:"Forts\xE4tt"},table:{emptyText:"Inga Data",confirmFilter:"Bekr\xE4fta",resetFilter:"\xC5terst\xE4ll",clearFilter:"Alla",sumText:"Summa"},tour:{next:"N\xE4sta",previous:"F\xF6reg\xE5ende",finish:"Avsluta"},tree:{emptyText:"Ingen data"},transfer:{noMatch:"Hittade inget",noData:"Ingen data",titles:["List 1","List 2"],filterPlaceholder:"Enter keyword",noCheckedFormat:"{total} items",hasCheckedFormat:"{checked}/{total} checked"},image:{error:"FAILED"},pageHeader:{title:"Bak\xE5t"},popconfirm:{confirmButtonText:"Ja",cancelButtonText:"Nej"},carousel:{leftArrow:"Carousel arrow left",rightArrow:"Carousel arrow right",indicator:"Carousel switch to index {index}"}}};export{e as default};
//# sourceMappingURL=sv.min.mjs.map
