{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/select/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\n\nimport Select from './src/select.vue'\nimport Option from './src/option.vue'\nimport OptionGroup from './src/option-group.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSelect: SFCWithInstall<typeof Select> & {\n  Option: typeof Option\n  OptionGroup: typeof OptionGroup\n} = withInstall(Select, {\n  Option,\n  OptionGroup,\n})\nexport default ElSelect\nexport const ElOption: SFCWithInstall<typeof Option> = withNoopInstall(Option)\nexport const ElOptionGroup: SFCWithInstall<typeof OptionGroup> =\n  withNoopInstall(OptionGroup)\n\nexport * from './src/token'\n"], "names": ["withInstall", "Select", "Option", "OptionGroup", "withNoopInstall"], "mappings": ";;;;;;;;;;AAIY,MAAC,QAAQ,GAAGA,mBAAW,CAACC,iBAAM,EAAE;AAC5C,UAAEC,iBAAM;AACR,eAAEC,sBAAW;AACb,CAAC,EAAE;AAES,MAAC,QAAQ,GAAGC,uBAAe,CAACF,iBAAM,EAAE;AACpC,MAAC,aAAa,GAAGE,uBAAe,CAACD,sBAAW;;;;;;;;;"}