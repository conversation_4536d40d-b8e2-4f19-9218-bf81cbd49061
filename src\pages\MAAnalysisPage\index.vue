<template>
  <div class="ma-analysis-page">
    <page-header title="均线分析" subtitle="分析股票价格相对于均线的位置关系，帮助判断股票趋势" />
    
    <el-card class="query-card">
      <el-form :model="queryForm" ref="queryFormRef" :rules="rules" label-width="100px" size="default">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="8">
            <el-form-item label="股票代码" prop="stock_code">
              <el-input 
                v-model="queryForm.stock_code" 
                placeholder="请输入股票代码，如：000001" 
                clearable
              />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="8">
            <el-form-item label="开始日期" prop="start_date">
              <el-date-picker
                v-model="queryForm.start_date"
                type="date"
                placeholder="选择开始日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="8">
            <el-form-item label="结束日期" prop="end_date">
              <el-date-picker
                v-model="queryForm.end_date"
                type="date"
                placeholder="选择结束日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :xs="24" :sm="8">
            <el-form-item label="最小周期" prop="min_period">
              <el-input-number 
                v-model="queryForm.min_period" 
                :min="1" 
                :max="30"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="8">
            <el-form-item label="最大周期" prop="max_period">
              <el-input-number 
                v-model="queryForm.max_period" 
                :min="10" 
                :max="120"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="8">
            <el-form-item label="周期步长" prop="step">
              <el-input-number 
                v-model="queryForm.step" 
                :min="1" 
                :max="10"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery" :loading="loading">
            <el-icon><Search /></el-icon> 查询分析
          </el-button>
          <el-button @click="resetForm">
            <el-icon><RefreshLeft /></el-icon> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-row :gutter="20" v-if="showResult">
      <el-col :span="24">
        <KLineMAChart 
          :dates="chartData.dates" 
          :klineData="chartData.klineData" 
          :maData="chartData.maData"
          :title="`${queryForm.stock_code} 股票K线与均线分析`"
          :periods="displayedPeriods"
          :analysisData="analysisData"
        />
      </el-col>
    </el-row>
    
    <el-row :gutter="20" v-if="showResult">
      <el-col :span="24">
        <MAAnalysisResult 
          :analysisData="analysisData" 
          :stockCode="queryForm.stock_code"
          :startDate="queryForm.start_date"
          :endDate="queryForm.end_date"
          :isLoading="loading"
        />
      </el-col>
    </el-row>
    
    <el-row :gutter="20" v-if="showResult">
      <el-col :span="24">
        <el-card shadow="hover" class="period-selector-card">
          <template #header>
            <div class="card-header">
              <h3>均线显示设置</h3>
            </div>
          </template>
          
          <div class="period-selector">
            <el-checkbox-group v-model="displayedPeriods">
              <el-checkbox 
                v-for="period in availablePeriods" 
                :key="period" 
                :label="period"
              >
                <span :style="{ color: getMALineColor(period) }">MA{{ period }}</span>
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { Search, RefreshLeft } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getMAPositionAnalysis, formatMAAnalysisData, getMALineColor } from '@/api/maAnalysis'
import KLineMAChart from './components/KLineMAChart.vue'
import MAAnalysisResult from './components/MAAnalysisResult.vue'
import PageHeader from '@/components/PageHeader/index.vue'

// 查询表单
const queryForm = reactive({
  stock_code: '',
  start_date: '',
  end_date: '',
  min_period: 5,
  max_period: 60,
  step: 5
})

// 表单校验规则
const rules = {
  stock_code: [
    { required: true, message: '请输入股票代码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '股票代码格式不正确', trigger: 'blur' }
  ],
  start_date: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  end_date: [
    { required: true, message: '请选择结束日期', trigger: 'change' }
  ],
  min_period: [
    { required: true, message: '请输入最小周期', trigger: 'blur' }
  ],
  max_period: [
    { required: true, message: '请输入最大周期', trigger: 'blur' }
  ],
  step: [
    { required: true, message: '请输入周期步长', trigger: 'blur' }
  ]
}

// 分析数据
const analysisData = ref(null)
const loading = ref(false)
const showResult = ref(false)
const queryFormRef = ref(null)

// 图表数据
const chartData = ref({
  dates: [],
  klineData: [],
  maData: {}
})

// 均线周期选择
const availablePeriods = ref([])
const displayedPeriods = ref([5, 10, 20, 30, 60])

// 当天结束日期
const today = new Date()
const defaultEndDate = today.toISOString().split('T')[0]
// 默认开始日期为6个月前
const defaultStartDate = new Date(today.setMonth(today.getMonth() - 6)).toISOString().split('T')[0]

// 初始化表单默认值
onMounted(() => {
  queryForm.start_date = defaultStartDate
  queryForm.end_date = defaultEndDate
})

// 查询分析数据
const handleQuery = async () => {
  // 表单校验
  await queryFormRef.value.validate()
  
  loading.value = true
  showResult.value = false
  
  try {
    // 调用均线分析接口
    const response = await getMAPositionAnalysis(queryForm)
    
    // 检查返回数据格式
    console.log('返回数据结构:', response)
    
    // 直接使用原始响应数据，不做结构转换
    analysisData.value = response
    
    // 使用工具函数处理数据用于图表展示
    chartData.value = formatMAAnalysisData(response)
    
    // 调试日志 - 检查处理后的图表数据
    console.log('格式化后的图表数据:', chartData.value)
    console.log('可用的均线周期:', Object.keys(chartData.value.maData))
    console.log('均线数据示例:', chartData.value.maData[5]?.slice(0, 3))
    
    // 更新可用周期选项，从maData取周期
    availablePeriods.value = Object.keys(chartData.value.maData)
      .map(period => Number(period))
      .sort((a, b) => a - b)
    
    console.log('获取到的可用均线周期:', availablePeriods.value)
    
    // 默认选择一些常用周期，如果可用
    const preferredPeriods = [5, 10, 20, 30, 60]
    displayedPeriods.value = preferredPeriods.filter(period => 
      availablePeriods.value.includes(period)
    )
    
    // 如果没有匹配到常用周期，则选择前5个可用周期（如果有）
    if (displayedPeriods.value.length === 0 && availablePeriods.value.length > 0) {
      displayedPeriods.value = availablePeriods.value.slice(0, Math.min(5, availablePeriods.value.length))
    }
    
    console.log('选择显示的均线周期:', displayedPeriods.value)
    
    showResult.value = true
    ElMessage.success('查询成功')
  } catch (error) {
    console.error('查询均线分析数据失败', error)
    ElMessage.error('查询失败：' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  queryFormRef.value.resetFields()
  analysisData.value = null
  showResult.value = false
  
  // 重置为默认日期
  queryForm.start_date = defaultStartDate
  queryForm.end_date = defaultEndDate
}

</script>

<style scoped>
.ma-analysis-page {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.period-selector-card {
  margin-top: 20px;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.period-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
</style> 