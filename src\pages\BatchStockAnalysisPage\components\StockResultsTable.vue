<template>
  <div class="stock-results-table">
    <div class="table-header">
      <h3>股票总体表现对比</h3>
      <div class="table-actions">
        <el-select v-model="selectedMetric" placeholder="选择排序指标" style="width: 150px">
          <el-option
            v-for="item in sortMetrics"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button type="primary" plain size="small" @click="handleExport">
          导出数据
        </el-button>
      </div>
    </div>
    
    <!-- 搜索框 -->
    <div class="search-container">
      <el-input
        v-model="searchQuery"
        placeholder="搜索股票代码或名称"
        clearable
        prefix-icon="Search"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <div class="search-stats" v-if="searchQuery">
        找到 {{ filteredStockResults.length }} 条结果
      </div>
    </div>
    
    <!-- 股票汇总表 -->
    <el-table
      :data="filteredStockResults"
      border
      style="width: 100%"
      height="500"
      :default-sort="{ prop: selectedMetric, order: 'descending' }"
    >
      <el-table-column prop="stockCode" label="股票代码" width="100" fixed="left">
        <template #default="scope">
          <div class="stock-code-cell">
            <span>{{ scope.row.stockCode }}</span>
            <span class="stock-name">{{ scope.row.stockName }}</span>
          </div>
        </template>
      </el-table-column>
      
      <!-- 收益分组 -->
      <el-table-column label="收益表现" align="center">
        <el-table-column prop="bestProfitRate" label="最佳收益率(%)" width="120" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.bestProfitRate)">
              {{ formatNumber(scope.row.bestProfitRate) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="bestPeriod" label="最佳周期" width="100" sortable>
          <template #default="scope">
            <el-tag size="small">MA{{ scope.row.bestPeriod }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="worstProfitRate" label="最差收益率(%)" width="120" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.worstProfitRate)">
              {{ formatNumber(scope.row.worstProfitRate) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="worstPeriod" label="最差周期" width="100" sortable>
          <template #default="scope">
            <el-tag size="small" type="danger">MA{{ scope.row.worstPeriod }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="averageProfitRate" label="平均收益率(%)" width="120" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.averageProfitRate)">
              {{ formatNumber(scope.row.averageProfitRate) }}
            </span>
          </template>
        </el-table-column>
      </el-table-column>
      
      <!-- 风险分组 -->
      <el-table-column label="风险指标" align="center">
        <el-table-column prop="averageMaxDrawdown" label="平均最大回撤(%)" width="140" sortable>
          <template #default="scope">
            <span class="negative-value">
              {{ formatNumber(scope.row.averageMaxDrawdown) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="bestDrawdownPeriod" label="最小回撤周期" width="120" sortable>
          <template #default="scope">
            <el-tag size="small" type="success">MA{{ scope.row.bestDrawdownPeriod }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="profitDrawdownRatio" label="收益回撤比" width="120" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.profitDrawdownRatio)">
              {{ formatNumber(scope.row.profitDrawdownRatio) }}
            </span>
          </template>
        </el-table-column>
      </el-table-column>
      
      <!-- 交易数据分组 -->
      <el-table-column label="交易数据" align="center">
        <el-table-column prop="averageTransactionCount" label="平均交易次数" width="120" sortable />
        <el-table-column prop="bestWinRate" label="最佳胜率(%)" width="120" sortable>
          <template #default="scope">
            <span :class="getValueClass(scope.row.bestWinRate)">
              {{ formatNumber(scope.row.bestWinRate) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="bestWinRatePeriod" label="最佳胜率周期" width="120" sortable>
          <template #default="scope">
            <el-tag size="small" type="success">MA{{ scope.row.bestWinRatePeriod }}</el-tag>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

const props = defineProps({
  results: {
    type: Array,
    default: () => []
  },
  stockCodes: {
    type: Array,
    default: () => []
  }
})

// 排序指标
const sortMetrics = [
  { label: '最佳收益率', value: 'bestProfitRate' },
  { label: '平均收益率', value: 'averageProfitRate' },
  { label: '收益回撤比', value: 'profitDrawdownRatio' },
  { label: '平均最大回撤', value: 'averageMaxDrawdown' },
  { label: '最佳胜率', value: 'bestWinRate' }
]

const selectedMetric = ref('bestProfitRate')
const searchQuery = ref('') // 搜索查询

// 处理股票汇总数据
const stockResults = computed(() => {
  if (!props.results.length) return []
  
  // 存储每只股票的汇总数据
  const stockSummary = {}
  
  // 遍历所有结果
  props.results.forEach(result => {
    const stockCode = result.stock_code
    
    if (!stockSummary[stockCode]) {
      stockSummary[stockCode] = {
        stockCode,
        stockName: result.stock_name || '未知',
        periods: [],
        profitRates: [],
        maxDrawdowns: [],
        transactionCounts: [],
        winRates: []
      }
    }
    
    // 收集每个周期的数据
    stockSummary[stockCode].periods.push({
      period: result.period,
      profitRate: result.profit_rate || 0,
      maxDrawdown: result.max_drawdown || 0,
      transactionCount: result.transaction_count || 0,
      winRate: result.win_rate || 0
    })
    
    stockSummary[stockCode].profitRates.push(result.profit_rate || 0)
    stockSummary[stockCode].maxDrawdowns.push(result.max_drawdown || 0)
    stockSummary[stockCode].transactionCounts.push(result.transaction_count || 0)
    stockSummary[stockCode].winRates.push(result.win_rate || 0)
  })
  
  // 计算每只股票的统计数据
  return Object.values(stockSummary).map(stock => {
    // 找出最佳收益率及对应周期
    const bestProfitPeriod = stock.periods.reduce((best, current) => 
      current.profitRate > best.profitRate ? current : best, { profitRate: -Infinity })
    
    // 找出最差收益率及对应周期
    const worstProfitPeriod = stock.periods.reduce((worst, current) => 
      current.profitRate < worst.profitRate ? current : worst, { profitRate: Infinity })
    
    // 找出最佳胜率及对应周期
    const bestWinRatePeriod = stock.periods.reduce((best, current) => 
      current.winRate > best.winRate ? current : best, { winRate: -Infinity })
    
    // 找出最小回撤及对应周期
    const bestDrawdownPeriod = stock.periods.reduce((best, current) => 
      current.maxDrawdown < best.maxDrawdown ? current : best, { maxDrawdown: Infinity })
    
    // 计算平均值
    const averageProfitRate = stock.profitRates.reduce((sum, rate) => sum + rate, 0) / stock.profitRates.length
    const averageMaxDrawdown = stock.maxDrawdowns.reduce((sum, dd) => sum + dd, 0) / stock.maxDrawdowns.length
    const averageTransactionCount = stock.transactionCounts.reduce((sum, count) => sum + count, 0) / stock.transactionCounts.length
    
    // 计算收益回撤比（使用平均值）
    const profitDrawdownRatio = averageMaxDrawdown !== 0 ? Math.abs(averageProfitRate / averageMaxDrawdown) : 0
    
    return {
      stockCode: stock.stockCode,
      stockName: stock.stockName,
      bestProfitRate: bestProfitPeriod.profitRate,
      bestPeriod: bestProfitPeriod.period,
      worstProfitRate: worstProfitPeriod.profitRate,
      worstPeriod: worstProfitPeriod.period,
      averageProfitRate,
      averageMaxDrawdown,
      bestDrawdownPeriod: bestDrawdownPeriod.period,
      profitDrawdownRatio,
      averageTransactionCount,
      bestWinRate: bestWinRatePeriod.winRate,
      bestWinRatePeriod: bestWinRatePeriod.period
    }
  })
})

// 过滤搜索结果
const filteredStockResults = computed(() => {
  if (!searchQuery.value) {
    return sortedStockResults.value
  }
  
  // 将搜索查询按空格或逗号分割，并去除多余空格
  const searchTerms = searchQuery.value
    .toLowerCase()
    .split(/[\s,]+/) // 使用正则表达式按空格或逗号分割
    .map(term => term.trim()) // 去除每个词条两端的空格
    .filter(term => term.length > 0); // 过滤掉空词条

  if (searchTerms.length === 0) {
    return sortedStockResults.value; // 如果没有有效搜索词，返回所有结果
  }

  return sortedStockResults.value.filter(stock => {
    const stockCodeLower = stock.stockCode.toLowerCase();
    const stockNameLower = stock.stockName.toLowerCase();
    // 检查股票代码或名称是否匹配任何一个搜索词条
    return searchTerms.some(term => 
      stockCodeLower.includes(term) || 
      stockNameLower.includes(term)
    );
  });
})

// 排序后的结果
const sortedStockResults = computed(() => {
  const results = [...stockResults.value]
  
  if (selectedMetric.value === 'averageMaxDrawdown') {
    // 对于回撤，值越小越好
    return results.sort((a, b) => a[selectedMetric.value] - b[selectedMetric.value])
  } else {
    // 对于其他指标，值越大越好
    return results.sort((a, b) => b[selectedMetric.value] - a[selectedMetric.value])
  }
})

// 格式化数字
const formatNumber = (value) => {
  if (value === undefined || value === null) return '-'
  return value.toFixed(2)
}

// 获取数值显示的CSS类
const getValueClass = (value) => {
  if (value === undefined || value === null) return ''
  return value > 0 ? 'positive-value' : value < 0 ? 'negative-value' : ''
}

// 导出数据
const handleExport = () => {
  try {
    // 创建CSV内容
    let csvContent = "股票代码,股票名称,最佳收益率(%),最佳周期,最差收益率(%),最差周期,平均收益率(%),平均最大回撤(%),最小回撤周期,收益回撤比,平均交易次数,最佳胜率(%),最佳胜率周期\n"
    
    // 添加每行数据
    stockResults.value.forEach(stock => {
      csvContent += `${stock.stockCode},${stock.stockName},${formatNumber(stock.bestProfitRate)},MA${stock.bestPeriod},${formatNumber(stock.worstProfitRate)},MA${stock.worstPeriod},${formatNumber(stock.averageProfitRate)},${formatNumber(stock.averageMaxDrawdown)},MA${stock.bestDrawdownPeriod},${formatNumber(stock.profitDrawdownRatio)},${formatNumber(stock.averageTransactionCount)},${formatNumber(stock.bestWinRate)},MA${stock.bestWinRatePeriod}\n`
    })
    
    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    
    // 创建下载链接
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `股票分析结果_${new Date().toISOString().split('T')[0]}.csv`)
    
    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + error.message)
  }
}
</script>

<style scoped>
.stock-results-table {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.search-container {
  margin-bottom: 15px;
}

.search-stats {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

.stock-code-cell {
  display: flex;
  flex-direction: column;
}

.stock-name {
  font-size: 0.8em;
  color: #888;
  margin-top: 3px;
}

.positive-value {
  color: #f56c6c;
  font-weight: bold;
}

.negative-value {
  color: #67c23a;
  font-weight: bold;
}
</style> 