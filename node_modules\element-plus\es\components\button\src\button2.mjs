import { Loading } from '@element-plus/icons-vue';
import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';
import { useSizeProp } from '../../../hooks/use-size/index.mjs';
import { iconPropType } from '../../../utils/vue/icon.mjs';

const buttonTypes = [
  "default",
  "primary",
  "success",
  "warning",
  "info",
  "danger",
  "text",
  ""
];
const buttonNativeTypes = ["button", "submit", "reset"];
const buttonProps = buildProps({
  size: useSizeProp,
  disabled: Boolean,
  type: {
    type: String,
    values: buttonTypes,
    default: ""
  },
  icon: {
    type: iconPropType
  },
  nativeType: {
    type: String,
    values: buttonNativeTypes,
    default: "button"
  },
  loading: Boolean,
  loadingIcon: {
    type: iconPropType,
    default: () => Loading
  },
  plain: Boolean,
  text: <PERSON><PERSON><PERSON>,
  link: <PERSON><PERSON><PERSON>,
  bg: <PERSON><PERSON><PERSON>,
  autofocus: Boolean,
  round: Boolean,
  circle: <PERSON>olean,
  color: String,
  dark: Boolean,
  autoInsertSpace: {
    type: Boolean,
    default: void 0
  },
  tag: {
    type: definePropType([String, Object]),
    default: "button"
  }
});
const buttonEmits = {
  click: (evt) => evt instanceof MouseEvent
};

export { buttonEmits, buttonNativeTypes, buttonProps, buttonTypes };
//# sourceMappingURL=button2.mjs.map
