import request from './request'

/**
 * 获取股票价格相对于均线位置分析数据
 * @param {Object} params 请求参数
 * @param {string} params.stock_code 股票代码
 * @param {string} params.start_date 开始日期，格式为YYYY-MM-DD
 * @param {string} params.end_date 结束日期，格式为YYYY-MM-DD
 * @param {number} params.min_period 最小均线周期，默认5
 * @param {number} params.max_period 最大均线周期，默认60
 * @param {number} params.step 均线周期步长，默认5
 * @returns {Promise<Object>} 包含分析结果的Promise
 */
export function getMAPositionAnalysis(params) {
  return request({
    url: '/ma_analysis/price_ma_position',
    method: 'get',
    params
  })
}

/**
 * 处理和格式化均线分析数据用于图表展示
 * @param {Object} analysisData 均线分析接口返回的数据
 * @returns {Object} 格式化后的数据，包含K线数据和均线数据
 */
export function formatMAAnalysisData(analysisData) {
  if (!analysisData) {
    return {
      dates: [],
      klineData: [],
      maData: {}
    }
  }
  
  console.log('开始格式化分析数据:', {
    hasStockData: !!analysisData.stock_data,
    hasDailyData: !!analysisData.daily_data,
    hasPeriodAnalyses: !!analysisData.period_analyses,
    hasPeriods: !!analysisData.periods,
    periodsLength: analysisData.periods?.length
  })
  
  // 提取日期数据和K线数据（如果有）
  const dates = []
  const klineData = []
  
  // 适配新的数据结构 - 既支持stock_data也支持daily_data
  const stockData = analysisData.stock_data || analysisData.daily_data || []
  
  // 创建日期映射，用于将日期字符串统一为相同的格式
  const dateMap = {}
  
  stockData.forEach((item, index) => {
    // 标准化日期格式
    const tradeDate = item.trade_date
    dates.push(tradeDate)
    dateMap[tradeDate] = index // 记录日期在数组中的位置
    
    klineData.push([
      item.open,  // 开盘价
      item.close, // 收盘价
      item.low,   // 最低价
      item.high,  // 最高价
      item.volume // 成交量
    ])
  })
  
  // 打印日期映射示例，帮助调试
  console.log('日期映射示例:', Object.keys(dateMap).slice(0, 3), '共', Object.keys(dateMap).length, '个日期')
  
  // 提取均线数据
  const maData = {}
  
  // 处理直接从daily_data中的ma_values获取均线数据
  if (stockData && stockData.length > 0 && stockData[0].ma_values) {
    console.log('从daily_data中的ma_values提取均线数据');
    
    // 确定所有可用的均线周期
    const availablePeriods = new Set();
    stockData.forEach(dayData => {
      if (dayData.ma_values) {
        Object.keys(dayData.ma_values).forEach(period => {
          availablePeriods.add(period);
        });
      }
    });
    
    // 为每个均线周期创建数据数组
    availablePeriods.forEach(period => {
      maData[period] = stockData
        .filter(dayData => dayData.ma_values && dayData.ma_values[period] !== undefined)
        .map(dayData => [dayData.trade_date, dayData.ma_values[period]]);
      
      console.log(`处理MA${period}数据: ${maData[period].length}条`);
    });
  }
  // 支持两种数据结构: period_analyses对象 或 periods数组
  else if (analysisData.period_analyses && Object.keys(analysisData.period_analyses).length > 0) {
    // 旧格式: {period_analyses: {5: {ma_values: [...]}}}
    console.log('使用period_analyses格式处理均线数据')
    const periods = Object.keys(analysisData.period_analyses)
    
    periods.forEach(period => {
      const periodData = analysisData.period_analyses[period]
      const maValues = periodData.ma_values || []
      
      if (maValues.length > 0) {
        // 检查 ma_values 的数据结构
        console.log(`MA${period}数据结构示例:`, maValues[0])
        
        // 处理可能的不同数据结构
        if (typeof maValues[0] === 'object' && maValues[0].trade_date) {
          // 对象格式: [{trade_date: '2023-01-01', ma: 100}, ...]
          maData[period] = maValues.map(item => [
            item.trade_date,
            item.ma
          ])
        } else if (Array.isArray(maValues[0]) && maValues[0].length === 2) {
          // 已经是数组格式: [['2023-01-01', 100], ...]
          maData[period] = maValues
        } else {
          console.warn(`无法识别MA${period}的数据格式:`, maValues[0])
        }
        
        console.log(`处理MA${period}数据: ${maValues.length}条`)
      } else {
        console.log(`MA${period}没有数据`)
      }
    })
  } else if (analysisData.periods && Array.isArray(analysisData.periods) && analysisData.periods.length > 0) {
    // 新格式: {periods: [{period: 5, ma_values: [...]}]}
    console.log('使用periods数组格式处理均线数据')
    analysisData.periods.forEach(periodData => {
      const period = periodData.period
      const maValues = periodData.ma_values || []
      
      if (maValues.length > 0) {
        // 检查 ma_values 的数据结构
        console.log(`MA${period}数据结构示例:`, maValues[0])
        
        // 处理可能的不同数据结构
        if (typeof maValues[0] === 'object' && maValues[0].trade_date) {
          // 对象格式: [{trade_date: '2023-01-01', ma: 100}, ...]
          maData[period] = maValues.map(item => [
            item.trade_date,
            item.ma
          ])
        } else if (Array.isArray(maValues[0]) && maValues[0].length === 2) {
          // 已经是数组格式: [['2023-01-01', 100], ...]
          maData[period] = maValues
        } else {
          console.warn(`无法识别MA${period}的数据格式:`, maValues[0])
        }
        
        console.log(`处理MA${period}数据: ${maValues.length}条`)
      } else {
        console.log(`MA${period}没有数据`)
      }
    })
  } else {
    console.warn('未找到有效的均线数据!')
  }
  
  const result = { dates, klineData, maData }
  console.log('格式化后的数据:', {
    dates: dates.length,
    klineData: klineData.length,
    maDataKeys: Object.keys(maData),
    maDataSizes: Object.keys(maData).map(k => `${k}: ${maData[k]?.length || 0}`)
  })
  
  return result
}

/**
 * 为均线分析结果生成趋势判断
 * @param {Object} analysis 单个周期的分析结果
 * @returns {Object} 包含趋势判断的结果
 */
export function generateTrendAnalysis(analysis) {
  const totalDays = analysis.above_count + analysis.below_count
  const aboveRatio = totalDays > 0 ? (analysis.above_count / totalDays * 100).toFixed(2) : 0
  
  // 简单的趋势判断逻辑
  let trendJudgment = '中性'
  let trendColor = '#909399' // 默认中性灰色
  
  if (aboveRatio > 65) {
    trendJudgment = '强上升趋势'
    trendColor = '#67C23A' // 强绿色
  } else if (aboveRatio > 55) {
    trendJudgment = '上升趋势'
    trendColor = '#95D475' // 淡绿色
  } else if (aboveRatio < 35) {
    trendJudgment = '强下降趋势'
    trendColor = '#F56C6C' // 强红色
  } else if (aboveRatio < 45) {
    trendJudgment = '下降趋势'
    trendColor = '#F89898' // 淡红色
  }
  
  return {
    ...analysis,
    aboveRatio: parseFloat(aboveRatio),
    trendJudgment,
    trendColor
  }
}

/**
 * 获取各均线周期的推荐颜色
 * @param {number} period 均线周期
 * @returns {string} 推荐的颜色代码
 */
export function getMALineColor(period) {
  const colorMap = {
    5: '#FF9800',    // 橙色 - MA5
    10: '#2196F3',   // 蓝色 - MA10
    20: '#4CAF50',   // 绿色 - MA20
    30: '#9C27B0',   // 紫色 - MA30
    60: '#E91E63',   // 粉红色 - MA60
  }
  
  return colorMap[period] || `hsl(${period * 5 % 360}, 70%, 50%)`
}

// 获取均线位置分析数据
export function getMAPosition(params) {
  return request({
    url: '/api/v1/ma_analysis/price_ma_position',
    method: 'get',
    params
  })
}

/**
 * 获取投资模拟分析数据
 * @param {Object} params 请求参数
 * @param {string} params.stock_code 股票代码
 * @param {string} params.start_date 开始日期，格式为YYYY-MM-DD
 * @param {string} params.end_date 结束日期，格式为YYYY-MM-DD
 * @param {number} params.initial_capital 初始资金，默认100000
 * @param {number} params.min_period 最小均线周期，默认5
 * @param {number} params.max_period 最大均线周期，默认120
 * @param {number} params.step 均线周期步长，默认5
 * @returns {Promise<Object>} 包含模拟结果的Promise
 */
export function simulateInvestment(params) {
  // 确保参数格式正确
  const cleanParams = {
    stock_code: params.stock_code,
    start_date: params.start_date,
    end_date: params.end_date,
    initial_capital: parseFloat(params.initial_capital),
    min_period: parseInt(params.min_period),
    max_period: parseInt(params.max_period),
    step: parseInt(params.step),
    include_kline: true,  // 请求包含K线数据
    include_daily_data: true  // 请求包含日线数据
  }
  
  console.log('发送投资模拟请求，参数:', cleanParams)
  
  // 先尝试获取K线数据
  let stockData = null
  
  // 返回投资模拟数据
  return request({
    url: '/ma_analysis/simulate_investment',
    method: 'get',
    params: cleanParams,
    timeout: 60000 // 增加超时时间为60秒
  }).then(async (data) => {
    // 如果返回数据中没有K线数据，尝试单独获取
    if ((!data.daily_data || data.daily_data.length === 0) && 
        (!data.stock_data || data.stock_data.length === 0)) {
      console.log('未找到K线数据，尝试单独获取')
      
      try {
        // 尝试从均线分析接口获取K线数据
        const maPositionData = await request({
          url: '/ma_analysis/price_ma_position',
          method: 'get',
          params: {
            stock_code: params.stock_code,
            start_date: params.start_date,
            end_date: params.end_date,
            min_period: params.min_period,
            max_period: params.max_period,
            step: params.step
          },
          timeout: 30000
        })
        
        if (maPositionData && (maPositionData.daily_data || maPositionData.stock_data)) {
          console.log('成功从均线分析接口获取K线数据')
          // 将K线数据合并到原始结果中
          data.stock_data = maPositionData.daily_data || maPositionData.stock_data
        }
      } catch (error) {
        console.warn('获取K线数据失败:', error)
      }
    }
    
    return data
  }).catch(error => {
    console.error('投资模拟请求失败:', error)
    throw error
  })
}

/**
 * 处理投资模拟数据用于图表展示
 * @param {Object} simulationData 模拟分析接口返回的数据
 * @returns {Object} 格式化后的数据，包含K线数据、买卖点和资产曲线数据
 */
export function formatSimulationData(simulationData) {
  console.log('原始数据结构:', JSON.stringify(Object.keys(simulationData)))
  
  if (!simulationData) {
    console.error('没有接收到数据')
    return getEmptyFormattedData()
  }
  
  try {
    // 提取日期和K线数据
    const dates = []
    const klineData = []
    
    // 尝试从不同的字段获取日线数据
    let dailyDataSource = null
    
    // 检查是否直接有daily_data
    if (simulationData.daily_data && Array.isArray(simulationData.daily_data) && simulationData.daily_data.length > 0) {
      dailyDataSource = simulationData.daily_data
      console.log('直接使用daily_data作为数据源, 数量:', dailyDataSource.length)
    } 
    // 检查其他可能的数据源
    else if (simulationData.stock_data && Array.isArray(simulationData.stock_data) && simulationData.stock_data.length > 0) {
      dailyDataSource = simulationData.stock_data
      console.log('使用stock_data作为数据源, 数量:', dailyDataSource.length)
    } else if (simulationData.data && Array.isArray(simulationData.data) && simulationData.data.length > 0) {
      dailyDataSource = simulationData.data
      console.log('使用data作为数据源, 数量:', dailyDataSource.length)
    } else {
      // 如果没有找到有效的日线数据，尝试从period_results中推断
      if (simulationData.period_results && Array.isArray(simulationData.period_results) && 
          simulationData.period_results.length > 0 && 
          simulationData.period_results[0].daily_assets && 
          Array.isArray(simulationData.period_results[0].daily_assets)) {
        
        // 从第一个周期的daily_assets中推断日期列表
        const assetDates = simulationData.period_results[0].daily_assets.map(item => item.trade_date)
        
        if (assetDates.length > 0) {
          console.log('没有K线数据，从daily_assets中提取日期, 数量:', assetDates.length)
          // 仅提取日期，不包含K线数据
          dates.push(...assetDates)
          
          // 创建空的K线数据（仅用于显示时间轴）
          dates.forEach(() => {
            klineData.push([0, 0, 0, 0, 0])
          })
        }
      }
    }
    
    // 如果找到了有效的日线数据源，处理数据
    if (dailyDataSource) {
      dailyDataSource.forEach(item => {
        dates.push(item.trade_date)
        klineData.push([
          parseFloat(item.open || 0),
          parseFloat(item.close || 0),
          parseFloat(item.low || 0),
          parseFloat(item.high || 0),
          parseFloat(item.volume || 0)
        ])
      })
    }
    
    console.log('处理后的日期数据:', dates.length, '条')
    console.log('处理后的K线数据:', klineData.length, '条')

    // 检查period_results存在性 - 适应新的数据结构
    const periodResults = simulationData.period_results || []
    if (periodResults.length === 0) {
      console.error('缺少周期结果数据或格式不正确')
      // 如果有日期数据但没有period_results，仍然返回日期和K线数据
      if (dates.length > 0) {
        return {
          dates,
          klineData,
          buyPoints: {},
          sellPoints: {},
          assetData: {},
          investmentData: {}
        }
      }
      return getEmptyFormattedData()
    }

    // 处理每个周期的买卖点和资产数据
    const buyPoints = {}
    const sellPoints = {}
    const assetData = {}
    const investmentData = {}

    periodResults.forEach(periodResult => {
      const period = periodResult.period
      console.log(`处理周期 MA${period} 的数据`)
      
      // 买卖点数据
      if (periodResult.transactions && Array.isArray(periodResult.transactions)) {
        const buyTrans = periodResult.transactions.filter(t => t.type === 'buy' || t.action === 'buy')
        const sellTrans = periodResult.transactions.filter(t => t.type === 'sell' || t.action === 'sell')
        
        buyPoints[period] = buyTrans.map(t => ({
          date: t.trade_date,
          price: parseFloat(t.price || 0),
          shares: parseInt(t.shares || t.quantity || 0)
        }))

        sellPoints[period] = sellTrans.map(t => ({
          date: t.trade_date,
          price: parseFloat(t.price || 0),
          shares: parseInt(t.shares || t.quantity || 0),
          profit: parseFloat(t.profit || 0)
        }))
        
        console.log(`MA${period}: ${buyTrans.length}个买点, ${sellTrans.length}个卖点`)
      } else {
        console.warn(`周期 MA${period} 没有交易数据`)
        buyPoints[period] = []
        sellPoints[period] = []
      }

      // 资产和投入成本数据
      if (periodResult.daily_assets && Array.isArray(periodResult.daily_assets)) {
        // 新增：记录处理前的第一天资产数据
        if (periodResult.daily_assets.length > 0) {
          console.log(`MA${period} 第一天原始资产数据:`, {
            date: periodResult.daily_assets[0].trade_date,
            value: periodResult.daily_assets[0].total_assets,
            type: typeof periodResult.daily_assets[0].total_assets
          });
        }
        
        assetData[period] = periodResult.daily_assets.map(item => {
          // 确保资产值被正确解析为数值
          const assetValue = parseFloat(item.total_assets || 0);
          
          // 新增：只检查第一天的资产数据
          if (item === periodResult.daily_assets[0]) {
            console.log(`MA${period} 第一天解析后资产数值:`, {
              original: item.total_assets,
              parsed: assetValue,
              difference: item.total_assets - assetValue
            });
          }
          
          return [
            item.trade_date,
            assetValue
          ];
        });
        console.log(`MA${period}: ${periodResult.daily_assets.length}条资产数据`)
        
        // 新增：记录格式化后的第一天资产数据
        if (assetData[period].length > 0) {
          console.log(`MA${period} 格式化后第一天资产数据:`, {
            entry: assetData[period][0],
            date: assetData[period][0][0],
            value: assetData[period][0][1]
          });
        }
      } else {
        console.warn(`周期 MA${period} 没有资产数据，生成模拟数据`)
        // 生成模拟的资产数据
        // 使用交易记录生成模拟资产数据
        if (dailyDataSource && dailyDataSource.length > 0 && 
            (buyPoints[period].length > 0 || sellPoints[period].length > 0)) {
          // 创建基于日期的模拟资产数据
          let initialValue = 100000; // 假设初始资金
          let currentValue = initialValue;
          
          // 创建日期到买卖点的映射
          const transactionMap = {};
          buyPoints[period].forEach(point => {
            transactionMap[point.date] = { type: 'buy', price: point.price };
          });
          sellPoints[period].forEach(point => {
            transactionMap[point.date] = { type: 'sell', price: point.price, profit: point.profit };
          });
          
          assetData[period] = dailyDataSource.map((item, index) => {
            const date = item.trade_date;
            // 如果这一天有交易，调整资产值
            if (transactionMap[date]) {
              if (transactionMap[date].type === 'sell' && transactionMap[date].profit) {
                currentValue += transactionMap[date].profit;
              }
            }
            
            // 模拟资产波动
            if (index > 0) {
              const prevClose = parseFloat(dailyDataSource[index-1].close || 0);
              const currClose = parseFloat(item.close || 0);
              if (prevClose > 0 && currClose > 0) {
                const changePct = (currClose - prevClose) / prevClose;
                // 资产随市场小幅波动（只有10%的影响）
                currentValue = currentValue * (1 + changePct * 0.1);
              }
            }
            
            return [date, parseFloat(currentValue.toFixed(2))];
          });
          
          console.log(`为MA${period}生成了${assetData[period].length}条模拟资产数据`);
        } else {
          assetData[period] = [];
        }
      }

      if (periodResult.daily_investment && Array.isArray(periodResult.daily_investment)) {
        investmentData[period] = periodResult.daily_investment.map(item => [
          item.trade_date,
          parseFloat(item.investment_amount || 0)
        ])
        console.log(`MA${period}: ${periodResult.daily_investment.length}条投入数据`)
      } else {
        console.warn(`周期 MA${period} 没有投入数据，生成模拟数据`)
        // 生成模拟的投入数据
        if (dailyDataSource && dailyDataSource.length > 0 && buyPoints[period].length > 0) {
          // 创建基于日期的模拟投入数据
          let investmentValue = 0;
          
          // 创建日期到买点的映射
          const buyMap = {};
          buyPoints[period].forEach(point => {
            buyMap[point.date] = { price: point.price, shares: point.shares || 100 };
          });
          
          investmentData[period] = dailyDataSource.map(item => {
            const date = item.trade_date;
            // 如果这一天有买入，增加投入
            if (buyMap[date]) {
              investmentValue += buyMap[date].price * (buyMap[date].shares || 100);
            }
            
            return [date, parseFloat(investmentValue.toFixed(2))];
          });
          
          console.log(`为MA${period}生成了${investmentData[period].length}条模拟投入数据`);
        } else {
          investmentData[period] = [];
        }
      }
    })

    // 确保至少有一些基本数据可以展示
    if (dates.length === 0 && Object.values(assetData).some(data => data.length > 0)) {
      // 如果没有日期数据但有资产数据，从资产数据中提取日期
      const firstPeriod = Object.keys(assetData)[0]
      if (firstPeriod && assetData[firstPeriod].length > 0) {
        dates.push(...assetData[firstPeriod].map(item => item[0]))
        // 创建空的K线数据作为占位符
        dates.forEach(() => klineData.push([0, 0, 0, 0, 0]))
      }
    }

    return {
      dates,
      klineData,
      buyPoints,
      sellPoints,
      assetData,
      investmentData
    }
  } catch (error) {
    console.error('格式化数据出错:', error)
    return getEmptyFormattedData()
  }
}

// 返回空的格式化数据结构
function getEmptyFormattedData() {
  return {
    dates: [],
    klineData: [],
    buyPoints: {},
    sellPoints: {},
    assetData: {},
    investmentData: {}
  }
}

/**
 * 生成投资模拟统计数据
 * @param {Object} simulationData 模拟分析接口返回的数据
 * @returns {Object} 包含收益统计和交易统计的数据
 */
export function generateSimulationStatistics(simulationData) {
  if (!simulationData || !simulationData.period_results) {
    return {
      profitStats: [],
      tradeStats: []
    }
  }

  const profitStats = simulationData.period_results.map(result => ({
    period: result.period,
    totalReturn: parseFloat(result.total_return).toFixed(2),
    profitRate: parseFloat(result.profit_rate).toFixed(2),
    maxDrawdown: parseFloat(result.max_drawdown).toFixed(2),
    annualizedReturn: parseFloat(result.annualized_return).toFixed(2),
    transactionCount: result.transaction_count
  }))

  const tradeStats = simulationData.period_results.map(result => ({
    period: result.period,
    maxConsecutiveBuy: result.detailed_stats?.max_consecutive_buys || 0,
    maxConsecutiveSell: result.detailed_stats?.max_consecutive_sells || 0,
    avgHoldingDays: parseFloat(result.detailed_stats?.avg_days_between_buy_sell || 0).toFixed(1),
    maxHoldingDays: result.detailed_stats?.max_days_between_buy_sell || 0,
    buyCount: result.buy_count,
    sellCount: result.sell_count
  }))

  return {
    profitStats,
    tradeStats
  }
} 