# 周期性涨跌幅统计页面功能说明文档

## 页面概述

周期性涨跌幅统计页面（PeriodChangeStatsPage）是一个专门用于分析股票在特定周期内涨跌幅情况的功能模块。该页面通过对指定时间范围内的股票数据按照用户设定的周期进行分组计算，展示每个周期内的涨跌幅统计结果，包括均值、方差、标准差等指标，并通过图表直观呈现这些数据的趋势和分布情况。

## 核心功能

### 1. 查询功能

页面提供了一个表单，允许用户输入以下查询条件：
- **股票代码**：要分析的股票代码（如：000001）
- **开始日期**：分析的起始日期
- **结束日期**：分析的结束日期
- **统计周期(天)**：将数据按多少天为一个周期进行分组统计（默认为5天）

表单包含数据验证功能，确保所有必要字段都已填写，且周期天数必须大于0。

### 2. 数据统计与展示

查询成功后，页面会以表格形式展示以下统计结果：
- 周期开始日期
- 周期结束日期
- 周期内涨跌幅列表（以标签形式展示，正值为绿色，负值为红色）
- 均值（%）
- 方差
- 标准差

同时，页面顶部会显示一个摘要信息，包括：
- 查询的股票代码
- 时间范围
- 统计周期
- 总交易天数
- 用于计算涨跌幅的数据点数量

### 3. K线图展示

当查询成功并且有足够的数据时，页面会显示一个K线图，展示股票在查询时间范围内的走势。K线图功能包括：
- 显示股票的开盘价、收盘价、最高价、最低价
- 支持多种周期（5日、10日、20日、30日）的移动平均线
- 提供缩放功能，允许用户放大查看特定时段

K线图通过引入`KLineMAChart`组件实现，该组件复用自均线分析页面（MAAnalysisPage）。

### 4. 周期统计图表

除了K线图，页面还提供了一个专门用于可视化周期统计结果的图表：
- 以线图形式展示每个周期的均值变化趋势
- 使用误差棒（error bar）展示每个点的标准差范围
- 支持与K线图联动，同步缩放和滚动
- 提供详细的tooltip，当鼠标悬停时显示具体数值

该图表使用ECharts库自定义实现，包含均值线和标准差范围两个数据系列。

### 5. 图表联动

K线图和周期统计图表之间实现了联动功能，当用户在一个图表上进行缩放或滚动操作时，另一个图表会同步更新视图，确保两个图表显示的是相同时间范围的数据，便于用户对比分析。

## 数据流程

1. **数据获取**：
   - 首先通过`/api/v1/period_change_pct_stats/calculate`接口获取周期性涨跌幅统计数据
   - 然后通过`getMAPositionAnalysis`函数获取K线和均线数据

2. **数据处理**：
   - 使用`formatMAAnalysisData`函数处理原始K线数据，转换为图表可用格式
   - 将统计数据映射到K线日期上，处理缺失值和异常值

3. **数据展示**：
   - 表格展示处理后的统计数据
   - K线图展示股票价格和均线数据
   - 统计图表展示均值和标准差数据

## 接口调用详情

### 1. 周期性涨跌幅统计数据接口

**接口路径**: `/api/v1/period_change_pct_stats/calculate`

**请求方式**: GET

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|-------|-----|------|------|-------|
| stock_code | String | 是 | 股票代码 | 002370 |
| start_date | String | 是 | 开始日期，格式为YYYY-MM-DD | 2022-01-01 |
| end_date | String | 是 | 结束日期，格式为YYYY-MM-DD | 2022-12-31 |
| period | Number | 是 | 统计周期天数 | 5 |

**请求示例**:
```javascript
axios.get('/api/v1/period_change_pct_stats/calculate', {
  params: {
    stock_code: '002370',
    start_date: '2022-01-01',
    end_date: '2022-12-31',
    period: 5
  },
  headers: {
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache',
    'Expires': '0'
  }
})
```

**响应数据格式**:
```json
{
  "stock_code": "002370",
  "start_date": "2022-01-01",
  "end_date": "2022-12-31",
  "period": 5,
  "total_trading_days_in_range": 242,
  "data_points_calculated": 240,
  "stats": [
    {
      "group_start_date": "2022-01-01",
      "group_end_date": "2022-01-07",
      "change_pct_list": [1.2, -0.5, 0.8, 1.1, -0.3],
      "mean": 0.46,
      "variance": 0.5124,
      "std_dev": 0.7158
    },
    // ... 更多统计数据
  ]
}
```

**响应字段说明**:
| 字段名 | 类型 | 说明 |
|-------|-----|------|
| stock_code | String | 股票代码 |
| start_date | String | 开始日期 |
| end_date | String | 结束日期 |
| period | Number | 统计周期天数 |
| total_trading_days_in_range | Number | 查询范围内的总交易天数 |
| data_points_calculated | Number | 实际用于计算的数据点数量 |
| stats | Array | 统计结果数组 |
| stats[].group_start_date | String | 周期开始日期 |
| stats[].group_end_date | String | 周期结束日期 |
| stats[].change_pct_list | Array | 该周期内每日涨跌幅列表 |
| stats[].mean | Number | 该周期内涨跌幅均值 |
| stats[].variance | Number | 该周期内涨跌幅方差 |
| stats[].std_dev | Number | 该周期内涨跌幅标准差 |

### 2. K线和均线数据接口

**功能**: 通过封装的`getMAPositionAnalysis`函数获取K线和均线数据

**函数调用**: 该函数从 `@/api/maAnalysis` 导入

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|-------|-----|------|------|-------|
| stock_code | String | 是 | 股票代码 | 002370 |
| start_date | String | 是 | 开始日期，格式为YYYY-MM-DD | 2022-01-01 |
| end_date | String | 是 | 结束日期，格式为YYYY-MM-DD | 2022-12-31 |
| min_period | Number | 是 | 最小均线周期 | 5 |
| max_period | Number | 是 | 最大均线周期 | 60 |
| step | Number | 是 | 均线周期步长 | 5 |

**调用示例**:
```javascript
const maApiParams = {
  stock_code: '002370',
  start_date: '2022-01-01',
  end_date: '2022-12-31',
  min_period: 5,
  max_period: 60,
  step: 5
};

const maRawData = await getMAPositionAnalysis(maApiParams);
```

**返回数据格式**:
`getMAPositionAnalysis`函数返回的原始数据需要通过`formatMAAnalysisData`函数处理后使用：

```javascript
const formattedChartData = formatMAAnalysisData(maRawData);
// formattedChartData 结构如下：
{
  dates: ['2022-01-01', '2022-01-02', ...], // 日期数组
  klineData: [ 
    [10.5, 11.2, 10.3, 11.5], // [开盘价, 收盘价, 最低价, 最高价]
    // ... 更多K线数据
  ],
  maData: {
    5: [11.0, 11.1, ...],  // 5日均线数据
    10: [10.8, 10.9, ...], // 10日均线数据
    // ... 更多不同周期的均线数据
  }
}
```

**处理后数据字段说明**:
| 字段名 | 类型 | 说明 |
|-------|-----|------|
| dates | Array | 所有交易日期的数组 |
| klineData | Array | K线数据数组，每个元素是一个包含开盘价、收盘价、最低价、最高价的数组 |
| maData | Object | 包含不同周期均线数据的对象，键为均线周期，值为对应的均线数据数组 |

### 3. 数据映射和处理

页面会将周期性涨跌幅统计数据映射到K线图的日期上，以便在统计图表中显示。映射过程如下：

1. 创建一个从周期结束日期(`group_end_date`)到统计对象的映射：
```javascript
const statsMap = new Map();
results.value.stats.forEach(stat => {
  statsMap.set(stat.group_end_date, stat);
});
```

2. 遍历K线图的日期，将对应的统计数据提取出来：
```javascript
xAxisDates.forEach(date => {
  const stat = statsMap.get(date);
  if (stat) {
    meanData.push([date, stat.mean]);
    customSeriesData.push([date, stat.mean, stat.std_dev]);
  } else {
    meanData.push([date, null]);
    customSeriesData.push([date, null, null]); // 标记数据缺失
  }
});
```

3. 数据验证与异常处理：
```javascript
// 过滤有效数据点
const validMeanDataPoints = meanData.filter(d => d[1] !== null && isFinite(d[1]));
const validCustomDataPoints = customSeriesData.filter(d => 
  d[1] !== null && isFinite(d[1]) && d[2] !== null && isFinite(d[2])
);

// 检查无效数据
if (validCustomDataPoints.length === 0 && results.value.stats.length > 0) {
  console.warn("无有效数据点可渲染，可能是计算的统计值有问题或日期不匹配");
  // 进一步检查原始数据
  const rawStatsWithInvalidStdDev = results.value.stats.filter(
    s => s.std_dev === null || !isFinite(s.std_dev)
  );
  // 处理异常情况...
}
```

## 主要组件

1. **查询表单**：基于Element Plus的表单组件，包含输入框、日期选择器和数字输入控件
2. **结果表格**：使用Element Plus的表格组件，展示统计结果
3. **K线图**：使用`KLineMAChart`组件，基于ECharts实现的K线和均线图表
4. **统计图表**：直接使用ECharts库创建的自定义图表，展示均值和标准差

## 错误处理

页面实现了完善的错误处理机制：
- 表单验证失败时会显示错误提示
- API请求失败时会显示错误消息
- 数据不足或无法计算时会显示相应的提示信息
- 图表数据无效时有保护措施，避免渲染错误

**错误处理代码示例**:
```javascript
try {
  // API请求代码...
} catch (error) {
  console.error("API请求错误:", error);
  if (error.response && error.response.data && error.response.data.detail) {
    errorMsg.value = `请求失败: ${error.response.data.detail}`;
  } else {
    errorMsg.value = '查询失败，请检查网络或联系管理员。';
  }
  ElMessage.error(errorMsg.value);
} finally {
  loading.value = false;
}
```

## 用户交互优化

1. **加载状态显示**：查询过程中显示骨架屏，提供视觉反馈
2. **空状态处理**：当没有查询结果时显示空状态提示
3. **数据可视化**：使用颜色区分正负值，提高数据可读性
4. **图表交互**：支持缩放、鼠标悬停查看详情等交互功能

## 扩展功能建议

1. **多股票对比**：增加支持同时分析多个股票的涨跌幅对比功能
2. **自定义统计指标**：允许用户选择除均值、方差、标准差外的其他统计指标
3. **导出功能**：支持将统计结果导出为CSV或Excel文件
4. **回测功能**：基于周期性涨跌幅统计结果，实现简单的交易策略回测

## 技术实现注意事项

1. **数据处理**：需要注意处理缺失值、异常值和边界情况
2. **图表联动**：确保两个图表的数据点对齐，时间轴同步
3. **性能优化**：对于大量数据，可能需要分页加载或虚拟滚动
4. **响应式设计**：确保在不同屏幕尺寸下都有良好的显示效果

## 重构指南

如需重构此页面，建议按以下步骤进行：
1. 建立数据模型，明确查询参数和返回结果的结构
2. 实现基础UI组件，包括查询表单和结果表格
3. 集成K线图组件，确保数据传递正确
4. 实现周期统计图表，注意与K线图的联动
5. 完善错误处理和用户体验优化

重构时可考虑将页面拆分为更小的组件，提高代码复用性和可维护性。同时，建议统一数据处理逻辑，避免重复代码。 