import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    port: 8081,
    host: true, // 监听所有地址
    open: true, // 自动打开浏览器
    proxy: {
      // 将API请求代理到后端服务器
      '/api/v1': {
        target: 'http://127.0.0.1:8000', // 修改为FastAPI的默认端口8000
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/v1/, '/api'), // 将/api/v1前缀替换为/api
        // 添加调试信息
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('代理错误', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('正在发送请求到:', options.target + req.url.replace(/^\/api\/v1/, '/api'));
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('收到响应:', proxyRes.statusCode);
          });
        }
      }
    }
  }
}) 