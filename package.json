{"name": "stock-front", "version": "1.0.0", "private": true, "scripts": {"dev": "vite --port 8081", "dev:8080": "vite --port 8080", "dev:8082": "vite --port 8082", "dev:debug": "vite --port 8081 --debug", "dev:custom": "vite --port", "build": "vite build", "preview": "vite preview --port 8081", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "echarts": "^5.4.3", "element-plus": "^2.4.3", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "sass": "^1.69.5", "vite": "^5.0.0"}}