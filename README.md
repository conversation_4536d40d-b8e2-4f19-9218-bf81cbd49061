# 股票数据可视化前端

## 项目简介

本项目是股票数据可视化前端，基于Vue.js和ECharts开发，提供以下功能：

1. 股票K线图展示 - 支持动态缩放和多种技术指标
2. 价格区间涨跌幅分布柱状图 - 展示不同价格区间下的涨跌幅分布情况

## 技术栈

- 前端框架：Vue.js 3
- UI库：Element Plus
- 图表库：ECharts 5.x
- HTTP客户端：Axios
- 构建工具：Vite

## 安装和运行

### 环境要求

- Node.js 14.0+
- npm 6.0+

### 安装依赖

```bash
npm install
```

### 开发环境运行

```bash
npm run dev
```

### 生产环境构建

```bash
npm run build
```

## 项目结构

```
stock_front/
├── public/              # 静态资源
├── src/                 # 源代码
│   ├── api/             # API调用模块
│   ├── assets/          # 项目资源
│   ├── components/      # 组件
│   ├── pages/           # 页面
│   ├── router/          # 路由配置
│   ├── store/           # 状态管理
│   ├── utils/           # 工具函数
│   ├── App.vue          # 根组件
│   └── main.js          # 入口文件
├── .env                 # 环境变量
├── package.json         # 项目配置
└── README.md            # 项目说明
```

## 使用说明

### K线图页面

K线图页面提供以下功能：

1. 输入股票代码查询指定股票数据
2. 选择日期范围查看特定时间段的K线图
3. 支持日K、周K、月K切换
4. 支持MA、MACD等技术指标的添加/移除
5. 支持图表缩放、拖动等交互操作

### 价格区间涨跌幅分布页面

价格区间涨跌幅分布页面提供以下功能：

1. 输入股票代码查询指定股票数据
2. 选择日期范围查看特定时间段的价格区间涨跌幅分布
3. 调整价格区间间隔和涨跌幅区间间隔
4. 显示/隐藏特定价格区间的数据
5. 查看各价格区间的统计信息

## 前后端交互

前端通过以下API与后端进行交互：

1. 获取股票K线数据：`/api/v1/stock/data`
2. 获取价格区间涨跌幅分布：`/api/v1/price_analysis/price_change_pct_distribution`

详细API文档请参考后端项目说明。 