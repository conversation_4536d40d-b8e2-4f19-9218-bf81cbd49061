<template>
  <div class="home-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="welcome-card">
          <div class="welcome-content">
            <h1>欢迎使用股票数据可视化平台</h1>
            <p>本平台提供股票K线图展示、价格区间涨跌幅分布分析和均线分析功能，帮助您更好地了解股票价格走势和涨跌规律。</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="feature-row">
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="feature-card">
          <div class="feature-header">
            <el-icon class="feature-icon"><TrendCharts /></el-icon>
            <h2>K线图分析</h2>
          </div>
          <div class="feature-content">
            <p>提供股票K线图展示功能，支持日K、周K、月K切换，可以添加MA、MACD等技术指标，帮助您分析股票价格走势。</p>
            <div class="feature-footer">
              <el-button type="primary" @click="goToKLine">查看K线图</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="feature-card">
          <div class="feature-header">
            <el-icon class="feature-icon"><Histogram /></el-icon>
            <h2>价格区间涨跌幅分布</h2>
          </div>
          <div class="feature-content">
            <p>分析股票在不同价格区间下的涨跌幅分布情况，帮助您了解股票在特定价格区间内的涨跌规律，为投资决策提供参考。</p>
            <div class="feature-footer">
              <el-button type="primary" @click="goToPriceDistribution">查看涨跌幅分布</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="feature-card">
          <div class="feature-header">
            <el-icon class="feature-icon"><DataLine /></el-icon>
            <h2>均线位置分析</h2>
          </div>
          <div class="feature-content">
            <p>分析股票价格相对于各种周期均线的位置关系，计算上方/下方占比和连续天数，帮助您判断股票趋势和买卖时机。</p>
            <div class="feature-footer">
              <el-button type="primary" @click="goToMAAnalysis">查看均线分析</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="feature-row">
      <el-col :span="24">
        <el-card class="guide-card">
          <div class="guide-header">
            <h2>快速上手指南</h2>
          </div>
          <div class="guide-content">
            <ol>
              <li>在导航菜单中选择您需要的功能页面</li>
              <li>输入股票代码和日期范围</li>
              <li>根据需要设置其他参数，如价格区间间隔、涨跌幅区间间隔、均线周期等</li>
              <li>点击查询按钮获取数据</li>
              <li>查看图表并进行交互操作，如缩放、拖动等</li>
            </ol>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { TrendCharts, Histogram, DataLine } from '@element-plus/icons-vue'

const router = useRouter()

// 跳转到K线图页面
const goToKLine = () => {
  router.push('/kline')
}

// 跳转到价格区间涨跌幅分布页面
const goToPriceDistribution = () => {
  router.push('/price-distribution')
}

// 跳转到均线分析页面
const goToMAAnalysis = () => {
  router.push('/ma-analysis')
}
</script>

<style scoped>
.home-container {
  padding: 20px;
}

.welcome-card {
  margin-bottom: 30px;
}

.welcome-content {
  text-align: center;
  padding: 20px;
}

.welcome-content h1 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 15px;
}

.welcome-content p {
  font-size: 16px;
  color: #606266;
}

.feature-row {
  margin-bottom: 30px;
}

.feature-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.feature-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.feature-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.feature-header h2 {
  font-size: 20px;
  color: #303133;
  margin: 0;
}

.feature-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.feature-content p {
  flex: 1;
  margin-bottom: 20px;
  color: #606266;
  line-height: 1.6;
}

.feature-footer {
  margin-top: auto;
  text-align: center;
}

.guide-header {
  margin-bottom: 15px;
}

.guide-header h2 {
  font-size: 20px;
  color: #303133;
  margin: 0;
}

.guide-content ol {
  padding-left: 20px;
  color: #606266;
  line-height: 1.8;
}
</style> 