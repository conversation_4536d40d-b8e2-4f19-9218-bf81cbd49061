{"version": 3, "file": "content2.js", "sources": ["../../../../../../packages/components/tooltip/src/content.vue"], "sourcesContent": ["<template>\n  <el-teleport :disabled=\"!teleported\" :to=\"appendTo\">\n    <transition\n      :name=\"transitionClass\"\n      @after-leave=\"onTransitionLeave\"\n      @before-enter=\"onBeforeEnter\"\n      @after-enter=\"onAfterShow\"\n      @before-leave=\"onBeforeLeave\"\n    >\n      <el-popper-content\n        v-if=\"shouldRender\"\n        v-show=\"shouldShow\"\n        :id=\"id\"\n        ref=\"contentRef\"\n        v-bind=\"$attrs\"\n        :aria-label=\"ariaLabel\"\n        :aria-hidden=\"ariaHidden\"\n        :boundaries-padding=\"boundariesPadding\"\n        :fallback-placements=\"fallbackPlacements\"\n        :gpu-acceleration=\"gpuAcceleration\"\n        :offset=\"offset\"\n        :placement=\"placement\"\n        :popper-options=\"popperOptions\"\n        :strategy=\"strategy\"\n        :effect=\"effect\"\n        :enterable=\"enterable\"\n        :pure=\"pure\"\n        :popper-class=\"popperClass\"\n        :popper-style=\"[popperStyle, contentStyle]\"\n        :reference-el=\"referenceEl\"\n        :trigger-target-el=\"triggerTargetEl\"\n        :visible=\"shouldShow\"\n        :z-index=\"zIndex\"\n        @mouseenter=\"onContentEnter\"\n        @mouseleave=\"onContentLeave\"\n        @blur=\"onBlur\"\n        @close=\"onClose\"\n      >\n        <slot />\n      </el-popper-content>\n    </transition>\n  </el-teleport>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, onBeforeUnmount, ref, unref, watch } from 'vue'\nimport { onClickOutside } from '@vueuse/core'\nimport { useNamespace, usePopperContainerId } from '@element-plus/hooks'\nimport { composeEventHandlers } from '@element-plus/utils'\nimport { ElPopperContent } from '@element-plus/components/popper'\nimport ElTeleport from '@element-plus/components/teleport'\nimport { tryFocus } from '@element-plus/components/focus-trap'\nimport { TOOLTIP_INJECTION_KEY } from './constants'\nimport { useTooltipContentProps } from './content'\nimport type { PopperContentInstance } from '@element-plus/components/popper'\n\ndefineOptions({\n  name: 'ElTooltipContent',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(useTooltipContentProps)\n\nconst { selector } = usePopperContainerId()\nconst ns = useNamespace('tooltip')\n\nconst contentRef = ref<PopperContentInstance>()\nlet stopHandle: ReturnType<typeof onClickOutside>\nconst {\n  controlled,\n  id,\n  open,\n  trigger,\n  onClose,\n  onOpen,\n  onShow,\n  onHide,\n  onBeforeShow,\n  onBeforeHide,\n} = inject(TOOLTIP_INJECTION_KEY, undefined)!\nconst transitionClass = computed(() => {\n  return props.transition || `${ns.namespace.value}-fade-in-linear`\n})\nconst persistentRef = computed(() => {\n  // For testing, we would always want the content to be rendered\n  // to the DOM, so we need to return true here.\n  if (process.env.NODE_ENV === 'test') {\n    return true\n  }\n  return props.persistent\n})\n\nonBeforeUnmount(() => {\n  stopHandle?.()\n})\n\nconst shouldRender = computed(() => {\n  return unref(persistentRef) ? true : unref(open)\n})\n\nconst shouldShow = computed(() => {\n  return props.disabled ? false : unref(open)\n})\n\nconst appendTo = computed(() => {\n  return props.appendTo || selector.value\n})\n\nconst contentStyle = computed(() => (props.style ?? {}) as any)\n\nconst ariaHidden = ref(true)\n\nconst onTransitionLeave = () => {\n  onHide()\n  isFocusInsideContent() && tryFocus(document.body)\n  ariaHidden.value = true\n}\n\nconst stopWhenControlled = () => {\n  if (unref(controlled)) return true\n}\n\nconst onContentEnter = composeEventHandlers(stopWhenControlled, () => {\n  if (props.enterable && unref(trigger) === 'hover') {\n    onOpen()\n  }\n})\n\nconst onContentLeave = composeEventHandlers(stopWhenControlled, () => {\n  if (unref(trigger) === 'hover') {\n    onClose()\n  }\n})\n\nconst onBeforeEnter = () => {\n  contentRef.value?.updatePopper?.()\n  onBeforeShow?.()\n}\n\nconst onBeforeLeave = () => {\n  onBeforeHide?.()\n}\n\nconst onAfterShow = () => {\n  onShow()\n  stopHandle = onClickOutside(\n    computed(() => {\n      return contentRef.value?.popperContentRef\n    }),\n    () => {\n      if (unref(controlled)) return\n      const $trigger = unref(trigger)\n      if ($trigger !== 'hover') {\n        onClose()\n      }\n    }\n  )\n}\n\nconst onBlur = () => {\n  if (!props.virtualTriggering) {\n    onClose()\n  }\n}\n\nconst isFocusInsideContent = (event?: FocusEvent) => {\n  const popperContent: HTMLElement | undefined =\n    contentRef.value?.popperContentRef\n  const activeElement = (event?.relatedTarget as Node) || document.activeElement\n\n  return popperContent?.contains(activeElement)\n}\n\nwatch(\n  () => unref(open),\n  (val) => {\n    if (!val) {\n      stopHandle?.()\n    } else {\n      ariaHidden.value = false\n    }\n  },\n  {\n    flush: 'post',\n  }\n)\n\nwatch(\n  () => props.content,\n  () => {\n    contentRef.value?.updatePopper?.()\n  }\n)\n\ndefineExpose({\n  /**\n   * @description el-popper-content component instance\n   */\n  contentRef,\n  /**\n   * @description validate current focus event is trigger inside el-popper-content\n   */\n  isFocusInsideContent,\n})\n</script>\n"], "names": ["usePopperContainerId", "useNamespace", "ref", "inject", "TOOLTIP_INJECTION_KEY", "computed", "onBeforeUnmount", "unref", "tryFocus", "composeEventHandlers", "onClickOutside", "watch", "_openBlock", "_createBlock", "_unref", "ElTeleport", "_withCtx", "_createVNode", "_Transition"], "mappings": ";;;;;;;;;;;;;;;;;uCAwDc,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAE,QAAS,EAAA,GAAIA,0BAAqB,EAAA,CAAA;AAC1C,IAAM,MAAA,EAAA,GAAKC,qBAAa,SAAS,CAAA,CAAA;AAEjC,IAAA,MAAM,aAAaC,OAA2B,EAAA,CAAA;AAC9C,IAAI,IAAA,UAAA,CAAA;AACJ,IAAM,MAAA;AAAA,MACJ,UAAA;AAAA,MACA,EAAA;AAAA,MACA,IAAA;AAAA,MACA,OAAA;AAAA,MACA,OAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,MACA,YAAA;AAAA,MACA,YAAA;AAAA,KACF,GAAIC,UAAO,CAAAC,+BAAA,EAAuB,KAAS,CAAA,CAAA,CAAA;AAC3C,IAAM,MAAA,eAAA,GAAkBC,aAAS,MAAM;AACrC,MAAA,OAAO,KAAM,CAAA,UAAA,IAAc,CAAG,EAAA,EAAA,CAAG,UAAU,KAAK,CAAA,eAAA,CAAA,CAAA;AAAA,KACjD,CAAA,CAAA;AACD,IAAM,MAAA,aAAA,GAAgBA,aAAS,MAAM;AAGnC,MAAI,IAAA,OAAA,CAAQ,GAAI,CAAA,QAAA,KAAa,MAAQ,EAAA;AACnC,QAAO,OAAA,IAAA,CAAA;AAAA,OACT;AACA,MAAA,OAAO,KAAM,CAAA,UAAA,CAAA;AAAA,KACd,CAAA,CAAA;AAED,IAAAC,mBAAA,CAAgB,MAAM;AACpB,MAAa,UAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,EAAA,CAAA;AAAA,KACd,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAeD,aAAS,MAAM;AAClC,MAAA,OAAOE,SAAM,CAAA,aAAa,CAAI,GAAA,IAAA,GAAOA,UAAM,IAAI,CAAA,CAAA;AAAA,KAChD,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAaF,aAAS,MAAM;AAChC,MAAA,OAAO,KAAM,CAAA,QAAA,GAAW,KAAQ,GAAAE,SAAA,CAAM,IAAI,CAAA,CAAA;AAAA,KAC3C,CAAA,CAAA;AAED,IAAM,MAAA,QAAA,GAAWF,aAAS,MAAM;AAC9B,MAAO,OAAA,KAAA,CAAM,YAAY,QAAS,CAAA,KAAA,CAAA;AAAA,KACnC,CAAA,CAAA;AAED,IAAA,MAAM,eAAeA,YAAS,CAAA,MAAO;AAErC,MAAM,IAAA,EAAA,CAAA;AAEN,MAAA,wBAA0B,KAAM,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA;AAC9B,KAAO,CAAA,CAAA;AACP,IAAqB,MAAA,UAAA,GAAAH,OAAA,CAAA,IAAA,CAAK,CAAS;AACnC,IAAA,MAAA,iBAAmB,GAAA,MAAA;AAAA,MACrB,MAAA,EAAA,CAAA;AAEA,MAAA,0BAAiCM,cAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AAC/B,MAAI,UAAM,CAAU,KAAA,GAAA,IAAU,CAAA;AAAA,KAChC,CAAA;AAEA,IAAM,MAAA,kBAAsC,GAAA,MAAA;AAC1C,MAAA,IAAID,SAAM,CAAA,UAAA,CAAA;AACR,QAAO,OAAA,IAAA,CAAA;AAAA,KACT,CAAA;AAAA,IACF,MAAC,cAAA,GAAAE,0BAAA,CAAA,kBAAA,EAAA,MAAA;AAED,MAAM,IAAA,KAAA,CAAA,SAAA,IAAsCF,SAAA,CAAA,OAAA,CAAA,KAAA,OAAA,EAAA;AAC1C,QAAI,MAAA,EAAM,CAAO;AACf,OAAQ;AAAA,KACV,CAAA,CAAA;AAAA,IACF,MAAC,cAAA,GAAAE,0BAAA,CAAA,kBAAA,EAAA,MAAA;AAED,MAAA,IAAMF,uBAAsB,OAAA,EAAA;AAC1B,QAAA,OAAA,EAAW;AACX,OAAe;AAAA,KACjB,CAAA,CAAA;AAEA,IAAA,MAAM,gBAAgB,MAAM;AAC1B,MAAe,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MACjB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAEA,MAAA,oBAA0B,GAAA,KAAA,CAAA,GAAA,YAAA,EAAA,CAAA;AACxB,KAAO,CAAA;AACP,IAAa,MAAA,aAAA,GAAA,MAAA;AAAA,MAAA,YACI,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,YAAA,EAAA,CAAA;AACb,KAAA,CAAA;AAAyB,IAAA,MAC1B,WAAA,GAAA,MAAA;AAAA,MAAA,MACK,EAAA,CAAA;AACJ,MAAI,UAAA,GAAAG,mBAAmB,CAAAL,YAAA,CAAA,MAAA;AACvB,QAAM,IAAA,EAAA,CAAA;AACN,QAAA,uBAA0B,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAA,CAAA;AACxB,OAAQ,CAAA,EAAA,MAAA;AAAA,QACV,IAAAE,SAAA,CAAA,UAAA,CAAA;AAAA,UACF,OAAA;AAAA,QACF,MAAA,QAAA,GAAAA,SAAA,CAAA,OAAA,CAAA,CAAA;AAAA,QACF,IAAA,QAAA,KAAA,OAAA,EAAA;AAEA,UAAM,SAAS,CAAM;AACnB,SAAI;AACF,OAAQ,CAAA,CAAA;AAAA,KACV,CAAA;AAAA,IACF,MAAA,MAAA,GAAA,MAAA;AAEA,MAAM,IAAA,CAAA,KAAA,CAAA,iBAAwB,EAAuB;AACnD,QAAM,OAAA,EAAA,CAAA;AAEN,OAAM;AAEN,KAAO,CAAA;AAAqC,IAC9C,MAAA,oBAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAA,IAAA,EAAA,CAAA;AAAA,MACE,MAAM,aAAU,GAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAA,CAAA;AAAA,MAChB,MAAS,aAAA,GAAA,CAAA,KAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,CAAA,aAAA,KAAA,QAAA,CAAA,aAAA,CAAA;AACP,MAAA,OAAK,aAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAA,QAAA,CAAA,aAAA,CAAA,CAAA;AACR,KAAa,CAAA;AAAA,IAAAI,SACR,CAAA,MAAAJ,SAAA,CAAA,IAAA,CAAA,EAAA,CAAA,GAAA,KAAA;AACL,MAAA,IAAA,CAAA,GAAA,EAAA;AAAmB,QACrB,UAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,EAAA,CAAA;AAAA,OACF,MAAA;AAAA,QACA,UAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,OAAA;AACS,KACT,EAAA;AAAA,MACF,KAAA,EAAA,MAAA;AAEA,KAAA,CAAA,CAAA;AAAA,IAAAI,gBACc,KAAA,CAAA,OAAA,EAAA,MAAA;AAAA,MACZ,IAAM,EAAA,EAAA,EAAA,CAAA;AACJ,MAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,UAAiC,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KACnC,CAAA,CAAA;AAAA,IACF,MAAA,CAAA;AAEA,MAAa,UAAA;AAAA,MAAA,oBAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAIX,OAAAC,aAAA,EAAA,EAAAC,eAAA,CAAAC,SAAA,CAAAC,kBAAA,CAAA,EAAA;AAAA,QAAA,QAAA,EAAA,CAAA,IAAA,CAAA,UAAA;AAAA,QAAA,EAAA,EAAAD,SAAA,CAAA,QAAA,CAAA;AAAA,OAAA,EAAA;AAAA,QAIA,OAAA,EAAAE,WAAA,CAAA,MAAA;AAAA,UACDC,eAAA,CAAAC,cAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}