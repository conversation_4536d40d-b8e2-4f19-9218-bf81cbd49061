<template>
  <el-card class="page-container">
    <template #header>
      <div class="card-header">
        <span>周期性涨跌幅统计</span>
      </div>
    </template>

    <!-- 查询表单 -->
    <el-form :model="form" :rules="rules" ref="queryForm" label-width="120px" class="query-form">
      <el-form-item label="股票代码" prop="stockCode">
        <el-input v-model="form.stockCode" placeholder="例如：000001" style="width: 220px;"></el-input>
      </el-form-item>
      <el-form-item label="开始日期" prop="startDate">
        <el-date-picker
          v-model="form.startDate"
          type="date"
          placeholder="选择开始日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="结束日期" prop="endDate">
        <el-date-picker
          v-model="form.endDate"
          type="date"
          placeholder="选择结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="统计周期(天)" prop="period">
        <el-input-number v-model="form.period" :min="1" placeholder="例如：5"></el-input-number>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleQuery" :loading="loading">查询</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 结果显示区域 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-if="!loading && queryPerformed && !errorMsg && results.stats && results.stats.length > 0" class="results-container">
      <el-alert
        :title="`查询结果 - ${results.stock_code} (${results.start_date} 至 ${results.end_date}) 周期: ${results.period}天`"
        type="success"
        :closable="false"
        show-icon
        style="margin-bottom: 20px;"
      >
        <p>总交易天数: {{ results.total_trading_days_in_range }} | 用于计算涨跌幅的数据点: {{ results.data_points_calculated }}</p>
      </el-alert>

      <el-table :data="results.stats" border stripe height="400" style="width: 100%">
        <el-table-column prop="group_start_date" label="周期开始日期" width="150" sortable></el-table-column>
        <el-table-column prop="group_end_date" label="周期结束日期" width="150" sortable></el-table-column>
        <el-table-column label="周期内涨跌幅列表" min-width="250">
            <template #default="scope">
                <el-tag
                    v-for="(pct, index) in scope.row.change_pct_list"
                    :key="index"
                    :type="pct >= 0 ? 'success' : 'danger'"
                    effect="light"
                    size="small"
                    style="margin-right: 5px; margin-bottom: 5px;"
                >
                    {{ pct.toFixed(2) }}%
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="mean" label="均值(%)" width="120" sortable>
            <template #default="scope">
                <span :class="{'positive-text': scope.row.mean >= 0, 'negative-text': scope.row.mean < 0}">
                    {{ scope.row.mean.toFixed(2) }}%
                </span>
            </template>
        </el-table-column>
        <el-table-column prop="variance" label="方差" width="120" sortable>
             <template #default="scope">
                {{ scope.row.variance.toFixed(4) }}
            </template>
        </el-table-column>
        <el-table-column label="标准差" width="120" sortable>
            <template #default="scope">
                {{ scope.row.std_dev?.toFixed(4) }}
            </template>
        </el-table-column>
      </el-table>
    </div>

    <el-empty
        v-if="!loading && queryPerformed && !errorMsg && (!results.stats || results.stats.length === 0)"
        description="未查询到相关统计数据，请调整查询条件或确认该时段有足够的交易数据。"
    ></el-empty>

    <el-alert
        v-if="errorMsg"
        :title="'查询出错'"
        type="error"
        :description="errorMsg"
        show-icon
        @close="errorMsg = ''"
        style="margin-top: 20px;"
      ></el-alert>

    <!-- K线图组件 -->
    <div v-if="showKlineChart" class="kline-chart-container">
      <KLineMAChart
        ref="klineChartRef"
        :dates="klineChartData.dates"
        :klineData="klineChartData.klineData"
        :maData="klineChartData.maData"
        :title="klineChartData.title"
        :periods="klineChartData.periods">
      </KLineMAChart>
    </div>

    <!-- 新增：周期性数据统计图表 -->
    <div v-if="showKlineChart && results.stats && results.stats.length > 0" class="stats-chart-container" style="margin-top: 20px;">
      <div ref="statsChartRef" style="width: 100%; height: 550px;"></div>
    </div>

  </el-card>
</template>

<script setup>
import { ref, reactive, onMounted, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios'; // 引入axios用于API请求
import * as echarts from 'echarts'; // 新增：引入 ECharts
// 引入KLineMAChart组件，请根据实际路径调整
import KLineMAChart from '@/pages/MAAnalysisPage/components/KLineMAChart.vue';
// 从 @/api/maAnalysis 导入均线分析相关的API函数和数据处理函数
import { getMAPositionAnalysis, formatMAAnalysisData } from '@/api/maAnalysis';

const queryForm = ref(null); // 表单引用
const klineChartRef = ref(null); // 新增：K线图组件的引用
const statsChartRef = ref(null); // 新增：统计图表DOM元素的引用
let statsChartInstance = null;   // 新增：统计图表的ECharts实例

const loading = ref(false);
const queryPerformed = ref(false); // 是否执行过查询
const errorMsg = ref('');

// Helper function to format date as YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Calculate default dates: today and one year ago
const today = new Date();
const oneYearAgo = new Date();
oneYearAgo.setFullYear(today.getFullYear() - 1);

const defaultStartDate = formatDate(oneYearAgo);
const defaultEndDate = formatDate(today);

const form = reactive({
  stockCode: '002370', // 默认股票代码
  startDate: defaultStartDate, // 设置默认开始日期
  endDate: defaultEndDate,   // 设置默认结束日期
  period: 5, // 默认周期性统计的周期
  // 新增：用于获取均线数据的参数，与MAAnalysisPage保持一致
  ma_min_period: 5,    // 最小均线周期
  ma_max_period: 60,   // 最大均线周期
  ma_step: 5,          // 均线周期步长
  // 新增：指定在K线图上实际显示的均线周期列表
  klineMaDisplayPeriods: [5, 10, 20, 30]
});

const rules = reactive({
  stockCode: [{ required: true, message: '请输入股票代码', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
  period: [{ required: true, message: '请输入统计周期', trigger: 'blur' }, {type: 'number', min:1, message: '周期必须大于0', trigger: 'blur'}],
});

const results = ref({
    stock_code: '',
    start_date: '',
    end_date: '',
    period: 0,
    stats: [],
    total_trading_days_in_range: 0,
    data_points_calculated: 0
});

// K线图相关数据
const klineChartData = ref({
  dates: [],
  klineData: [], // [[open, close, low, high], ...]
  maData: {},    // 由 formatMAAnalysisData 直接生成
  title: '',
  periods: []    // 初始化为空数组，将在获取数据后使用 form.klineMaDisplayPeriods 填充
});
const showKlineChart = ref(false); // 控制K线图显示

// 新增：在组件挂载后和DOM更新后初始化或调整图表
onMounted(() => {
  // DOM 准备好后可以进行 ECharts 初始化
  // 但实际的图表option设置会在数据获取后进行
});

// 监听K线图数据的变化，当K线图显示时，尝试初始化/更新统计图表
watch(showKlineChart, (newValue) => {
  if (newValue && results.value.stats && results.value.stats.length > 0) {
    nextTick(() => {
      initOrUpdateStatsChart();
    });
  }
});

// 处理查询
const handleQuery = async () => {
  console.log("[DEBUG] handleQuery method called.");

  if (!queryForm.value) {
    console.error("[DEBUG] queryForm is not available.");
    ElMessage.error('表单引用丢失，请刷新页面重试。');
    return;
  }

  queryForm.value.validate(async (valid) => {
    console.log("[DEBUG] Form validation callback. Valid:", valid);

    if (valid) {
      console.log("[DEBUG] Form is valid. Proceeding to make API call.");

      loading.value = true;
      queryPerformed.value = true;
      errorMsg.value = '';

      // 重置图表实例 - 先销毁现有的图表实例
      if (statsChartInstance) {
        console.log("[DEBUG] Disposing existing statsChartInstance before new query");
        statsChartInstance.dispose();
        statsChartInstance = null;
      }

      // 重置结果数据
      results.value = {
        stock_code: '',
        start_date: '',
        end_date: '',
        period: 0,
        stats: [],
        total_trading_days_in_range: 0,
        data_points_calculated: 0
      };

      // 重置K线图数据
      klineChartData.value = {
        dates: [],
        klineData: [],
        maData: {},
        title: '',
        periods: []
      };

      showKlineChart.value = false; // 重置时隐藏K线图

      const paramsToLog = {
        stock_code: form.stockCode,
        start_date: form.startDate,
        end_date: form.endDate,
        period: form.period,
      };
      console.log("[DEBUG] Attempting axios.get with params:", JSON.stringify(paramsToLog));

      try {
        // 1. 获取周期性涨跌幅统计数据
        const statsResponse = await axios.get('/api/v1/period_change_pct_stats/calculate', {
          params: {
            stock_code: form.stockCode,
            start_date: form.startDate,
            end_date: form.endDate,
            period: form.period, // 这是周期性统计的周期
          },
          // 添加缓存控制头，防止浏览器缓存
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });

        console.log("[DEBUG] Stats API response received:", statsResponse);
        if(statsResponse && statsResponse.data) {
            console.log("[DEBUG] Stats API response.data:", JSON.stringify(statsResponse.data));
        }

        results.value = statsResponse.data;
        if (!statsResponse.data.stats || statsResponse.data.stats.length === 0) {
          if (statsResponse.data.total_trading_days_in_range > 0) {
             ElMessage.warning('查询成功，但当前条件下未计算出任何周期统计数据。可能是数据点不足以形成完整周期。');
             console.log("[DEBUG] API success, but no stats calculated (data points might be insufficient).");
          } else {
             ElMessage.info('未查询到指定范围内的交易数据或统计结果。');
             console.log("[DEBUG] API success, but no trading data or stats found.");
          }
        }

        // 2. 如果统计数据获取成功，则获取K线数据用于图表展示 (采用MAAnalysisPage的方式)
        if (statsResponse.data && statsResponse.data.stock_code) {
          // 准备K线图标题
          klineChartData.value.title = `${results.value.stock_code} (${results.value.start_date} 至 ${results.value.end_date}) K线图`;

          // 准备调用 getMAPositionAnalysis 的参数
          const maApiParams = {
            stock_code: form.stockCode,
            start_date: form.startDate,
            end_date: form.endDate,
            min_period: form.ma_min_period,
            max_period: form.ma_max_period,
            step: form.ma_step
          };

          console.log("[DEBUG] Calling getMAPositionAnalysis with params:", maApiParams);
          const maRawData = await getMAPositionAnalysis(maApiParams);
          console.log("[DEBUG] Raw MA data from getMAPositionAnalysis:", maRawData);

          if (maRawData) {
            // 使用 formatMAAnalysisData 处理数据
            const formattedChartData = formatMAAnalysisData(maRawData);
            console.log("[DEBUG] Formatted chart data from formatMAAnalysisData:", formattedChartData);

            klineChartData.value.dates = formattedChartData.dates;
            klineChartData.value.klineData = formattedChartData.klineData;
            klineChartData.value.maData = formattedChartData.maData;
            klineChartData.value.periods = form.klineMaDisplayPeriods; // 设置图表实际显示的均线

            // 确保数据有效后再显示图表
            if (klineChartData.value.dates.length > 0 && klineChartData.value.klineData.length > 0) {
              console.log("[DEBUG] Setting showKlineChart to true with valid data");
              showKlineChart.value = true;

              // K线图数据加载完毕后，也尝试初始化/更新统计图表
              nextTick(() => {
                if (results.value.stats && results.value.stats.length > 0) {
                  console.log("[DEBUG] Initializing stats chart after K-line data loaded");
                  initOrUpdateStatsChart();
                  // 尝试连接图表
                  connectCharts();
                }
              });
            } else {
              console.warn("[DEBUG] K-line data is empty or invalid, not showing chart");
              showKlineChart.value = false;
            }
          } else {
            ElMessage.warning('K线图数据获取失败或无数据 (来自getMAPositionAnalysis)。');
            showKlineChart.value = false;
          }
        } else if (statsResponse.data && statsResponse.data.stats && statsResponse.data.stats.length > 0) {
          // 即使没有K线数据（例如getMAPositionAnalysis失败或没有返回数据），但有统计数据
          // 也要尝试显示统计图表，不过这种情况下它无法与K线图联动或共享X轴
          console.log("[DEBUG] No K-line data but stats data exists, trying to show stats chart only");
          nextTick(() => {
            initOrUpdateStatsChart(); // 此时K线图的日期可能为空
          });
        }

      } catch (error) {
        console.error("[DEBUG] API request error in handleQuery:", error);
        if (error.response) {
            console.error("[DEBUG] Error response data:", error.response.data);
            console.error("[DEBUG] Error response status:", error.response.status);
            console.error("[DEBUG] Error response headers:", error.response.headers);
        }
        if (error.response && error.response.data && error.response.data.detail) {
          errorMsg.value = `请求失败: ${error.response.data.detail}`;
        } else {
          errorMsg.value = '查询失败，请检查网络或联系管理员。';
        }
        ElMessage.error(errorMsg.value);
      } finally {
        loading.value = false;
        console.log("[DEBUG] handleQuery finished, loading set to false.");
      }
    } else {
      ElMessage.error('请检查表单输入项!');
      console.log("[DEBUG] Form validation failed. API call not made.");
      return false;
    }
  });
};

// 重置表单
const resetForm = () => {
  if (queryForm.value) {
  queryForm.value.resetFields();
  }
  // 重置时，也清空图表实例和数据
  results.value = {
        stock_code: '',
        start_date: '',
        end_date: '',
        period: 0,
        stats: [],
        total_trading_days_in_range: 0,
        data_points_calculated: 0
      };
  klineChartData.value = {
    dates: [],
    klineData: [],
    maData: {},
    title: '',
    periods: []
  };
  showKlineChart.value = false;
  queryPerformed.value = false;
  errorMsg.value = '';

  if (statsChartInstance) {
    statsChartInstance.dispose();
    statsChartInstance = null;
  }
  // klineChartRef.value 上的实例由其组件内部管理，此处不直接干预其销毁
  // 但如果 klineChartRef.value?.chartInstance 存在并且我们有权访问，可以考虑调用其dispose
};

// 新增：初始化或更新统计图表的方法
const initOrUpdateStatsChart = () => {
  if (!statsChartRef.value) {
    console.warn("[StatsChart] statsChartRef is not yet available.");
    return;
  }
  if (!klineChartData.value.dates || klineChartData.value.dates.length === 0) {
    console.warn("[StatsChart] K-line chart dates are not available, cannot initialize stats chart accurately.");
    // 如果没有K线日期，可以考虑不渲染统计图表，或者用统计数据自身的日期，但这会与K线图不一致
    // 目前按要求，日期需要和K线图保持一致，所以K线日期是必须的
    if (statsChartInstance) { // 如果之前有实例，先销毁
        statsChartInstance.dispose();
        statsChartInstance = null;
    }
    // 可以在这里显示一个提示，说明缺少K线日期数据导致统计图无法渲染
    return;
  }

  if (!statsChartInstance) {
    statsChartInstance = echarts.init(statsChartRef.value);
    console.log("[StatsChart] Initialized.");
  }

  // 准备图表数据
  // X轴数据：直接使用K线图的日期
  const xAxisDates = klineChartData.value.dates;

  // Y轴数据：均值 和 标准差 (用于自定义系列)
  // 我们需要将 results.stats 中的数据映射到 xAxisDates 上
  const meanData = []; // 存储 [日期, 均值]
  const customSeriesData = []; // 存储 [日期, 均值, 标准差] 或者更复杂的用于renderItem的结构

  // 创建一个从 group_end_date 到统计对象的映射，方便查找
  const statsMap = new Map();
  results.value.stats.forEach(stat => {
    statsMap.set(stat.group_end_date, stat);
  });

  xAxisDates.forEach(date => {
    const stat = statsMap.get(date);
    if (stat) {
      meanData.push([date, stat.mean]);
      // 对于自定义系列，我们需要原始的均值和标准差来计算误差棒的位置
      // 数据格式可以是 [xValue, yValue (mean), stdDev, otherInfo...]
      // 这里我们用 [date_index, mean, std_dev] 或者直接用 [date, mean, std_dev]
      // ECharts的custom series的data通常是数值数组，如果x轴是category，则第一个值是索引
      // 如果x轴是time/value，则第一个值是实际的x轴数值（日期时间戳或可解析的日期字符串）
      customSeriesData.push([date, stat.mean, stat.std_dev]);
    } else {
      // 如果K线图有这个日期，但统计数据没有，则该点数据缺失
      // meanData可以推入null或'-', ECharts会处理为断点
      meanData.push([date, null]);
      // customSeriesData也对应推入一个标记，或者不推，renderItem时判断
      customSeriesData.push([date, null, null]); // 标记数据缺失
    }
  });

  // 新增调试日志 =====================================================
  console.log("[StatsChart DEBUG] Query form.period (统计周期):", JSON.parse(JSON.stringify(form.period)));
  // 打印前5个K线日期，以及K线日期的总数
  console.log("[StatsChart DEBUG] KLine Dates (first 5):", JSON.parse(JSON.stringify(klineChartData.value.dates.slice(0, 5))), "Total KLine dates:", klineChartData.value.dates.length);
  // 打印原始统计数据，以及其数量
  console.log("[StatsChart DEBUG] Raw Stats Data (first 5):", JSON.parse(JSON.stringify(results.value.stats.slice(0, 5))), "Total raw stats:", results.value.stats.length);

  const validMeanDataPoints = meanData.filter(d => d[1] !== null && isFinite(d[1]));
  const validCustomDataPoints = customSeriesData.filter(d => d[1] !== null && isFinite(d[1]) && d[2] !== null && isFinite(d[2]));

  console.log("[StatsChart DEBUG] Processed Mean Data for Chart (first 5 valid):", JSON.parse(JSON.stringify(validMeanDataPoints.slice(0,5))));
  console.log("[StatsChart DEBUG] Processed Custom Series Data for Chart (first 5 valid):", JSON.parse(JSON.stringify(validCustomDataPoints.slice(0,5))));

  console.log("[StatsChart DEBUG] Total valid (non-null, finite mean) meanData points:", validMeanDataPoints.length);
  console.log("[StatsChart DEBUG] Total valid (non-null, finite mean & stdDev) customSeriesData points:", validCustomDataPoints.length);

  if (validCustomDataPoints.length === 0 && results.value.stats.length > 0) {
    console.warn("[StatsChart DEBUG] No valid customSeriesData points to render (all means/stdDevs might be null, NaN, or Infinity), but raw stats data exists. This indicates a potential issue with calculated stats values or date mismatch.");
    // 进一步检查原始数据中的std_dev
    const rawStatsWithInvalidStdDev = results.value.stats.filter(s => s.std_dev === null || !isFinite(s.std_dev));
    if (rawStatsWithInvalidStdDev.length > 0) {
        console.warn("[StatsChart DEBUG] Found raw stats entries with null or non-finite std_dev (first 5):", JSON.parse(JSON.stringify(rawStatsWithInvalidStdDev.slice(0,5))));
    }
  }

  // 动态计算Y轴的min和max ==============================================
  /* // 开始注释掉手动计算Y轴范围的逻辑
  let yAxisMin = null;
  let yAxisMax = null;

  if (validCustomDataPoints.length > 0) {
    yAxisMin = validCustomDataPoints[0][1] - validCustomDataPoints[0][2]; // mean - stdDev
    yAxisMax = validCustomDataPoints[0][1] + validCustomDataPoints[0][2]; // mean + stdDev

    validCustomDataPoints.forEach(point => {
      const mean = point[1];
      const stdDev = point[2];
      const currentMin = mean - stdDev;
      const currentMax = mean + stdDev;
      if (currentMin < yAxisMin) {
        yAxisMin = currentMin;
      }
      if (currentMax > yAxisMax) {
        yAxisMax = currentMax;
      }
    });

    // 给min/max增加一些padding，让图表看起来不那么拥挤
    const padding = Math.abs(yAxisMax - yAxisMin) * 0.1; // 10% padding
    yAxisMin -= padding;
    yAxisMax += padding;

    // 确保min和max不是NaN或Infinity，如果只有一个数据点且stdDev为0，可能会出现问题
    if (!isFinite(yAxisMin) || !isFinite(yAxisMax)) {
        console.warn("[StatsChart DEBUG] Calculated yAxisMin or yAxisMax is not finite. Resetting to null for auto-scaling.", {yAxisMin, yAxisMax});
        yAxisMin = null;
        yAxisMax = null;
    } else {
        console.log("[StatsChart DEBUG] Calculated Y-axis range (after padding): min =", yAxisMin, "max =", yAxisMax);
    }
  } else {
    console.log("[StatsChart DEBUG] No valid data points to calculate Y-axis range, ECharts will auto-scale.");
  }
  */ // 结束注释掉手动计算Y轴范围的逻辑
  // =================================================================

  const option = {
    title: {
      text: `${results.value.stock_code || '周期统计'} - 均值与标准差 (周期: ${results.value.period}天)`,
      left: 'center',
      top: 10,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function (params) {
        // params 是一个数组，包含了当前axis上所有series的数据点
        const date = params[0].axisValue; // X轴的日期值
        let tooltipHtml = `${date}<br/>`;
        params.forEach(param => {
          if (param.seriesName === '均值' && param.value && param.value[1] !== null) {
            tooltipHtml += `${param.marker} ${param.seriesName}: ${param.value[1].toFixed(2) }%<br/>`;
          }
          // 对于自定义系列，params[0].data 可能是你传入的原始数据项 [date, mean, std_dev]
          // 需要根据你的renderItem逻辑和data格式来定制tooltip
          if (param.seriesName === '标准差范围' && param.data && param.data[1] !== null && param.data[2] !== null) {
             tooltipHtml += `${param.marker} 标准差: ±${param.data[2].toFixed(4) }<br/>`;
             tooltipHtml += `(范围: ${(param.data[1] - param.data[2]).toFixed(2) }% ~ ${(param.data[1] + param.data[2]).toFixed(2) }%)<br/>`;
          }
        });
        return tooltipHtml;
      }
    },
    legend: {
      data: ['均值', '标准差范围'],
      top: 40,
    },
    grid: { // 与K线图的grid对齐，或根据需要调整
      left: '10%',
      right: '10%',
      bottom: '15%', // 留出空间给 dataZoom
      containLabel: true
    },
    xAxis: {
      type: 'category', // 与K线图的X轴类型保持一致
      data: xAxisDates,
      boundaryGap: false, // 让数据显示在刻度线上，而不是刻度之间
      axisLine: { onZero: false },
      splitLine: { show: false },
      min: 'dataMin', // 默认从数据最小值开始
      max: 'dataMax', // 默认到数据最大值结束
    },
    yAxis: {
      type: 'value',
      scale: true,
      // min: yAxisMin, // Keep commented out
      // max: yAxisMax, // Keep commented out
      axisLabel: {
        formatter: '{value}%'
      },
      splitArea: {
        show: true
      },
      interval: 0.2, //  新增：尝试设置Y轴刻度间隔为0.2
      // minInterval: 0.1, // 可以配合使用，保证最小间隔，防止interval过大时标签重叠或过少
      // splitNumber: 10 // 也可以尝试指定大致的分割段数，ECharts会尽量满足
    },
    dataZoom: [ // 与K线图的dataZoom配置类似，确保可以联动
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        show: true,
        type: 'slider',
        bottom: '5%',
        start: 0,
        end: 100
      }
    ],
    series: [
      {
        name: '均值',
        type: 'line',
        data: meanData, // [[date, mean], [date, mean], ...]
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
            color: '#5470C6' // 蓝色
        },
        itemStyle: {
            color: '#5470C6'
        }
      },
      {
        name: '标准差范围',
        type: 'custom',
        renderItem: function (params, api) {
          const xValue = api.value(0); // 日期字符串
          let mean = api.value(1);   // 均值
          let stdDev = api.value(2); // 标准差

          // 增加更严格的检查: 确保 mean 和 stdDev 都是有效的数字
          if (mean === null || !isFinite(mean)) {
            // console.warn(`[RenderItem] Invalid mean for date ${xValue}:`, mean);
            return; // 如果均值无效，则不渲染任何东西
          }

          let drawErrorBar = true;
          if (stdDev === null || !isFinite(stdDev)) {
            // console.warn(`[RenderItem] Invalid stdDev for date ${xValue}:`, stdDev, "Will only draw mean point.");
            drawErrorBar = false;
            stdDev = 0; // 将stdDev视为0，只绘制均值点，或根据需要处理
          }

          const currentPoint = api.coord([xValue, mean]); // 均值点的坐标

          if (!currentPoint) {
            // console.warn(`[RenderItem] Could not get valid coordinate for mean point at ${xValue}, mean: ${mean}`);
            return; // 如果均值点坐标无效
          }

          if (!drawErrorBar) {
            // 如果标准差无效，只画一个均值点 (如果需要)
            // (当前均值是通过线系列画的，这里可以不额外画点，或者根据需求画一个强调点)
            // return {
            //   type: 'circle',
            //   shape: { cx: currentPoint[0], cy: currentPoint[1], r: 3 },
            //   style: api.style({ fill: '#5470C6' })
            // };
            return; // 当前选择不画任何东西如果stdDev无效，因为均值线会画均值
          }

          // 只有在 stdDev 有效时才计算和绘制误差棒
          const upperY = mean + stdDev;
          const lowerY = mean - stdDev;

          const upperPoint = api.coord([xValue, upperY]);
          const lowerPoint = api.coord([xValue, lowerY]);

          if (!upperPoint || !lowerPoint) {
              // console.warn(`[RenderItem] Could not get valid coordinates for error bar points at ${xValue}`);
              return; // 如果误差棒的上下点坐标无效
          }

          const barWidth = api.size([1, 0])[0] * 0.15;

          return {
            type: 'group',
            children: [
              { // 垂直线，连接 (mean - stdDev) 和 (mean + stdDev)
                type: 'line',
                shape: {
                  x1: currentPoint[0], y1: lowerPoint[1],
                  x2: currentPoint[0], y2: upperPoint[1]
                },
                style: api.style({
                  stroke: '#91CC75', // 绿色系
                  lineWidth: 2
                })
              },
              { // 上横线
                type: 'line',
                shape: {
                  x1: currentPoint[0] - barWidth / 2, y1: upperPoint[1],
                  x2: currentPoint[0] + barWidth / 2, y2: upperPoint[1]
                },
                style: api.style({
                  stroke: '#91CC75',
                  lineWidth: 2
                })
              },
              { // 下横线
                type: 'line',
                shape: {
                  x1: currentPoint[0] - barWidth / 2, y1: lowerPoint[1],
                  x2: currentPoint[0] + barWidth / 2, y2: lowerPoint[1]
                },
                style: api.style({
                  stroke: '#91CC75',
                  lineWidth: 2
                })
              },
              // (可选) 在均值点处画一个点，如果均值线本身不明显的话
              // {
              //   type: 'circle',
              //   shape: {
              //     cx: currentPoint[0],
              //     cy: currentPoint[1],
              //     r: 3
              //   },
              //   style: api.style({
              //     fill: '#EE6666' // 红色
              //   })
              // }
            ]
          };
        },
        data: customSeriesData, // [[date, mean, stdDev], ...]
        z: 100, // 确保在均值线上方
        itemStyle: { // 确保图例正确显示颜色
            color: '#91CC75'
        },
        encode: { // 告诉 ECharts 如何从 data 中取 x 和 y
            x: 0, // data的第一列 (date) 作为 x 轴
            y: [1, 2] // data的第二列 (mean) 和第三列 (stdDev) 会传递给 renderItem 的 api.value(1) 和 api.value(2)
        },
      }
    ]
  };
  statsChartInstance.setOption(option, true); // true表示不合并，完全替换
  console.log("[StatsChart] Option set.");
};

// 新增：尝试连接图表以实现联动
const connectCharts = () => {
  // 需要获取KLineMAChart组件内部的echarts实例
  // 这通常需要KLineMAChart组件通过 defineExpose 暴露其实例
  // 假设KLineMAChart组件暴露了名为 chartInstance 的echarts实例
  nextTick(() => { // 确保klineChartRef.value已经挂载并且其内部的图表已初始化
    const klineInstance = klineChartRef.value?.getChartInstance?.(); // 假设 KLineMAChart 有 getChartInstance 方法

    if (klineInstance && statsChartInstance) {
      console.log("[Charts] Attempting to connect KLineChart and StatsChart.");
      try {
        // 清除任何旧的连接组，以防重复连接或连接到已销毁的实例
        // echarts.disconnect('groupName'); // 如果之前设置了组名

        // 将图表连接到一个组，dataZoom、tooltip等操作会同步
        // 如果只是希望dataZoom同步，可以直接连接
        echarts.connect([klineInstance, statsChartInstance]);
        // 或者给它们一个组名：
        // klineInstance.group = 'stockChartsGroup';
        // statsChartInstance.group = 'stockChartsGroup';
        // echarts.connect('stockChartsGroup');

        console.log("[Charts] Successfully connected.");
      } catch (e) {
        console.error("[Charts] Failed to connect charts:", e);
        ElMessage.error('图表联动设置失败，部分同步功能可能不正常。');
      }
    } else {
      if (!klineInstance) console.warn("[Charts] KLineChart instance not available for connection.");
      if (!statsChartInstance) console.warn("[Charts] StatsChart instance not available for connection.");
    }
  });
};

</script>

<style scoped>
.page-container {
  margin: 20px;
}

.query-form {
  margin-bottom: 20px;
}

.loading-container,
.results-container {
  margin-top: 20px;
}

.positive-text {
  color: #67c23a; /* Element Plus success color */
}

.negative-text {
  color: #f56c6c; /* Element Plus danger color */
}

/* 微调表格内tag的样式，使其更紧凑 */
.el-table .el-tag {
    margin-right: 3px;
    margin-bottom: 3px;
}

.kline-chart-container {
  margin-top: 20px;
}
</style>