<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票数据可视化平台</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
          Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      }
      #app {
        height: 100%;
        width: 100%;
      }
      
      /* 加载状态样式 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #f0f2f5;
        z-index: 9999;
      }
      .loading-text {
        margin-top: 20px;
        font-size: 16px;
        color: #409EFF;
      }
      /* 当Vue挂载成功时隐藏 */
      .vue-mounted {
        display: none;
      }
    </style>
  </head>
  <body>
    <!-- 加载指示器 -->
    <div id="loading" class="loading-container">
      <div class="loading-text">正在加载应用...</div>
      <div id="loading-status"></div>
    </div>
    
    <!-- Vue应用容器 -->
    <div id="app"></div>
    
    <!-- 初始化脚本 -->
    <script>
      // 显示加载状态
      function updateLoadingStatus(message) {
        const statusEl = document.getElementById('loading-status');
        if (statusEl) {
          statusEl.innerHTML += `<div>${message}</div>`;
        }
      }
      
      // 初始状态
      updateLoadingStatus('正在初始化...');
      
      // 添加错误处理
      window.addEventListener('error', function(event) {
        console.error('全局错误:', event.error);
        updateLoadingStatus(`错误: ${event.message}`);
      });
      
      // 当Vue挂载成功后隐藏加载指示器
      window.hideLoading = function() {
        const loadingEl = document.getElementById('loading');
        if (loadingEl) {
          loadingEl.classList.add('vue-mounted');
        }
        updateLoadingStatus('应用加载完成!');
      }
      
      updateLoadingStatus('准备加载主脚本...');
    </script>
    
    <!-- 主应用脚本 -->
    <script type="module" src="/src/main.js"></script>
  </body>
</html> 