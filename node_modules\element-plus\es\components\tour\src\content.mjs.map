{"version": 3, "file": "content.mjs", "sources": ["../../../../../../packages/components/tour/src/content.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type { Placement, Strategy, VirtualElement } from '@floating-ui/dom'\n\nexport const tourStrategies = ['absolute', 'fixed'] as const\n\nexport const tourPlacements = [\n  'top-start',\n  'top-end',\n  'top',\n  'bottom-start',\n  'bottom-end',\n  'bottom',\n  'left-start',\n  'left-end',\n  'left',\n  'right-start',\n  'right-end',\n  'right',\n] as const\n\nexport const tourContentProps = buildProps({\n  /**\n   * @description position of the guide card relative to the target element\n   */\n  placement: {\n    type: definePropType<Placement>(String),\n    values: tourPlacements,\n    default: 'bottom',\n  },\n  /**\n   * @description the reference dom\n   */\n  reference: {\n    type: definePropType<HTMLElement | VirtualElement | null>(Object),\n    default: null,\n  },\n  /**\n   * @description position strategy of the content\n   */\n  strategy: {\n    type: definePropType<Strategy>(String),\n    values: tourStrategies,\n    default: 'absolute',\n  },\n  /**\n   * @description offset of the arrow\n   */\n  offset: {\n    type: Number,\n    default: 10,\n  },\n  /**\n   * @description @description whether to show the arrow\n   */\n  showArrow: Boolean,\n  /**\n   * @description content's zIndex\n   */\n  zIndex: {\n    type: Number,\n    default: 2001,\n  },\n})\n\nexport type TourContentProps = ExtractPropTypes<typeof tourContentProps>\n\nexport const tourContentEmits = {\n  close: () => true,\n}\nexport type TourContentEmits = typeof tourContentEmits\n"], "names": [], "mappings": ";;AACY,MAAC,cAAc,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE;AACxC,MAAC,cAAc,GAAG;AAC9B,EAAE,WAAW;AACb,EAAE,SAAS;AACX,EAAE,KAAK;AACP,EAAE,cAAc;AAChB,EAAE,YAAY;AACd,EAAE,QAAQ;AACV,EAAE,YAAY;AACd,EAAE,UAAU;AACZ,EAAE,MAAM;AACR,EAAE,aAAa;AACf,EAAE,WAAW;AACb,EAAE,OAAO;AACT,EAAE;AACU,MAAC,gBAAgB,GAAG,UAAU,CAAC;AAC3C,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,MAAM,EAAE,cAAc;AAC1B,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,MAAM,EAAE,cAAc;AAC1B,IAAI,OAAO,EAAE,UAAU;AACvB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,gBAAgB,GAAG;AAChC,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB;;;;"}