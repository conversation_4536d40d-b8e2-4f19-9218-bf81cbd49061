<template>
  <div class="best-combinations-table">
    <div class="table-header">
      <h3>最佳股票-周期组合</h3>
      <div class="table-actions">
        <el-select v-model="selectedMetric" placeholder="选择排序指标" style="width: 150px">
          <el-option
            v-for="item in sortMetrics"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button type="primary" plain size="small" @click="handleExport">
          导出数据
        </el-button>
      </div>
    </div>
    
    <!-- 最佳组合表格 -->
    <el-table
      :data="sortedCombinations"
      border
      style="width: 100%"
      height="500"
      :default-sort="{ prop: selectedMetric, order: 'descending' }"
    >
      <el-table-column label="股票信息" width="150" fixed="left">
        <template #default="scope">
          <div class="stock-info">
            <span class="stock-code">{{ scope.row.stockCode }}</span>
            <span class="stock-name">{{ scope.row.stockName }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="period" label="均线周期" width="120">
        <template #default="scope">
          <el-tag size="small">MA{{ scope.row.period }}</el-tag>
        </template>
      </el-table-column>
      
      <!-- 收益指标 -->
      <el-table-column prop="profitRate" label="收益率(%)" width="120" sortable>
        <template #default="scope">
          <span :class="getValueClass(scope.row.profitRate)">
            {{ formatNumber(scope.row.profitRate) }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column prop="annualizedReturn" label="年化收益(%)" width="120" sortable>
        <template #default="scope">
          <span :class="getValueClass(scope.row.annualizedReturn)">
            {{ formatNumber(scope.row.annualizedReturn) }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column prop="maxDrawdown" label="最大回撤(%)" width="120" sortable>
        <template #default="scope">
          <span class="negative-value">
            {{ formatNumber(scope.row.maxDrawdown) }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column prop="profitDrawdownRatio" label="收益回撤比" width="120" sortable>
        <template #default="scope">
          <span :class="getValueClass(scope.row.profitDrawdownRatio)">
            {{ formatNumber(scope.row.profitDrawdownRatio) }}
          </span>
        </template>
      </el-table-column>
      
      <!-- 交易指标 -->
      <el-table-column prop="transactionCount" label="交易次数" width="100" sortable />
      
      <el-table-column prop="winRate" label="胜率(%)" width="100" sortable>
        <template #default="scope">
          <span :class="getValueClass(scope.row.winRate)">
            {{ formatNumber(scope.row.winRate) }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column prop="avgProfitPerTrade" label="平均每笔收益" width="120" sortable>
        <template #default="scope">
          <span :class="getValueClass(scope.row.avgProfitPerTrade)">
            {{ formatNumber(scope.row.avgProfitPerTrade) }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column prop="avgHoldingDays" label="平均持仓天数" width="120" sortable />
      
      <el-table-column prop="sharpeRatio" label="夏普比率" width="100" sortable>
        <template #default="scope">
          <span :class="getValueClass(scope.row.sharpeRatio)">
            {{ formatNumber(scope.row.sharpeRatio) }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  results: {
    type: Array,
    default: () => []
  }
})

// 排序指标
const sortMetrics = [
  { label: '收益率', value: 'profitRate' },
  { label: '年化收益', value: 'annualizedReturn' },
  { label: '收益回撤比', value: 'profitDrawdownRatio' },
  { label: '夏普比率', value: 'sharpeRatio' },
  { label: '胜率', value: 'winRate' }
]

const selectedMetric = ref('profitRate')

// 计算所有组合
const allCombinations = computed(() => {
  if (!props.results.length) return []
  
  // 将所有结果转换为组合
  return props.results.map(result => {
    const profitDrawdownRatio = result.max_drawdown !== 0 
      ? Math.abs((result.profit_rate || 0) / (result.max_drawdown || 1)) 
      : 0
    
    const avgProfitPerTrade = result.transaction_count > 0 
      ? (result.profit_rate || 0) / result.transaction_count 
      : 0
    
    return {
      stockCode: result.stock_code,
      stockName: result.stock_name || '未知',
      period: result.period,
      profitRate: result.profit_rate || 0,
      annualizedReturn: result.annualized_return || 0,
      maxDrawdown: result.max_drawdown || 0,
      profitDrawdownRatio,
      transactionCount: result.transaction_count || 0,
      winRate: result.win_rate || 0,
      avgProfitPerTrade,
      avgHoldingDays: result.average_holding_days || 0,
      sharpeRatio: result.sharpe_ratio || 0
    }
  })
})

// 排序后的组合
const sortedCombinations = computed(() => {
  const combinations = [...allCombinations.value]
  
  if (selectedMetric.value === 'maxDrawdown') {
    // 对于回撤，值越小越好
    return combinations.sort((a, b) => a[selectedMetric.value] - b[selectedMetric.value])
  } else {
    // 对于其他指标，值越大越好
    return combinations.sort((a, b) => b[selectedMetric.value] - a[selectedMetric.value])
  }
})

// 格式化数字
const formatNumber = (value) => {
  if (value === undefined || value === null) return '-'
  return value.toFixed(2)
}

// 获取数值显示的CSS类
const getValueClass = (value) => {
  if (value === undefined || value === null) return ''
  return value > 0 ? 'positive-value' : value < 0 ? 'negative-value' : ''
}

// 导出数据
const handleExport = () => {
  try {
    // 创建CSV内容
    let csvContent = "股票代码,股票名称,均线周期,收益率(%),年化收益(%),最大回撤(%),收益回撤比,交易次数,胜率(%),平均每笔收益,平均持仓天数,夏普比率\n"
    
    // 添加每行数据
    allCombinations.value.forEach(combo => {
      csvContent += `${combo.stockCode},${combo.stockName},MA${combo.period},${formatNumber(combo.profitRate)},${formatNumber(combo.annualizedReturn)},${formatNumber(combo.maxDrawdown)},${formatNumber(combo.profitDrawdownRatio)},${combo.transactionCount},${formatNumber(combo.winRate)},${formatNumber(combo.avgProfitPerTrade)},${formatNumber(combo.avgHoldingDays)},${formatNumber(combo.sharpeRatio)}\n`
    })
    
    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    
    // 创建下载链接
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `最佳组合_${new Date().toISOString().split('T')[0]}.csv`)
    
    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + error.message)
  }
}
</script>

<style scoped>
.best-combinations-table {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.stock-info {
  display: flex;
  flex-direction: column;
}

.stock-code {
  font-weight: bold;
}

.stock-name {
  font-size: 0.8em;
  color: #888;
  margin-top: 3px;
}

.positive-value {
  color: #f56c6c;
  font-weight: bold;
}

.negative-value {
  color: #67c23a;
  font-weight: bold;
}
</style> 