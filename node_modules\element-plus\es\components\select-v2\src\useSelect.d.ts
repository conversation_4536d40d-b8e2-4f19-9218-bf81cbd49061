import type { Option, OptionType, SelectStates } from './select.types';
import type { ISelectV2Props } from './token';
import type { SelectEmitFn } from './defaults';
import type { TooltipInstance } from 'element-plus/es/components/tooltip';
import type { SelectDropdownInstance } from './select-dropdown';
declare const useSelect: (props: ISelectV2Props, emit: SelectEmitFn) => {
    inputId: import("vue").Ref<string | undefined>;
    collapseTagSize: import("vue").ComputedRef<"default" | "small">;
    currentPlaceholder: import("vue").ComputedRef<string>;
    expanded: import("vue").Ref<boolean>;
    emptyText: import("vue").ComputedRef<string | false | null>;
    popupHeight: import("vue").ComputedRef<number>;
    debounce: import("vue").ComputedRef<0 | 300>;
    allOptions: import("vue").ComputedRef<OptionType[]>;
    filteredOptions: import("vue").Ref<({
        [x: string]: any;
    } | {
        [x: string]: any;
        created?: boolean | undefined;
    })[]>;
    iconComponent: import("vue").ComputedRef<any>;
    iconReverse: import("vue").ComputedRef<any>;
    tagStyle: import("vue").ComputedRef<{
        maxWidth: string;
    }>;
    collapseTagStyle: import("vue").ComputedRef<{
        maxWidth: string;
    }>;
    popperSize: import("vue").Ref<number>;
    dropdownMenuVisible: import("vue").WritableComputedRef<boolean>;
    hasModelValue: import("vue").ComputedRef<boolean>;
    shouldShowPlaceholder: import("vue").ComputedRef<boolean>;
    selectDisabled: import("vue").ComputedRef<boolean | undefined>;
    selectSize: import("vue").ComputedRef<"" | "small" | "default" | "large">;
    needStatusIcon: import("vue").ComputedRef<boolean>;
    showClearBtn: import("vue").ComputedRef<boolean>;
    states: SelectStates;
    isFocused: import("vue").Ref<boolean>;
    nsSelect: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string) => string;
        m: (modifier?: string) => string;
        be: (blockSuffix?: string, element?: string) => string;
        em: (element?: string, modifier?: string) => string;
        bm: (blockSuffix?: string, modifier?: string) => string;
        bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    nsInput: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string) => string;
        m: (modifier?: string) => string;
        be: (blockSuffix?: string, element?: string) => string;
        em: (element?: string, modifier?: string) => string;
        bm: (blockSuffix?: string, modifier?: string) => string;
        bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    inputRef: import("vue").Ref<HTMLElement | undefined>;
    menuRef: import("vue").Ref<SelectDropdownInstance | undefined>;
    tagMenuRef: import("vue").Ref<HTMLElement | undefined>;
    tooltipRef: import("vue").Ref<TooltipInstance | undefined>;
    tagTooltipRef: import("vue").Ref<TooltipInstance | undefined>;
    selectRef: import("vue").Ref<HTMLElement | undefined>;
    wrapperRef: import("vue").ShallowRef<HTMLElement | undefined>;
    selectionRef: import("vue").Ref<HTMLElement | undefined>;
    prefixRef: import("vue").Ref<HTMLElement | undefined>;
    suffixRef: import("vue").Ref<HTMLElement | undefined>;
    collapseItemRef: import("vue").Ref<HTMLElement | undefined>;
    popperRef: import("vue").ComputedRef<HTMLElement | undefined>;
    validateState: import("vue").ComputedRef<"" | "error" | "success" | "validating">;
    validateIcon: import("vue").ComputedRef<any>;
    showTagList: import("vue").ComputedRef<Option[]>;
    collapseTagList: import("vue").ComputedRef<Option[]>;
    debouncedOnInputChange: import("lodash").DebouncedFunc<() => void>;
    deleteTag: (event: MouseEvent, option: Option) => void;
    getLabel: (option: Option) => any;
    getValue: (option: Option) => any;
    getDisabled: (option: Option) => any;
    getValueKey: (item: unknown) => any;
    handleClear: () => void;
    handleClickOutside: (event: Event) => void;
    handleDel: (e: KeyboardEvent) => void;
    handleEsc: () => void;
    focus: () => void;
    blur: () => void;
    handleMenuEnter: () => Promise<void>;
    handleResize: () => void;
    resetSelectionWidth: () => void;
    updateTooltip: () => void;
    updateTagTooltip: () => void;
    updateOptions: () => void;
    toggleMenu: () => void;
    scrollTo: (index: number) => void;
    onInput: (event: Event) => void;
    onKeyboardNavigate: (direction: "forward" | "backward", hoveringIndex?: number | undefined) => void;
    onKeyboardSelect: () => void;
    onSelect: (option: Option) => void;
    onHover: (idx?: number) => void;
    handleCompositionStart: (event: CompositionEvent) => void;
    handleCompositionEnd: (event: CompositionEvent) => void;
    handleCompositionUpdate: (event: CompositionEvent) => void;
};
export default useSelect;
