import request from './request'

/**
 * 获取股票K线数据
 * @param {Object} params 请求参数
 * @param {string} params.stock_code 股票代码
 * @param {string} params.start_date 开始日期，格式为YYYY-MM-DD
 * @param {string} params.end_date 结束日期，格式为YYYY-MM-DD
 * @returns {Promise<Array>} 股票K线数据数组
 */
export function getStockData(params) {
  return request({
    url: '/stock_data',
    method: 'get',
    params
  })
}

/**
 * 计算移动平均线数据
 * @param {Array} data 原始K线数据
 * @param {number} period 周期，如5、10、20等
 * @returns {Array} 计算后的MA数据
 */
export function calculateMA(data, period) {
  if (!data || data.length === 0) return []
  
  const result = []
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      // 不足周期的数据，MA为空
      result.push('-')
      continue
    }
    
    let sum = 0
    for (let j = 0; j < period; j++) {
      sum += data[i - j].close
    }
    result.push(+(sum / period).toFixed(2))
  }
  
  return result
}

/**
 * 转换股票数据为K线图所需格式
 * @param {Array} data 原始K线数据
 * @returns {Object} 格式化后的K线图数据
 */
export function formatKLineData(data) {
  if (!data || data.length === 0) return { dates: [], values: [] }
  
  const dates = []
  const values = []
  
  data.forEach(item => {
    dates.push(item.trade_date)
    values.push([
      item.open,  // 开盘价
      item.close, // 收盘价
      item.low,   // 最低价
      item.high,  // 最高价
      item.volume // 成交量
    ])
  })
  
  return { dates, values }
}

/**
 * 转换股票数据为周K或月K
 * @param {Array} dailyData 日K数据
 * @param {string} type 'week'或'month'
 * @returns {Array} 转换后的数据
 */
export function convertToWeekOrMonthData(dailyData, type) {
  if (!dailyData || dailyData.length === 0) return []
  
  const result = []
  let current = null
  
  // 按日期排序
  const sortedData = [...dailyData].sort((a, b) => 
    new Date(a.trade_date) - new Date(b.trade_date)
  )
  
  sortedData.forEach(item => {
    const date = new Date(item.trade_date)
    const year = date.getFullYear()
    const month = date.getMonth()
    const week = getWeekNumber(date)
    
    // 确定分组键
    const key = type === 'week' 
      ? `${year}-W${week}` 
      : `${year}-${month + 1}`
    
    if (!current || current.key !== key) {
      // 创建新的周/月数据
      if (current) {
        result.push(current.data)
      }
      
      current = {
        key,
        data: {
          trade_date: item.trade_date,
          open: item.open,
          high: item.high,
          low: item.low,
          close: item.close,
          volume: item.volume,
          amount: item.amount
        }
      }
    } else {
      // 更新当前周/月数据
      current.data.high = Math.max(current.data.high, item.high)
      current.data.low = Math.min(current.data.low, item.low)
      current.data.close = item.close // 用最后一天的收盘价
      current.data.volume += item.volume
      current.data.amount += item.amount
    }
  })
  
  // 添加最后一组数据
  if (current) {
    result.push(current.data)
  }
  
  return result
}

/**
 * 获取日期所在的周数
 * @param {Date} date 日期对象
 * @returns {number} 周数
 */
function getWeekNumber(date) {
  const firstDayOfYear = new Date(date.getFullYear(), 0, 1)
  const pastDaysOfYear = (date - firstDayOfYear) / 86400000
  return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7)
} 