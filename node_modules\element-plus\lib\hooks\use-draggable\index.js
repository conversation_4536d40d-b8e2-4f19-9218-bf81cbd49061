'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var style = require('../../utils/dom/style.js');

const useDraggable = (targetRef, dragRef, draggable, overflow) => {
  let transform = {
    offsetX: 0,
    offsetY: 0
  };
  const onMousedown = (e) => {
    const downX = e.clientX;
    const downY = e.clientY;
    const { offsetX, offsetY } = transform;
    const targetRect = targetRef.value.getBoundingClientRect();
    const targetLeft = targetRect.left;
    const targetTop = targetRect.top;
    const targetWidth = targetRect.width;
    const targetHeight = targetRect.height;
    const clientWidth = document.documentElement.clientWidth;
    const clientHeight = document.documentElement.clientHeight;
    const minLeft = -targetLeft + offsetX;
    const minTop = -targetTop + offsetY;
    const maxLeft = clientWidth - targetLeft - targetWidth + offsetX;
    const maxTop = clientHeight - targetTop - targetHeight + offsetY;
    const onMousemove = (e2) => {
      let moveX = offsetX + e2.clientX - downX;
      let moveY = offsetY + e2.clientY - downY;
      if (!(overflow == null ? void 0 : overflow.value)) {
        moveX = Math.min(Math.max(moveX, minLeft), maxLeft);
        moveY = Math.min(Math.max(moveY, minTop), maxTop);
      }
      transform = {
        offsetX: moveX,
        offsetY: moveY
      };
      if (targetRef.value) {
        targetRef.value.style.transform = `translate(${style.addUnit(moveX)}, ${style.addUnit(moveY)})`;
      }
    };
    const onMouseup = () => {
      document.removeEventListener("mousemove", onMousemove);
      document.removeEventListener("mouseup", onMouseup);
    };
    document.addEventListener("mousemove", onMousemove);
    document.addEventListener("mouseup", onMouseup);
  };
  const onDraggable = () => {
    if (dragRef.value && targetRef.value) {
      dragRef.value.addEventListener("mousedown", onMousedown);
    }
  };
  const offDraggable = () => {
    if (dragRef.value && targetRef.value) {
      dragRef.value.removeEventListener("mousedown", onMousedown);
    }
  };
  const resetPosition = () => {
    transform = {
      offsetX: 0,
      offsetY: 0
    };
    if (targetRef.value) {
      targetRef.value.style.transform = "none";
    }
  };
  vue.onMounted(() => {
    vue.watchEffect(() => {
      if (draggable.value) {
        onDraggable();
      } else {
        offDraggable();
      }
    });
  });
  vue.onBeforeUnmount(() => {
    offDraggable();
  });
  return {
    resetPosition
  };
};

exports.useDraggable = useDraggable;
//# sourceMappingURL=index.js.map
