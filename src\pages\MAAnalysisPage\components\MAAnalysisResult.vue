<template>
  <div class="ma-analysis-result">
    <el-card shadow="hover" v-if="isLoading">
      <div class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
    </el-card>
    
    <el-card shadow="hover" v-else-if="analysisData && Object.keys(periodAnalyses).length > 0">
      <template #header>
        <div class="card-header">
          <h3>均线位置分析结果</h3>
          <div class="stock-info">
            <span class="stock-code">{{ stockCode }}</span>
            <span class="date-range">{{ startDate }} 至 {{ endDate }}</span>
          </div>
        </div>
      </template>
      
      <div class="analysis-content">
        <el-table :data="tableData" border stripe style="width: 100%">
          <el-table-column prop="period" label="均线周期" width="100">
            <template #default="scope">
              <span>MA{{ scope.row.period }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="aboveCount" label="价格位于上方天数" width="140">
            <template #default="scope">
              <el-progress 
                :percentage="calculatePercentage(scope.row.aboveCount, scope.row.totalDays)" 
                :stroke-width="15"
                :color="scope.row.trendColor"
                :format="() => scope.row.aboveCount"
              />
            </template>
          </el-table-column>
          
          <el-table-column prop="belowCount" label="价格位于下方天数" width="140">
            <template #default="scope">
              <el-progress 
                :percentage="calculatePercentage(scope.row.belowCount, scope.row.totalDays)" 
                :stroke-width="15"
                color="#909399"
                :format="() => scope.row.belowCount"
              />
            </template>
          </el-table-column>
          
          <el-table-column prop="maxConsecutiveAbove" label="最大连续上方天数" width="140" />
          
          <el-table-column prop="maxConsecutiveBelow" label="最大连续下方天数" width="140" />
          
          <el-table-column prop="aboveRatio" label="上方占比" width="100">
            <template #default="scope">
              <span :style="{ color: scope.row.trendColor }">{{ scope.row.aboveRatio.toFixed(2) }}%</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="trendJudgment" label="趋势判断" min-width="120">
            <template #default="scope">
              <el-tag :color="scope.row.trendColor" :style="{ color: getContrastColor(scope.row.trendColor) }">
                {{ scope.row.trendJudgment }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="analysis-summary" v-if="tableData.length > 0">
          <h4>综合分析</h4>
          <p>
            在分析周期内（共{{ totalDays }}个交易日），股票价格相对于各均线的位置情况表明：
          </p>
          <ul>
            <li v-for="(row, index) in tableData.slice(0, 3)" :key="index">
              MA{{ row.period }}均线：价格位于上方占比{{ row.aboveRatio.toFixed(2) }}%，
              最大连续上涨{{ row.maxConsecutiveAbove }}天，最大连续下跌{{ row.maxConsecutiveBelow }}天，
              整体呈<span :style="{ color: row.trendColor, fontWeight: 'bold' }">{{ row.trendJudgment }}</span>。
            </li>
          </ul>
          <p>
            <strong>投资建议：</strong>
            {{ generateInvestmentSuggestion() }}
          </p>
        </div>
      </div>
    </el-card>
    
    <el-empty v-else description="暂无数据，请先执行分析" />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { generateTrendAnalysis } from '@/api/maAnalysis'

const props = defineProps({
  analysisData: {
    type: Object,
    default: () => ({})
  },
  stockCode: {
    type: String,
    default: ''
  },
  startDate: {
    type: String,
    default: ''
  },
  endDate: {
    type: String,
    default: ''
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})

// 计算处理后的周期分析数据
const periodAnalyses = computed(() => {
  if (!props.analysisData) return {}
  
  // 直接从原始数据中提取周期分析结果
  // 首先检查 periods 数组格式
  if (props.analysisData.periods && Array.isArray(props.analysisData.periods)) {
    console.log('从periods数组提取均线分析数据')
    // 处理新格式: {periods: [{period: 5, above_count: 10, ...}]}
    const result = {}
    props.analysisData.periods.forEach(periodData => {
      if (periodData && periodData.period) {
        result[periodData.period] = {
          above_count: periodData.above_count || 0,
          below_count: periodData.below_count || 0,
          max_consecutive_above: periodData.max_consecutive_above || 0,
          max_consecutive_below: periodData.max_consecutive_below || 0,
          ma_values: periodData.ma_values || []
        }
      }
    })
    return result
  }
  
  // 然后检查 period_analyses 对象格式
  if (props.analysisData.period_analyses) {
    console.log('使用period_analyses对象')
    return props.analysisData.period_analyses
  }
  
  return {}
})

// 计算总天数
const totalDays = computed(() => {
  if (!props.analysisData) return 0
  return props.analysisData.total_days || 0
})

// 计算表格数据
const tableData = computed(() => {
  if (!periodAnalyses.value) return []
  
  return Object.keys(periodAnalyses.value)
    .map(period => {
      const analysis = periodAnalyses.value[period]
      const enhancedAnalysis = generateTrendAnalysis(analysis)
      
      return {
        period: Number(period),
        aboveCount: enhancedAnalysis.above_count,
        belowCount: enhancedAnalysis.below_count,
        maxConsecutiveAbove: enhancedAnalysis.max_consecutive_above,
        maxConsecutiveBelow: enhancedAnalysis.max_consecutive_below,
        totalDays: enhancedAnalysis.above_count + enhancedAnalysis.below_count,
        aboveRatio: enhancedAnalysis.aboveRatio,
        trendJudgment: enhancedAnalysis.trendJudgment,
        trendColor: enhancedAnalysis.trendColor
      }
    })
    .sort((a, b) => a.period - b.period)
})

// 计算百分比
const calculatePercentage = (value, total) => {
  if (!total) return 0
  return (value / total) * 100
}

// 生成投资建议
const generateInvestmentSuggestion = () => {
  if (tableData.value.length === 0) return '无法提供建议，数据不足'
  
  // 根据短中长期均线判断趋势
  const shortTermMA = tableData.value.find(item => item.period === 5 || item.period === 10)
  const midTermMA = tableData.value.find(item => item.period === 20 || item.period === 30)
  const longTermMA = tableData.value.find(item => item.period === 60)
  
  if (!shortTermMA || !midTermMA || !longTermMA) {
    return '建议关注MA5、MA20和MA60均线的组合形态，综合判断趋势。'
  }
  
  // 强多头：短中长期均线都是上升趋势
  if (shortTermMA.aboveRatio > 60 && midTermMA.aboveRatio > 55 && longTermMA.aboveRatio > 55) {
    return '多头排列明显，短中长期均线呈上升趋势，可考虑持股待涨或逢低买入。'
  }
  
  // 强空头：短中长期均线都是下降趋势
  if (shortTermMA.aboveRatio < 40 && midTermMA.aboveRatio < 45 && longTermMA.aboveRatio < 45) {
    return '空头排列明显，短中长期均线呈下降趋势，建议控制仓位或观望等待反转信号。'
  }
  
  // 多头反转：短期均线上升，中长期均线下降
  if (shortTermMA.aboveRatio > 55 && midTermMA.aboveRatio < 50 && longTermMA.aboveRatio < 50) {
    return '可能处于多头反转初期，短期均线已向上，但中长期均线仍在下降，可小仓位试探性建仓，密切关注后续变化。'
  }
  
  // 空头反转：短期均线下降，中长期均线上升
  if (shortTermMA.aboveRatio < 45 && midTermMA.aboveRatio > 50 && longTermMA.aboveRatio > 50) {
    return '可能处于空头反转初期，短期弱势但中长期均线仍向好，建议降低仓位，等待进一步确认信号。'
  }
  
  // 震荡市场
  return '市场可能处于震荡调整阶段，建议等待明确的趋势信号出现再做决策，控制仓位规避风险。'
}

// 获取对比色（确保标签文字在背景色上清晰可见）
const getContrastColor = (hexColor) => {
  // 简化的亮度计算，用于确定文字是用黑色还是白色
  if (!hexColor || hexColor === '#909399') return '#FFFFFF'
  
  // 去掉#号
  const color = hexColor.replace('#', '')
  
  // 将颜色转换为RGB
  const r = parseInt(color.substr(0, 2), 16)
  const g = parseInt(color.substr(2, 2), 16)
  const b = parseInt(color.substr(4, 2), 16)
  
  // 计算亮度
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  
  // 如果亮度高于128，返回黑色，否则返回白色
  return brightness > 128 ? '#000000' : '#FFFFFF'
}
</script>

<style scoped>
.ma-analysis-result {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.stock-info {
  display: flex;
  gap: 10px;
}

.stock-code {
  font-weight: bold;
  color: #409EFF;
}

.date-range {
  color: #606266;
}

.loading-container {
  padding: 20px 0;
}

.analysis-content {
  margin-top: 15px;
}

.analysis-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.analysis-summary h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #303133;
}

.analysis-summary p {
  margin-bottom: 10px;
  line-height: 1.6;
  color: #606266;
}

.analysis-summary ul {
  margin-bottom: 15px;
  padding-left: 20px;
}

.analysis-summary li {
  margin-bottom: 8px;
  line-height: 1.6;
  color: #606266;
}
</style> 