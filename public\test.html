<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      margin: 0;
      background-color: #f0f2f5;
    }
    .container {
      text-align: center;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #409EFF;
    }
    p {
      margin: 20px 0;
    }
    button {
      background-color: #409EFF;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #337ecc;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>静态测试页面</h1>
    <p>如果您能看到此页面，说明服务器基本功能正常。</p>
    <p>这是一个不依赖Vue.js的纯HTML页面。</p>
    <button onclick="alert('按钮点击测试 - 基础功能正常')">点击测试</button>
  </div>

  <script>
    console.log('测试页面已加载')
  </script>
</body>
</html> 