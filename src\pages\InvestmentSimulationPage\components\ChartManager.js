import * as echarts from 'echarts'
import { getMALineColor } from '@/api/maAnalysis'

/**
 * 图表管理器类
 * 负责处理K线图和资产图的初始化、渲染和联动
 */
export class ChartManager {
  constructor() {
    this.klineInstance = null
    this.assetInstance = null
    this.maData = {}
    this.maVisibility = {}
    this.chartGroup = 'stock'
  }

  /**
   * 初始化图表实例
   * @param {HTMLElement} klineElement K线图DOM元素
   * @param {HTMLElement} assetElement 资产图DOM元素
   * @returns {boolean} 初始化是否成功
   */
  initChartInstances(klineElement, assetElement) {
    if (!klineElement || !assetElement) {
      console.error('图表容器未找到')
      return false
    }

    // 销毁现有实例（如果有）
    this.disposeCharts()

    try {
      // 初始化图表实例
      console.log('初始化K线图实例')
      this.klineInstance = echarts.init(klineElement)
      console.log('初始化资产图实例')
      this.assetInstance = echarts.init(assetElement)

      // 注册图例切换事件监听
      this.registerLegendEventHandlers()

      // 处理图表联动
      this.klineInstance.group = this.chartGroup
      this.assetInstance.group = this.chartGroup
      echarts.connect(this.chartGroup)

      return true
    } catch (error) {
      console.error('初始化图表实例失败:', error)
      return false
    }
  }

  /**
   * 注册图例事件处理器
   */
  registerLegendEventHandlers() {
    if (!this.assetInstance) return

    this.assetInstance.on('legendselectchanged', (params) => {
      const { name } = params
      // 如果是资产或投入曲线被点击
      if (name.includes('MA') && (name.includes('资产') || name.includes('投入'))) {
        // 提取MA周期
        const period = name.replace('MA', '').replace('资产', '').replace('投入', '')
        // 切换均线选择状态
        this.maVisibility[period] = !this.maVisibility[period]
        // 更新图表
        this.updateMAVisibility()
      }
    })
  }

  /**
   * 设置均线数据
   * @param {Object} maData 均线数据
   */
  setMAData(maData) {
    this.maData = maData
  }

  /**
   * 设置均线可见性
   * @param {Object} maVisibility 均线可见性设置
   */
  setMAVisibility(maVisibility) {
    this.maVisibility = maVisibility
  }

  /**
   * 渲染K线图和资产图
   * @param {Object} chartData 图表数据
   * @param {Array} chartData.dates 日期数组
   * @param {Array} chartData.klineData K线数据
   * @param {Object} chartData.buyPoints 买入点数据
   * @param {Object} chartData.sellPoints 卖出点数据
   * @param {Object} chartData.assetData 资产数据
   * @param {Object} chartData.investmentData 投入数据
   * @returns {boolean} 渲染是否成功
   */
  renderCharts(chartData) {
    const { dates, klineData, buyPoints, sellPoints, assetData, investmentData } = chartData

    if (!this.klineInstance || !this.assetInstance) {
      console.error('图表实例未初始化')
      return false
    }

    try {
      // 设置K线图配置
      const klineOption = this.createKlineOption(dates, klineData, buyPoints, sellPoints)
      console.log('设置K线图配置')
      this.klineInstance.setOption(klineOption, true)

      // 设置资产图配置
      const assetOption = this.createAssetOption(dates, assetData, investmentData)
      console.log('设置资产图配置')
      this.assetInstance.setOption(assetOption, true)

      return true
    } catch (error) {
      console.error('渲染图表失败:', error)
      return false
    }
  }

  /**
   * 创建K线图配置项
   * @param {Array} dates 日期数组
   * @param {Array} klineData K线数据
   * @param {Object} buyPoints 买入点数据
   * @param {Object} sellPoints 卖出点数据
   * @returns {Object} echarts配置项
   */
  createKlineOption(dates, klineData, buyPoints, sellPoints) {
    return {
      animation: false,
      title: {
        text: '股票K线图与买卖点',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function (params) {
          const date = params[0].axisValue
          let html = `${date}<br/>`
          
          // 检查是否有K线数据
          const klineParam = params.find(p => p.seriesName === 'K线')
          if (klineParam && klineParam.data && klineParam.data.length >= 4) {
            html += `开盘：${klineParam.data[1]}<br/>`
            html += `收盘：${klineParam.data[2]}<br/>`
            html += `最低：${klineParam.data[3]}<br/>`
            html += `最高：${klineParam.data[4]}<br/>`
          }
          
          // 买卖点数据
          params.forEach(param => {
            if (param.seriesName.includes('买入') || param.seriesName.includes('卖出')) {
              html += `${param.seriesName}：${param.data[1]}<br/>`
            }
          })
          
          return html
        }
      },
      legend: {
        data: ['K线', ...Object.keys(buyPoints).map(p => `MA${p}买入`), ...Object.keys(sellPoints).map(p => `MA${p}卖出`)],
        bottom: 10,
        selected: {
          'K线': klineData.some(data => data.some(val => val > 0)) // 只有当K线数据有效时才默认显示
        }
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '15%'
      },
      xAxis: {
        type: 'category',
        data: dates,
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        splitLine: { show: false },
        min: 'dataMin',
        max: 'dataMax'
      },
      yAxis: {
        scale: true,
        splitArea: {
          show: true
        }
      },
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: 100
        },
        {
          show: true,
          type: 'slider',
          bottom: 60,
          start: 0,
          end: 100
        }
      ],
      series: [
        // 只有当K线数据有效时才添加K线图
        ...(klineData.some(data => data.some(val => val > 0)) ? [{
          name: 'K线',
          type: 'candlestick',
          data: klineData,
          itemStyle: {
            color: '#ef232a',
            color0: '#14b143',
            borderColor: '#ef232a',
            borderColor0: '#14b143'
          }
        }] : []),
        // 添加均线
        ...Object.entries(this.maData)
          .filter(([period]) => this.maVisibility[period])
          .map(([period, data]) => ({
            name: `MA${period}`,
            type: 'line',
            data: data,
            smooth: true,
            symbol: 'none',
            lineStyle: {
              width: 2,
              color: getMALineColor(Number(period))
            },
            z: 1
          })),
        // 添加买入点标记
        ...Object.entries(buyPoints).map(([period, points]) => ({
          name: `MA${period}买入`,
          type: 'scatter',
          data: points.map(p => [p.date, p.price]),
          symbol: 'arrow',
          symbolSize: 10,
          symbolRotate: -90,
          itemStyle: {
            color: '#FF9800'
          },
          label: {
            show: true,
            position: 'top',
            formatter: 'B'
          }
        })),
        // 添加卖出点标记
        ...Object.entries(sellPoints).map(([period, points]) => ({
          name: `MA${period}卖出`,
          type: 'scatter',
          data: points.map(p => [p.date, p.price]),
          symbol: 'arrow',
          symbolSize: 10,
          symbolRotate: 90,
          itemStyle: {
            color: '#2196F3'
          },
          label: {
            show: true,
            position: 'bottom',
            formatter: 'S'
          }
        }))
      ]
    }
  }

  /**
   * 创建资产图配置项
   * @param {Array} dates 日期数组
   * @param {Object} assetData 资产数据
   * @param {Object} investmentData 投入数据
   * @returns {Object} echarts配置项
   */
  createAssetOption(dates, assetData, investmentData) {
    // 只添加被选中的周期的资产和投入曲线
    const selectedPeriods = Object.keys(this.maVisibility).filter(period => this.maVisibility[period])
    
    // 资产曲线
    const assetSeries = Object.entries(assetData)
      .filter(([period, data]) => data.length > 0 && selectedPeriods.includes(period))
      .map(([period, data]) => ({
        name: `MA${period}资产`,
        type: 'line',
        data: data,
        symbol: 'none',
        lineStyle: {
          width: 2,
          color: getMALineColor(parseInt(period))
        },
        emphasis: {
          lineStyle: {
            width: 3
          }
        }
      }))
    
    // 投入曲线
    const investmentSeries = Object.entries(investmentData)
      .filter(([period, data]) => data.length > 0 && selectedPeriods.includes(period))
      .map(([period, data]) => ({
        name: `MA${period}投入`,
        type: 'line',
        data: data,
        symbol: 'none',
        lineStyle: {
          width: 1,
          type: 'dashed',
          color: getMALineColor(parseInt(period))
        }
      }))
    
    // 系列数据
    const series = [...assetSeries, ...investmentSeries]

    return {
      animation: false,
      title: {
        text: '资产与投入曲线',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          const date = params[0].axisValue
          let html = `${date}<br/>`
          params.forEach(param => {
            if (param.data && param.data.length > 1) {
              const value = parseFloat(param.data[1]).toFixed(2)
              html += `${param.seriesName}：${value}<br/>`
            }
          })
          return html
        }
      },
      legend: {
        data: series.map(s => s.name),
        bottom: 10,
        selected: {}
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '15%'
      },
      xAxis: {
        type: 'category',
        data: dates,
        boundaryGap: false,
        axisLine: { onZero: false },
        splitLine: { show: false },
        min: 'dataMin',
        max: 'dataMax'
      },
      yAxis: {
        type: 'value',
        scale: true,
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: 100
        },
        {
          show: true,
          type: 'slider',
          bottom: 60,
          start: 0,
          end: 100
        }
      ],
      series
    }
  }

  /**
   * 更新均线可见性
   */
  updateMAVisibility() {
    this.updateKlineMAVisibility()
    this.updateAssetVisibility()
  }

  /**
   * 更新K线图的均线可见性
   */
  updateKlineMAVisibility() {
    if (!this.klineInstance) return
    
    try {
      // 重新配置K线图的均线系列
      const option = this.klineInstance.getOption()
      
      // 保存原始系列数据（K线和买卖点）
      const originalSeries = option.series.filter(s => 
        s.name === 'K线' || s.name.includes('买入') || s.name.includes('卖出')
      )
      
      // 创建均线系列
      const maSeries = Object.entries(this.maData)
        .filter(([period]) => this.maVisibility[period])
        .map(([period, data]) => ({
          name: `MA${period}`,
          type: 'line',
          data: data,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            width: 2,
            color: getMALineColor(Number(period))
          },
          z: 1
        }))
      
      // 更新图例数据
      option.legend.data = [
        'K线', 
        ...Object.keys(this.maVisibility)
          .filter(period => this.maVisibility[period])
          .map(period => `MA${period}`),
        ...originalSeries
          .filter(s => s.name.includes('买入') || s.name.includes('卖出'))
          .map(s => s.name)
      ]
      
      // 组合新的系列数据
      option.series = [...originalSeries, ...maSeries]
      
      // 应用更新
      this.klineInstance.setOption(option, true)
    } catch (error) {
      console.error('更新K线图均线可见性出错:', error)
    }
  }

  /**
   * 更新资产图可见性
   */
  updateAssetVisibility() {
    if (!this.assetInstance) return
    
    try {
      console.log('更新资产图可见性', Object.keys(this.maVisibility).map(p => `${p}: ${this.maVisibility[p]}`))
      
      // 获取当前的选中的周期
      const visiblePeriods = Object.keys(this.maVisibility).filter(period => this.maVisibility[period])
      
      // 获取当前的资产图配置
      const option = this.assetInstance.getOption()
      
      // 创建新的系列数据数组
      const newSeries = []
      
      // 从现有系列中筛选出资产和投入曲线
      option.series.forEach(series => {
        const name = series.name
        if (name.includes('资产') || name.includes('投入')) {
          const period = name.replace('MA', '').replace('资产', '').replace('投入', '')
          
          // 只保留勾选的周期对应的曲线
          if (visiblePeriods.includes(period)) {
            newSeries.push(series)
          }
        }
      })
      
      // 更新系列数据
      option.series = newSeries
      
      // 更新图例数据
      option.legend.data = newSeries.map(s => s.name)
      
      // 应用更新
      this.assetInstance.setOption(option, true)
    } catch (error) {
      console.error('更新资产图可见性出错:', error)
    }
  }

  /**
   * 调整图表大小
   */
  resize() {
    if (this.klineInstance) this.klineInstance.resize()
    if (this.assetInstance) this.assetInstance.resize()
  }

  /**
   * 销毁图表实例
   */
  disposeCharts() {
    if (this.klineInstance) {
      this.klineInstance.dispose()
      this.klineInstance = null
    }
    if (this.assetInstance) {
      this.assetInstance.dispose()
      this.assetInstance = null
    }
    echarts.disconnect(this.chartGroup)
  }
}

// 导出单例实例
export const chartManager = new ChartManager() 