<template>
  <div class="transaction-list-container">
    <el-card shadow="hover" class="transaction-card">
      <template #header>
        <div class="card-header">
          <span>交易明细</span>
          <div class="header-right">
            <el-tag 
              v-if="selectedPeriod" 
              type="info" 
              size="small"
              class="period-tag"
            >
              周期: {{ selectedPeriod }}天
            </el-tag>
            <el-input
              v-model="searchQuery"
              placeholder="搜索交易..."
              prefix-icon="Search"
              clearable
              size="small"
              style="width: 200px; margin-left: 10px;"
            />
          </div>
        </div>
      </template>
      
      <div v-if="!hasTransactions" class="empty-state">
        <el-empty description="暂无交易记录">
          <template #image>
            <el-icon class="empty-icon"><document /></el-icon>
          </template>
        </el-empty>
      </div>
      
      <div v-else>
        <!-- 交易统计信息 -->
        <div class="transaction-summary">
          <div class="summary-item">
            <span class="summary-label">交易次数:</span>
            <span class="summary-value">{{ statistics.transactionCount }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">盈利次数:</span>
            <span class="summary-value positive-value">{{ statistics.winCount }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">亏损次数:</span>
            <span class="summary-value negative-value">{{ statistics.lossCount }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">胜率:</span>
            <span class="summary-value">{{ statistics.winRate }}%</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">平均持仓:</span>
            <span class="summary-value">{{ statistics.avgHoldDays }}天</span>
          </div>
        </div>
        
        <el-divider />
        
        <!-- 交易列表 -->
        <el-table
          :data="filteredTransactions"
          stripe
          style="width: 100%"
          max-height="400px"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="date" label="日期" width="100" sortable />
          <el-table-column prop="action" label="交易类型" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.action === 'buy' ? 'danger' : 'success'" size="small">
                {{ scope.row.action === 'buy' ? '买入' : '卖出' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="价格" width="90" sortable>
            <template #default="scope">
              {{ scope.row.price }}元
            </template>
          </el-table-column>
          <el-table-column prop="shares" label="股数" width="90" sortable>
            <template #default="scope">
              {{ scope.row.shares }}股
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" width="120" sortable>
            <template #default="scope">
              {{ scope.row.amount }}元
            </template>
          </el-table-column>
          <el-table-column prop="profit" label="盈亏" width="120" sortable>
            <template #default="scope">
              <span :class="getValueClass(scope.row.profit)">
                {{ scope.row.profit > 0 ? '+' : '' }}{{ scope.row.profit }}元
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="profitPct" label="盈亏率" width="90" sortable>
            <template #default="scope">
              <span :class="getValueClass(scope.row.profitPct)">
                {{ scope.row.profitPct > 0 ? '+' : '' }}{{ scope.row.profitPct }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="holdDays" label="持仓天数" sortable>
            <template #default="scope">
              {{ scope.row.holdDays || '-' }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { Document, Search } from '@element-plus/icons-vue'
import { extractTransactionList, extractStatistics } from '../utils/dataProcessor'

export default {
  name: 'TransactionList',
  components: {
    Document,
    Search
  },
  props: {
    // 模拟结果数据
    simulationResult: {
      type: Object,
      default: () => ({})
    },
    // 选中的周期
    selectedPeriod: {
      type: [Number, String],
      default: null
    }
  },
  setup(props) {
    // 搜索关键词
    const searchQuery = ref('')
    
    // 获取当前周期的交易列表
    const transactions = computed(() => {
      if (!props.simulationResult || !props.simulationResult.period_results) {
        return []
      }
      
      // 如果没有选中特定周期，则返回空数组
      if (props.selectedPeriod === null) {
        return []
      }
      
      return extractTransactionList(props.simulationResult, props.selectedPeriod)
    })
    
    // 根据搜索关键词过滤交易列表
    const filteredTransactions = computed(() => {
      if (!searchQuery.value) {
        return transactions.value
      }
      
      const query = searchQuery.value.toLowerCase()
      return transactions.value.filter(transaction => {
        return (
          transaction.date.toLowerCase().includes(query) ||
          transaction.action.toLowerCase().includes(query) ||
          transaction.price.toString().includes(query) ||
          transaction.shares.toString().includes(query) ||
          transaction.amount.toString().includes(query) ||
          (transaction.profit && transaction.profit.toString().includes(query)) ||
          (transaction.profitPct && transaction.profitPct.toString().includes(query)) ||
          (transaction.holdDays && transaction.holdDays.toString().includes(query))
        )
      })
    })
    
    // 获取统计信息
    const statistics = computed(() => {
      if (!props.simulationResult || !props.simulationResult.period_results || props.selectedPeriod === null) {
        return {
          transactionCount: 0,
          winCount: 0,
          lossCount: 0,
          winRate: '0.00',
          avgHoldDays: '0.00'
        }
      }
      
      const stats = extractStatistics(props.simulationResult, props.selectedPeriod)
      return stats
    })
    
    // 判断是否有交易数据
    const hasTransactions = computed(() => {
      return transactions.value.length > 0
    })
    
    // 获取数值的样式类
    const getValueClass = (value) => {
      const numValue = parseFloat(value)
      if (numValue > 0) return 'positive-value'
      if (numValue < 0) return 'negative-value'
      return 'neutral-value'
    }
    
    // 监听周期变化，重置搜索关键词
    watch(() => props.selectedPeriod, () => {
      searchQuery.value = ''
    })
    
    return {
      searchQuery,
      filteredTransactions,
      statistics,
      hasTransactions,
      getValueClass
    }
  }
}
</script>

<style scoped>
.transaction-list-container {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.period-tag {
  margin-right: 10px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.empty-icon {
  font-size: 60px;
  color: #909399;
}

.transaction-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 10px 0;
}

.summary-item {
  display: flex;
  align-items: center;
}

.summary-label {
  margin-right: 5px;
  color: #606266;
}

.summary-value {
  font-weight: bold;
}

.positive-value {
  color: #67C23A;
  font-weight: bold;
}

.negative-value {
  color: #F56C6C;
  font-weight: bold;
}

.neutral-value {
  color: #909399;
}

.transaction-card {
  margin-bottom: 20px;
}
</style> 