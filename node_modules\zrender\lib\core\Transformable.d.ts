import * as matrix from './matrix';
import * as vector from './vector';
declare class Transformable {
    parent: Transformable;
    x: number;
    y: number;
    scaleX: number;
    scaleY: number;
    skewX: number;
    skewY: number;
    rotation: number;
    anchorX: number;
    anchorY: number;
    originX: number;
    originY: number;
    globalScaleRatio: number;
    transform: matrix.MatrixArray;
    invTransform: matrix.MatrixArray;
    getLocalTransform(m?: matrix.MatrixArray): matrix.MatrixArray;
    setPosition(arr: number[]): void;
    setScale(arr: number[]): void;
    setSkew(arr: number[]): void;
    setOrigin(arr: number[]): void;
    needLocalTransform(): boolean;
    updateTransform(): void;
    private _resolveGlobalScaleRatio;
    getComputedTransform(): matrix.MatrixArray;
    setLocalTransform(m: vector.VectorArray): void;
    decomposeTransform(): void;
    getGlobalScale(out?: vector.VectorArray): vector.VectorArray;
    transformCoordToLocal(x: number, y: number): number[];
    transformCoordToGlobal(x: number, y: number): number[];
    getLineScale(): number;
    copyTransform(source: Transformable): void;
    static getLocalTransform(target: Transformable, m?: matrix.MatrixArray): matrix.MatrixArray;
    private static initDefaultProps;
}
export declare const TRANSFORMABLE_PROPS: readonly ["x", "y", "originX", "originY", "anchorX", "anchorY", "rotation", "scaleX", "scaleY", "skewX", "skewY"];
export declare type TransformProp = (typeof TRANSFORMABLE_PROPS)[number];
export declare function copyTransform(target: Partial<Pick<Transformable, TransformProp>>, source: Pick<Transformable, TransformProp>): void;
export default Transformable;
