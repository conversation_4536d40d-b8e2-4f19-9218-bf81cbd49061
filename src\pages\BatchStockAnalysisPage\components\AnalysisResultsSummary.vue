<template>
  <div class="results-summary-container">
    <el-tabs type="border-card">
      <el-tab-pane label="总览">
        <StockResultsTable 
          :results="results" 
          :stockCodes="stockCodes"
        />
      </el-tab-pane>
      <el-tab-pane label="周期分析">
        <PeriodAnalysisTable 
          :results="results" 
          :periodRange="periodRange"
        />
      </el-tab-pane>
      <el-tab-pane label="最佳组合">
        <BestCombinationsTable 
          :results="results"
        />
      </el-tab-pane>
      <el-tab-pane label="最新交易信号">
        <LatestTradeSignalsTable 
          :results="results"
          :rawResults="rawResults"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import StockResultsTable from './StockResultsTable.vue'
import PeriodAnalysisTable from './PeriodAnalysisTable.vue'
import BestCombinationsTable from './BestCombinationsTable.vue'
import LatestTradeSignalsTable from './LatestTradeSignalsTable.vue'

const props = defineProps({
  // 分析结果数据
  results: {
    type: Array,
    default: () => []
  },
  // 股票代码列表
  stockCodes: {
    type: Array,
    default: () => []
  },
  // 周期范围
  periodRange: {
    type: Object,
    default: () => ({
      min: 2,
      max: 20,
      step: 2
    })
  },
  // 原始分析返回数据（包含daily_data）
  rawResults: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style scoped>
.results-summary-container {
  margin-bottom: 30px;
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
}

:deep(.el-tab-pane) {
  padding: 15px;
}
</style> 