{"version": 3, "file": "cache-options.js", "sources": ["../../../../../../packages/components/tree-select/src/cache-options.ts"], "sourcesContent": ["import { defineComponent, inject, watch } from 'vue'\nimport { selectKey } from '@element-plus/components/select'\nimport { isClient } from '@element-plus/utils'\nimport type { SelectContext } from '@element-plus/components/select'\nimport type { PropType } from 'vue'\n\n// same as el-option instance,\n// these are required for `cachedOptions`\nexport type CacheOption = {\n  value: string | number | boolean | object\n  currentLabel: string | number\n  isDisabled: boolean\n}\n\nexport default defineComponent({\n  props: {\n    data: {\n      type: Array as PropType<CacheOption[]>,\n      default: () => [],\n    },\n  },\n  setup(props) {\n    const select = inject(selectKey) as NonNullable<SelectContext>\n\n    watch(\n      () => props.data,\n      () => {\n        props.data.forEach((item) => {\n          if (!select.states.cachedOptions.has(item.value)) {\n            select.states.cachedOptions.set(item.value, item)\n          }\n        })\n\n        // fork from packages/select/src/useSelect.ts#330\n        const inputs = select.selectRef?.querySelectorAll('input') || []\n        if (\n          isClient &&\n          !Array.from(inputs).includes(\n            document.activeElement as HTMLInputElement\n          )\n        ) {\n          select.setSelected()\n        }\n      },\n      { flush: 'post', immediate: true }\n    )\n\n    return () => undefined\n  },\n})\n"], "names": ["defineComponent", "inject", "<PERSON><PERSON><PERSON>", "watch", "isClient"], "mappings": ";;;;;;;;AAGA,mBAAeA,mBAAe,CAAC;AAC/B,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,OAAO,EAAE,MAAM,EAAE;AACvB,KAAK;AACL,GAAG;AACH,EAAE,KAAK,CAAC,KAAK,EAAE;AACf,IAAI,MAAM,MAAM,GAAGC,UAAM,CAACC,eAAS,CAAC,CAAC;AACrC,IAAIC,SAAK,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,MAAM;AAClC,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACnC,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC1D,UAAU,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC5D,SAAS;AACT,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;AACrG,MAAM,IAAIC,aAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;AAC5E,QAAQ,MAAM,CAAC,WAAW,EAAE,CAAC;AAC7B,OAAO;AACP,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAC3C,IAAI,OAAO,MAAM,KAAK,CAAC,CAAC;AACxB,GAAG;AACH,CAAC,CAAC;;;;"}