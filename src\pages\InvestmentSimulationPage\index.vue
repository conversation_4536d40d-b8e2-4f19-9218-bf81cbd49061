<template>
  <div class="investment-simulation-container">
    <!-- 参数设置表单 -->
    <el-form :model="formData" :rules="rules" ref="formRef" label-width="120px" class="parameter-form">
      <el-form-item label="股票代码" prop="stockCode">
        <el-input v-model="formData.stockCode" placeholder="请输入股票代码，例如：000001"></el-input>
      </el-form-item>
      <el-form-item label="开始日期" prop="startDate">
        <el-date-picker
          v-model="formData.startDate"
          type="date"
          placeholder="开始日期"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="结束日期" prop="endDate">
        <el-date-picker
          v-model="formData.endDate"
          type="date"
          placeholder="结束日期"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="初始资金" prop="initialCapital">
        <el-input-number 
          v-model="formData.initialCapital" 
          :min="1000" 
          :step="1000"
          :precision="2"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="均线周期范围">
        <el-col :span="8">
          <el-form-item prop="minPeriod">
            <el-input-number 
              v-model="formData.minPeriod" 
              :min="2" 
              :max="formData.maxPeriod"
              label="最小周期"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="maxPeriod">
            <el-input-number 
              v-model="formData.maxPeriod" 
              :min="formData.minPeriod"
              :max="240"
              label="最大周期"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="step">
            <el-input-number 
              v-model="formData.step" 
              :min="1"
              :max="20"
              label="步长"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSimulate" :loading="loading">开始模拟</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 图表加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 均线显示设置 -->
    <div v-if="simulationResult && !loading" class="ma-settings-container">
      <div class="ma-settings-title">均线显示设置</div>
      <div class="ma-settings-options">
        <el-checkbox 
          v-for="period in availablePeriods" 
          :key="`ma-${period}`"
          v-model="maVisibility[period]"
          @change="() => handleMAVisibilityChange(period)"
          :style="{ color: getMALineColor(Number(period)) }"
        >
          MA{{ period }}
        </el-checkbox>
      </div>
      <div class="ma-settings-tips">
        提示：均线选择器将同步控制K线图和资产图的显示状态
      </div>
      <el-divider></el-divider>
      <div class="trading-rules">
        <div class="trading-rules-title">交易规则说明：</div>
        <div class="trading-rule-item">
          <span class="buy-rule">买入规则</span>：当天K线<strong>严格在均线下方</strong>（最高价低于均线），且收盘价高于开盘价（阳线），买入100股
        </div>
        <div class="trading-rule-item">
          <span class="sell-rule">卖出规则</span>：当天K线<strong>严格在均线上方</strong>（最低价高于均线），卖出100股
        </div>
        <div class="trading-rule-item note">
          注：虽然部分K线可能看起来高于/低于均线，但如果不满足"严格在均线上/下方"的条件（如穿过均线），则不会触发买卖信号
        </div>
      </div>
    </div>

    <!-- 图表展示区域 -->
    <div class="charts-container">
      <div v-if="simulationResult && !loading" class="chart-wrapper">
        <div ref="klineChart" class="chart" id="klineChart"></div>
      </div>
      <div v-if="simulationResult && !loading" class="chart-wrapper">
        <div ref="assetChart" class="chart" id="assetChart"></div>
      </div>
      <!-- 空占位符，确保即使没有数据也能初始化容器 -->
      <div v-if="!simulationResult || loading" class="chart-wrapper empty-chart">
        <div id="klineChart"></div>
      </div>
      <div v-if="!simulationResult || loading" class="chart-wrapper empty-chart">
        <div id="assetChart"></div>
      </div>
    </div>

    <!-- 统计结果表格 -->
    <div v-if="simulationResult && !loading" class="statistics-container">
      <div class="statistics-title">模拟交易结果统计</div>
      <el-table 
        :data="combinedStatsData" 
        border 
        style="width: 100%"
        :default-sort="{prop: 'profitRate', order: 'descending'}"
        max-height="600"
      >
        <el-table-column prop="period" label="均线周期" width="90" fixed="left" sortable>
          <template #default="scope">
            <span class="period-cell">{{ scope.row.period }}</span>
          </template>
        </el-table-column>
        
        <!-- 收益统计列 -->
        <el-table-column label="收益统计" align="center">
          <el-table-column prop="totalReturn" label="总收益" width="110" sortable>
            <template #default="scope">
              <span :class="getValueColorClass(scope.row.totalReturn)">{{ scope.row.totalReturn }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="profitRate" label="收益率(%)" width="100" sortable>
            <template #default="scope">
              <span :class="getValueColorClass(scope.row.profitRate)">{{ scope.row.profitRate }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="annualizedReturn" label="年化收益率(%)" width="120" sortable>
            <template #default="scope">
              <span :class="getValueColorClass(scope.row.annualizedReturn)">{{ scope.row.annualizedReturn }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="maxDrawdown" label="最大回撤(%)" width="110" sortable>
            <template #default="scope">
              <span class="negative-value">{{ scope.row.maxDrawdown }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        
        <!-- 交易统计列 -->
        <el-table-column label="交易统计" align="center">
          <el-table-column prop="transactionCount" label="交易次数" width="90" sortable />
          <el-table-column prop="maxConsecutiveBuy" label="最大连续买入" width="110" sortable />
          <el-table-column prop="maxConsecutiveSell" label="最大连续卖出" width="110" sortable />
          <el-table-column prop="avgHoldingDays" label="平均持仓天数" width="110" sortable />
          <el-table-column prop="maxHoldingDays" label="最长持仓天数" width="110" sortable />
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import { simulateInvestment, formatSimulationData, generateSimulationStatistics, getMALineColor } from '@/api/maAnalysis'

export default {
  name: 'InvestmentSimulationPage',
  setup() {
    // 图表实例
    const klineChart = ref(null)
    const assetChart = ref(null)
    let klineInstance = null
    let assetInstance = null

    // 格式化后的数据，用于后续更新图表
    let formattedData = null;

    // 表单数据
    const formData = reactive({
      stockCode: '000002',
      startDate: '2023-04-19',
      endDate: '2025-04-19',
      initialCapital: 100000,
      minPeriod: 2,
      maxPeriod: 10,
      step: 2
    })

    // 表单验证规则
    const rules = {
      stockCode: [
        { required: true, message: '请输入股票代码', trigger: 'blur' },
        { pattern: /^\d{6}$/, message: '请输入6位数字的股票代码', trigger: 'blur' }
      ],
      startDate: [
        { required: true, message: '请选择开始日期', trigger: 'change' }
      ],
      endDate: [
        { required: true, message: '请选择结束日期', trigger: 'change' }
      ]
    }

    const formRef = ref(null)
    const loading = ref(false)
    const simulationResult = ref(null)

    // 均线显示控制
    const availablePeriods = ref([]);
    const maVisibility = reactive({});
    const maData = ref({});
    
    // 更新均线可见性
    const updateMAVisibility = () => {
      if (!klineInstance) return;
      
      // 重新配置K线图的均线系列
      const option = klineInstance.getOption();
      
      // 获取当前的选中的周期
      const visiblePeriods = Object.keys(maVisibility).filter(period => maVisibility[period]);
      console.log('可见周期:', visiblePeriods);
      
      // 保存原始K线数据系列
      const klineSeries = option.series.find(s => s.name === 'K线');
      
      // 创建均线系列
      const maSeries = Object.entries(maData.value)
        .filter(([period]) => maVisibility[period])
        .map(([period, data]) => ({
          name: `MA${period}`,
          type: 'line',
          data: data,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            width: 2,
            color: getMALineColor(Number(period))
          },
          z: 1
        }));
        
      // 从formattedData获取买卖点数据
      const { buyPoints = {}, sellPoints = {} } = formattedData || {};
      
      // 创建买入点系列，只显示选中周期的买入点
      const buySeries = Object.entries(buyPoints)
        .filter(([period]) => visiblePeriods.includes(period))
        .map(([period, points]) => ({
          name: `MA${period}买入`,
          type: 'scatter',
          data: points.map(p => [p.date, p.price]),
          symbol: 'arrow',
          symbolSize: 10,
          symbolRotate: -90,
          itemStyle: {
            color: '#FF9800'
          },
          label: {
            show: true,
            position: 'top',
            formatter: 'B'
          }
        }));
        
      // 创建卖出点系列，只显示选中周期的卖出点
      const sellSeries = Object.entries(sellPoints)
        .filter(([period]) => visiblePeriods.includes(period))
        .map(([period, points]) => ({
          name: `MA${period}卖出`,
          type: 'scatter',
          data: points.map(p => [p.date, p.price]),
          symbol: 'arrow',
          symbolSize: 10,
          symbolRotate: 90,
          itemStyle: {
            color: '#2196F3'
          },
          label: {
            show: true,
            position: 'bottom',
            formatter: 'S'
          }
        }));
      
      // 组合所有系列
      const allSeries = [];
      if (klineSeries) {
        allSeries.push(klineSeries);
      }
      allSeries.push(...maSeries, ...buySeries, ...sellSeries);
      
      // 更新图例数据
      option.legend.data = [
        'K线', 
        ...visiblePeriods.map(period => `MA${period}`),
        ...buySeries.map(s => s.name),
        ...sellSeries.map(s => s.name)
      ];
      
      // 更新系列数据
      option.series = allSeries;
      
      // 应用更新
      klineInstance.setOption(option, true);
      console.log('K线图更新完成，显示周期:', visiblePeriods);
      
      // 同时更新资产图的显示/隐藏
      updateAssetVisibility();
    };
    
    // 处理MA可见性变更
    const handleMAVisibilityChange = (period) => {
      console.log(`MA${period} 可见性变更为: ${maVisibility[period]}`);
      updateMAVisibility();
    };
    
    // 更新资产图可见性
    const updateAssetVisibility = () => {
      if (!assetInstance || !formattedData) return;
      
      try {
        console.log('更新资产图可见性', Object.keys(maVisibility).map(p => `${p}: ${maVisibility[p]}`));
        
        // 获取当前的选中的周期
        const visiblePeriods = Object.keys(maVisibility).filter(period => maVisibility[period]);
        console.log('可见周期:', visiblePeriods);
        
        // 获取初始化图表时的原始数据
        const { assetData = {}, investmentData = {} } = formattedData || {};
        
        // 资产曲线
        const assetSeries = Object.entries(assetData)
          .filter(([period, data]) => data.length > 0 && visiblePeriods.includes(period))
          .map(([period, data]) => {
            // 新增：检查第一个周期的第一天资产数据
            if (data.length > 0) {
              console.log(`创建图表系列前 - MA${period} 第一天资产值:`, {
                date: data[0][0],
                originalValue: data[0][1],
                periodValue: period
              });
            }
            
            // 检查数据中是否存在null或undefined值，若存在则替换为0
            const cleanedData = data.map(item => {
              // 检查每一项并输出
              if (item[0] === data[0][0]) {
                console.log(`MA${period} 第一天资产数据清理前:`, item);
              }
              
              // 检查并确保值有效
              if (item[1] === null || item[1] === undefined || isNaN(item[1])) {
                return [item[0], 0];
              }
              return item;
            });
            
            // 检查清理后的第一天数据
            if (cleanedData.length > 0 && data.length > 0) {
              console.log(`MA${period} 第一天资产数据清理后:`, cleanedData[0]);
            }
            
            return {
              name: `MA${period}资产`,
              type: 'line',
              data: cleanedData,
              symbol: 'none',
              lineStyle: {
                width: 2,
                color: getMALineColor(parseInt(period))
              },
              emphasis: {
                lineStyle: {
                  width: 3
                }
              }
            };
          });
        
        // 投入曲线
        const investmentSeries = Object.entries(investmentData)
          .filter(([period, data]) => data.length > 0 && visiblePeriods.includes(period))
          .map(([period, data]) => ({
            name: `MA${period}投入`,
            type: 'line',
            data: data,
            symbol: 'none',
            lineStyle: {
              width: 1,
              type: 'dashed',
              color: getMALineColor(parseInt(period))
            }
          }));
        
        // 更新图表的系列数据
        const option = assetInstance.getOption();
        
        // 更新系列和图例
        option.series = [...assetSeries, ...investmentSeries];
        option.legend.data = option.series.map(s => s.name);
        
        // 应用更新
        assetInstance.setOption(option, true);
        console.log('资产图更新完成，显示周期:', visiblePeriods);
      } catch (error) {
        console.error('更新资产图可见性出错:', error);
      }
    };

    // 处理模拟请求
    const handleSimulate = async () => {
      if (!formRef.value) return
      
      try {
        await formRef.value.validate()
        loading.value = true
        
        // 清除现有图表
        if (klineInstance) {
          klineInstance.dispose()
          klineInstance = null
        }
        if (assetInstance) {
          assetInstance.dispose()
          assetInstance = null
        }
        
        const params = {
          stock_code: formData.stockCode,
          start_date: formData.startDate,
          end_date: formData.endDate,
          initial_capital: formData.initialCapital,
          min_period: formData.minPeriod,
          max_period: formData.maxPeriod,
          step: formData.step
        }

        console.log('发送请求参数:', params)
        try {
          const result = await simulateInvestment(params)
          console.log('接收到的数据:', result)
          
          // 添加日志：检查一下后端返回的period_results内容
          if (result && result.period_results && result.period_results.length > 0) {
            // 检查第一个周期的原始资产数据
            const firstPeriod = result.period_results[0];
            console.log('第一个周期数据:', {
              period: firstPeriod.period,
              totalReturn: firstPeriod.total_return,
              initialCapital: result.initial_capital
            });
            
            // 检查daily_assets第一天的值（如果存在）
            if (firstPeriod.daily_assets && firstPeriod.daily_assets.length > 0) {
              console.log('第一天原始资产数据:', {
                date: firstPeriod.daily_assets[0].trade_date,
                totalAssets: firstPeriod.daily_assets[0].total_assets,
                investmentAmount: firstPeriod.daily_assets[0].investment_amount
              });
            } else {
              console.log('找不到daily_assets或为空');
            }
          }
          
          simulationResult.value = result
          
          // 确保DOM已经更新
          await nextTick()
          console.log('开始渲染图表')
          renderCharts()
        } catch (apiError) {
          console.error('API请求错误:', apiError)
          ElMessage.error(`获取数据失败: ${apiError.message || '未知错误'}`)
        }
        
      } catch (error) {
        console.error('请求或渲染出错:', error)
        ElMessage.error(error.message || '请求失败')
      } finally {
        loading.value = false
      }
    }

    // 重置表单
    const resetForm = () => {
      if (formRef.value) {
        formRef.value.resetFields()
      }
      simulationResult.value = null
      if (klineInstance) klineInstance.dispose()
      if (assetInstance) assetInstance.dispose()
    }

    // 渲染图表
    const renderCharts = () => {
      if (!simulationResult.value) {
        console.warn('没有数据可供渲染')
        return
      }

      console.log('格式化数据开始')
      formattedData = formatSimulationData(simulationResult.value)
      console.log('格式化后的数据:', formattedData)
      const { dates, klineData, buyPoints, sellPoints, assetData, investmentData } = formattedData
      
      // 添加日志：检查格式化后第一个周期的asset和investment数据
      if (Object.keys(assetData).length > 0) {
        const firstPeriodKey = Object.keys(assetData)[0];
        console.log(`格式化后第一个周期(MA${firstPeriodKey})的资产数据:`, 
          assetData[firstPeriodKey].length > 0 ? assetData[firstPeriodKey][0] : '无数据');
        console.log(`格式化后第一个周期(MA${firstPeriodKey})的投入数据:`, 
          investmentData[firstPeriodKey].length > 0 ? investmentData[firstPeriodKey][0] : '无数据');
      }
      
      // 添加：提取均线数据
      maData.value = extractMAData(simulationResult.value, dates);
      
      // 更新可用均线周期
      const periods = Object.keys(maData.value).map(Number).sort((a, b) => a - b);
      availablePeriods.value = periods;
      
      // 初始化均线可见性（默认显示MA5、MA10、MA20、MA30）
      const defaultShowPeriods = [5, 10, 20, 30, 60];
      periods.forEach(period => {
        maVisibility[period] = defaultShowPeriods.includes(Number(period));
      });
      
      // 预处理买卖点数据，确保每个周期都有数据结构
      Object.keys(maVisibility).forEach(period => {
        if (!buyPoints[period]) {
          buyPoints[period] = [];
        }
        if (!sellPoints[period]) {
          sellPoints[period] = [];
        }
      });
      
      // 检查是否有有效数据
      if (dates.length === 0) {
        console.error('没有有效的日期数据可供展示')
        ElMessage.warning('无法渲染图表：没有有效的日期数据')
        return
      }
      
      // 确保在下一次DOM更新后再初始化图表
      nextTick(() => {
        // 检查图表容器
        if (!klineChart.value || !assetChart.value) {
          console.error('图表容器未找到，等待DOM更新')
          // 再次尝试，确保DOM已更新
          setTimeout(() => {
            initAndRenderCharts(dates, klineData, buyPoints, sellPoints, assetData, investmentData)
          }, 500)
          return
        }
        
        initAndRenderCharts(dates, klineData, buyPoints, sellPoints, assetData, investmentData)
      })
    }
    
    // 从模拟结果中提取均线数据
    const extractMAData = (simulationData, dates) => {
      const result = {};
      
      // 尝试从daily_data中提取均线数据
      const dailyData = simulationData.daily_data || simulationData.stock_data || [];
      
      if (dailyData.length > 0 && dailyData[0].ma_values) {
        // 找出所有可用的均线周期
        const periods = new Set();
        dailyData.forEach(day => {
          if (day.ma_values) {
            Object.keys(day.ma_values).forEach(p => periods.add(p));
          }
        });
        
        // 为每个周期创建均线数据
        periods.forEach(period => {
          result[period] = dailyData
            .filter(day => day.ma_values && day.ma_values[period] !== undefined)
            .map(day => [day.trade_date, day.ma_values[period]]);
        });
      } else {
        // 期间结果中查找均线数据
        const periodResults = simulationData.period_results || [];
        
        periodResults.forEach(periodResult => {
          const period = periodResult.period;
          
          // 如果有均线数据
          if (periodResult.ma_values && Array.isArray(periodResult.ma_values)) {
            result[period] = periodResult.ma_values.map(item => 
              Array.isArray(item) ? item : [item.trade_date, item.ma]
            );
          }
        });
        
        // 如果没有找到均线数据，但有日线数据，尝试计算简单均线
        if (Object.keys(result).length === 0 && dailyData.length > 0) {
          const basicPeriods = [5, 10, 20, 30, 60];
          basicPeriods.forEach(period => {
            if (dailyData.length >= period) {
              // 简单计算移动平均
              const maValues = [];
              for (let i = 0; i < dailyData.length; i++) {
                if (i >= period - 1) {
                  let sum = 0;
                  for (let j = 0; j < period; j++) {
                    sum += parseFloat(dailyData[i - j].close || 0);
                  }
                  const ma = sum / period;
                  maValues.push([dailyData[i].trade_date, parseFloat(ma.toFixed(2))]);
                } else {
                  // 数据不足时用null填充
                  maValues.push([dailyData[i].trade_date, null]);
                }
              }
              result[period] = maValues;
            }
          });
        }
      }
      
      return result;
    };
    
    // 初始化并渲染图表
    const initAndRenderCharts = (dates, klineData, buyPoints, sellPoints, assetData, investmentData) => {
      // 检查图表容器
      let klineElement = klineChart.value;
      let assetElement = assetChart.value;
      
      // 如果通过ref获取不到，尝试通过ID获取
      if (!klineElement) {
        klineElement = document.getElementById('klineChart');
        console.log('通过ID获取K线图容器:', klineElement);
      }
      
      if (!assetElement) {
        assetElement = document.getElementById('assetChart');
        console.log('通过ID获取资产图容器:', assetElement);
      }
      
      if (!klineElement || !assetElement) {
        console.error('图表容器仍未找到，放弃渲染');
        ElMessage.error('图表容器未找到，请尝试刷新页面');
        return;
      }
      
      // 销毁现有实例（如果有）
      if (klineInstance) {
        klineInstance.dispose();
        klineInstance = null;
      }
      if (assetInstance) {
        assetInstance.dispose();
        assetInstance = null;
      }

      // 初始化图表实例
      console.log('初始化K线图实例');
      klineInstance = echarts.init(klineElement);
      console.log('初始化资产图实例');
      assetInstance = echarts.init(assetElement);
      
      // 注册图例切换事件监听
      assetInstance.on('legendselectchanged', function(params) {
        const { name } = params;
        // 如果是资产或投入曲线被点击
        if (name.includes('MA') && (name.includes('资产') || name.includes('投入'))) {
          // 提取MA周期
          const period = name.replace('MA', '').replace('资产', '').replace('投入', '');
          // 切换均线选择状态
          maVisibility[period] = !maVisibility[period];
          // 更新图表
          updateMAVisibility();
        }
      });
      
      // 为K线图添加图例事件监听
      klineInstance.on('legendselectchanged', function(params) {
        const { name } = params;
        // 如果是均线被点击
        if (name.includes('MA') && !name.includes('买入') && !name.includes('卖出')) {
          // 提取MA周期
          const period = name.replace('MA', '');
          // 切换均线选择状态
          maVisibility[period] = !maVisibility[period];
          // 更新图表
          updateMAVisibility();
        }
        // 如果是买入点或卖出点被点击，不做特殊处理，交由ECharts默认处理
      });

      // 配置K线图选项
      const klineOption = {
        animation: false,
        title: {
          text: '股票K线图与买卖点',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function (params) {
            const date = params[0].axisValue;
            let html = `${date}<br/>`;
            
            // 检查是否有K线数据
            const klineParam = params.find(p => p.seriesName === 'K线')
            if (klineParam && klineParam.data && klineParam.data.length >= 4) {
              html += `开盘：${klineParam.data[1]}<br/>`;
              html += `收盘：${klineParam.data[2]}<br/>`;
              html += `最低：${klineParam.data[3]}<br/>`;
              html += `最高：${klineParam.data[4]}<br/>`;
            }
            
            // 添加均线数据
            params.forEach(param => {
              if (param.seriesName.startsWith('MA') && !param.seriesName.includes('买入') && !param.seriesName.includes('卖出') && !param.seriesName.includes('资产') && !param.seriesName.includes('投入')) {
                html += `${param.seriesName}：${param.value[1]}<br/>`;
              }
            });
            
            // 买卖点数据
            params.forEach(param => {
              if (param.seriesName.includes('买入') || param.seriesName.includes('卖出')) {
                html += `${param.seriesName}：${param.data[1]}<br/>`;
              }
            });
            
            return html;
          }
        },
        legend: {
          data: ['K线', ...Object.keys(buyPoints).map(p => `MA${p}买入`), ...Object.keys(sellPoints).map(p => `MA${p}卖出`)],
          bottom: 10,
          selected: {
            'K线': klineData.some(data => data.some(val => val > 0)) // 只有当K线数据有效时才默认显示
          }
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%'
        },
        xAxis: {
          type: 'category',
          data: dates,
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          splitLine: { show: false },
          min: 'dataMin',
          max: 'dataMax'
        },
        yAxis: {
          scale: true,
          splitArea: {
            show: true
          }
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            show: true,
            type: 'slider',
            bottom: 60,
            start: 0,
            end: 100
          }
        ],
        series: [
          // 只有当K线数据有效时才添加K线图
          ...(klineData.some(data => data.some(val => val > 0)) ? [{
            name: 'K线',
            type: 'candlestick',
            data: klineData,
            itemStyle: {
              color: '#ef232a',
              color0: '#14b143',
              borderColor: '#ef232a',
              borderColor0: '#14b143'
            }
          }] : []),
          // 添加均线
          ...Object.entries(maData.value)
            .filter(([period]) => maVisibility[period])
            .map(([period, data]) => ({
              name: `MA${period}`,
              type: 'line',
              data: data,
              smooth: true,
              symbol: 'none',
              lineStyle: {
                width: 2,
                color: getMALineColor(Number(period))
              },
              z: 1
            })),
          // 添加买入点标记
          ...Object.entries(buyPoints).map(([period, points]) => ({
            name: `MA${period}买入`,
            type: 'scatter',
            data: points.map(p => [p.date, p.price]),
            symbol: 'arrow',
            symbolSize: 10,
            symbolRotate: -90,
            itemStyle: {
              color: '#FF9800'
            },
            label: {
              show: true,
              position: 'top',
              formatter: 'B'
            }
          })),
          // 添加卖出点标记
          ...Object.entries(sellPoints).map(([period, points]) => ({
            name: `MA${period}卖出`,
            type: 'scatter',
            data: points.map(p => [p.date, p.price]),
            symbol: 'arrow',
            symbolSize: 10,
            symbolRotate: 90,
            itemStyle: {
              color: '#2196F3'
            },
            label: {
              show: true,
              position: 'bottom',
              formatter: 'S'
            }
          }))
        ]
      }

      // 配置资产曲线图选项
      const assetOption = {
        animation: false,
        title: {
          text: '资产与投入曲线',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 添加日志：检查tooltip formatter中的params
            console.log('资产图tooltip数据:', JSON.stringify(params.map(p => ({
              seriesName: p.seriesName,
              dataValue: p.value
            }))));
            
            const date = params[0].axisValue;
            let html = `${date}<br/>`;
            params.forEach(param => {
              if (param.data && param.data.length > 1) {
                const value = parseFloat(param.data[1]).toFixed(2);
                html += `${param.seriesName}：${value}<br/>`;
              }
            });
            return html;
          }
        },
        legend: {
          data: [],  // 将在下面动态添加
          bottom: 10,
          selected: {}
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%'
        },
        xAxis: {
          type: 'category',
          data: dates,
          boundaryGap: false,
          axisLine: { onZero: false },
          splitLine: { show: false },
          min: 'dataMin',
          max: 'dataMax'
        },
        yAxis: {
          type: 'value',
          scale: true,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            show: true,
            type: 'slider',
            bottom: 60,
            start: 0,
            end: 100
          }
        ],
        series: []  // 将在下面动态添加
      }
      
      // 只添加被选中的周期的资产和投入曲线
      const selectedPeriods = Object.keys(maVisibility).filter(period => maVisibility[period]);
      
      // 资产曲线
      const assetSeries = Object.entries(assetData)
        .filter(([period, data]) => data.length > 0 && selectedPeriods.includes(period))
        .map(([period, data]) => {
          // 新增：检查第一个周期的第一天资产数据
          if (data.length > 0) {
            console.log(`创建图表系列前 - MA${period} 第一天资产值:`, {
              date: data[0][0],
              originalValue: data[0][1],
              periodValue: period
            });
          }
          
          // 检查数据中是否存在null或undefined值，若存在则替换为0
          const cleanedData = data.map(item => {
            // 检查每一项并输出
            if (item[0] === data[0][0]) {
              console.log(`MA${period} 第一天资产数据清理前:`, item);
            }
            
            // 检查并确保值有效
            if (item[1] === null || item[1] === undefined || isNaN(item[1])) {
              return [item[0], 0];
            }
            return item;
          });
          
          // 检查清理后的第一天数据
          if (cleanedData.length > 0 && data.length > 0) {
            console.log(`MA${period} 第一天资产数据清理后:`, cleanedData[0]);
          }
          
          return {
            name: `MA${period}资产`,
            type: 'line',
            data: cleanedData,
            symbol: 'none',
            lineStyle: {
              width: 2,
              color: getMALineColor(parseInt(period))
            },
            emphasis: {
              lineStyle: {
                width: 3
              }
            }
          };
        });
      
      // 投入曲线
      const investmentSeries = Object.entries(investmentData)
        .filter(([period, data]) => data.length > 0 && selectedPeriods.includes(period))
        .map(([period, data]) => ({
          name: `MA${period}投入`,
          type: 'line',
          data: data,
          symbol: 'none',
          lineStyle: {
            width: 1,
            type: 'dashed',
            color: getMALineColor(parseInt(period))
          }
        }));
      
      // 添加系列
      assetOption.series = [...assetSeries, ...investmentSeries];
      
      // 更新图例数据
      assetOption.legend.data = assetOption.series.map(s => s.name);
      
      try {
        console.log('设置K线图配置')
        klineInstance.setOption(klineOption, true)
        console.log('设置资产图配置')
        assetInstance.setOption(assetOption, true)

        // 处理图表联动
        klineInstance.group = 'stock'
        assetInstance.group = 'stock'
        echarts.connect('stock')
        
        // 添加调整大小事件
        window.dispatchEvent(new Event('resize'))
      } catch (error) {
        console.error('图表渲染错误:', error)
        ElMessage.error(`图表渲染失败: ${error.message || '未知错误'}`)
      }
    }

    // 计算属性：收益统计表格数据
    const profitTableData = computed(() => {
      if (!simulationResult.value) return []
      const { profitStats } = generateSimulationStatistics(simulationResult.value)
      return profitStats
    })

    // 计算属性：交易统计表格数据
    const tradeTableData = computed(() => {
      if (!simulationResult.value) return []
      const { tradeStats } = generateSimulationStatistics(simulationResult.value)
      return tradeStats
    })
    
    // 计算属性：合并后的统计表格数据
    const combinedStatsData = computed(() => {
      if (!simulationResult.value) return [];
      
      const { profitStats, tradeStats } = generateSimulationStatistics(simulationResult.value);
      
      // 建立周期到交易统计的映射
      const tradeStatsMap = {};
      tradeStats.forEach(item => {
        tradeStatsMap[item.period] = item;
      });
      
      // 合并数据
      return profitStats.map(profitItem => {
        const tradeItem = tradeStatsMap[profitItem.period] || {};
        return {
          period: profitItem.period,
          // 收益统计
          totalReturn: profitItem.totalReturn,
          profitRate: profitItem.profitRate,
          annualizedReturn: profitItem.annualizedReturn,
          maxDrawdown: profitItem.maxDrawdown,
          transactionCount: profitItem.transactionCount,
          // 交易统计
          maxConsecutiveBuy: tradeItem.maxConsecutiveBuy || '-',
          maxConsecutiveSell: tradeItem.maxConsecutiveSell || '-',
          avgHoldingDays: tradeItem.avgHoldingDays || '-',
          maxHoldingDays: tradeItem.maxHoldingDays || '-'
        };
      });
    });
    
    // 根据数值获取样式类
    const getValueColorClass = (value) => {
      if (!value || value === '-') return '';
      
      const numValue = parseFloat(value);
      if (isNaN(numValue)) return '';
      
      if (numValue > 0) return 'positive-value';
      if (numValue < 0) return 'negative-value';
      return '';
    };

    // 图表大小调整处理函数
    const handleResize = () => {
      if (klineInstance) klineInstance.resize();
      if (assetInstance) assetInstance.resize();
    };

    // 在组件挂载时自动触发一次模拟
    onMounted(async () => {
      // 添加窗口大小变化监听
      window.addEventListener('resize', handleResize);
      
      // 延迟执行，确保DOM已经渲染
      setTimeout(async () => {
        try {
          await handleSimulate();
        } catch (error) {
          console.error('自动模拟失败:', error);
        }
      }, 1000); // 增加延迟时间，确保DOM完全渲染
    });

    // 组件卸载时清理事件监听
    onUnmounted(() => {
      // 移除窗口大小变化监听器
      window.removeEventListener('resize', handleResize);
      
      // 移除图表事件监听器并销毁图表实例
      if (klineInstance) {
        klineInstance.off('legendselectchanged');
        klineInstance.dispose();
      }
      
      if (assetInstance) {
        assetInstance.off('legendselectchanged');
        assetInstance.dispose();
      }
      
      // 断开图表联动
      echarts.disconnect('stock');
      
      // 清空实例引用
      klineInstance = null;
      assetInstance = null;
    })

    return {
      formRef,
      formData,
      rules,
      loading,
      klineChart,
      assetChart,
      simulationResult,
      availablePeriods,
      maVisibility,
      updateMAVisibility,
      handleMAVisibilityChange,
      getMALineColor,
      combinedStatsData,
      getValueColorClass,
      handleSimulate,
      resetForm
    }
  }
}
</script>

<style scoped>
.investment-simulation-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.parameter-form {
  max-width: 800px;
  margin-bottom: 20px;
}

.ma-settings-container {
  background: #fff;
  border-radius: 4px;
  padding: 15px 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.ma-settings-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.ma-settings-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 8px;
}

.ma-settings-options :deep(.el-checkbox) {
  margin-right: 0;
}

.ma-settings-options :deep(.el-checkbox__label) {
  font-weight: bold;
}

.ma-settings-tips {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.trading-rules {
  margin-top: 10px;
}

.trading-rules-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.trading-rule-item {
  font-size: 13px;
  margin-bottom: 6px;
  line-height: 1.5;
}

.buy-rule {
  color: #f56c6c;
  font-weight: bold;
}

.sell-rule {
  color: #67c23a;
  font-weight: bold;
}

.note {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
  font-style: italic;
}

.charts-container {
  flex: 1;
  min-height: 800px;
  margin: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-wrapper {
  flex: 1;
  min-height: 400px;
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.chart {
  width: 100%;
  height: 100%;
  min-height: 360px;
}

.empty-chart {
  flex: 1;
  min-height: 400px;
  background: #f5f7fa;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.empty-chart div {
  width: 100%;
  height: 100%;
  min-height: 360px;
}

.statistics-container {
  margin-top: 20px;
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.statistics-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.period-cell {
  font-weight: bold;
}

.positive-value {
  color: #67c23a;
  font-weight: bold;
}

.negative-value {
  color: #f56c6c;
  font-weight: bold;
}

:deep(.el-table__header) th {
  background-color: #f5f7fa;
  font-weight: bold;
}

:deep(.el-table__row:hover) td {
  background-color: #ecf5ff !important;
}

:deep(.el-table .el-table__cell.is-leaf) {
  text-align: center;
}

.loading-container {
  padding: 40px;
  background: #fff;
  border-radius: 4px;
  margin: 20px 0;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}
</style> 