<template>
  <div class="latest-signals-table">
    <div class="table-header">
      <h3>最优周期下最新交易信号</h3>
      <div class="table-actions">
        <el-select v-model="filterOption" placeholder="筛选" style="width: 150px">
          <el-option label="全部股票" value="all" />
          <el-option label="仅买入信号" value="buy" />
          <el-option label="仅卖出信号" value="sell" />
          <el-option label="仅观望" value="hold" />
        </el-select>
        <el-button type="primary" plain size="small" @click="handleExport">
          导出数据
        </el-button>
        <el-button type="success" plain size="small" @click="copyStockCodes">
          <el-icon><CopyDocument /></el-icon>
          复制股票代码
        </el-button>
      </div>
    </div>

    <!-- 最新交易信号表格 -->
    <el-table
      :data="filteredSignals"
      border
      style="width: 100%"
      height="500"
      :default-sort="{ prop: 'signal', order: 'descending' }"
    >
      <el-table-column label="股票信息" width="150" fixed="left">
        <template #default="scope">
          <div class="stock-info">
            <span class="stock-code">{{ scope.row.stockCode }}</span>
            <span class="stock-name">{{ scope.row.stockName }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="bestPeriod" label="最优周期" width="100">
        <template #default="scope">
          <el-tag size="small">MA{{ scope.row.bestPeriod }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="lastTradeDate" label="最后交易日" width="120" />

      <el-table-column prop="signal" label="信号" width="100" sortable>
        <template #default="scope">
          <el-tag
            :type="scope.row.signal === '买入' ? 'success' : scope.row.signal === '卖出' ? 'danger' : 'info'"
            effect="dark"
          >
            {{ scope.row.signal }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="price" label="价格" width="100">
        <template #default="scope">
          {{ formatNumber(scope.row.price) }}
        </template>
      </el-table-column>

      <el-table-column prop="maValue" label="均线值" width="100">
        <template #default="scope">
          {{ formatNumber(scope.row.maValue) }}
        </template>
      </el-table-column>

      <el-table-column prop="pricePosition" label="价格相对均线" width="120">
        <template #default="scope">
          <span :class="getPricePositionClass(scope.row.pricePosition)">
            {{ formatNumber(scope.row.pricePosition) }}%
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="signalReason" label="信号触发原因" min-width="200" show-overflow-tooltip />

      <el-table-column prop="bestProfitRate" label="历史最佳收益率" width="140" sortable>
        <template #default="scope">
          <span :class="getValueClass(scope.row.bestProfitRate)">
            {{ formatNumber(scope.row.bestProfitRate) }}%
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { CopyDocument } from '@element-plus/icons-vue'

const props = defineProps({
  // 分析结果数据
  results: {
    type: Array,
    default: () => []
  },
  // 原始分析返回数据（包含daily_data）
  rawResults: {
    type: Object,
    default: () => ({})
  }
})

// 筛选选项
const filterOption = ref('all')

// 判断最后交易日的信号
const signalsList = computed(() => {
  if (!props.results.length || !props.rawResults) return []

  // 提取每只股票的最佳周期
  const stockBestPeriods = {}

  // 首先找出每只股票的最佳周期
  props.results.forEach(result => {
    const stockCode = result.stock_code

    if (!stockBestPeriods[stockCode] ||
        result.profit_rate > stockBestPeriods[stockCode].profit_rate) {
      stockBestPeriods[stockCode] = {
        period: result.period,
        profit_rate: result.profit_rate,
        stock_name: result.stock_name
      }
    }
  })

  // 分析每只股票在最佳周期下的最后交易日情况
  const signals = []

  // 遍历每只股票
  Object.entries(stockBestPeriods).forEach(([stockCode, bestPeriodInfo]) => {
    const bestPeriod = bestPeriodInfo.period

    // 尝试从原始数据中获取该股票的数据
    const stockData = props.rawResults[stockCode]

    if (!stockData || !stockData.daily_data || stockData.daily_data.length === 0) {
      console.warn(`没有找到股票 ${stockCode} 的daily_data数据`)
      return
    }

    // 获取最后一个交易日的数据
    const lastDayData = stockData.daily_data[stockData.daily_data.length - 1]

    // 获取最后一个交易日的MA值
    const maValue = lastDayData.ma_values ?
      lastDayData.ma_values[bestPeriod] : null

    if (!maValue) {
      console.warn(`无法获取股票 ${stockCode} 周期 ${bestPeriod} 的均线值`)
      return
    }

    // 判断信号
    let signal = '观望'
    let signalReason = '无明确买卖信号'

    // 获取价格数据
    const open = lastDayData.open
    const close = lastDayData.close
    const high = lastDayData.high
    const low = lastDayData.low

    // 计算价格相对均线的位置(%)
    const pricePosition = ((close - maValue) / maValue) * 100

    // 判断买入条件：K线严格在均线下方（最高价低于均线），且收盘价高于开盘价（阳线）
    if (high < maValue && close > open) {
      signal = '买入'
      signalReason = '最高价低于均线(MA严格在上方)，且为阳线(收盘价高于开盘价)'
    }
    // 判断卖出条件：K线严格在均线上方（最低价高于均线）
    else if (low > maValue) {
      signal = '卖出'
      signalReason = '最低价高于均线(MA严格在下方)'
    }
    // 其他情况
    else {
      if (close > maValue && low < maValue) {
        signalReason = '价格穿过均线，收盘在均线上方'
      } else if (close < maValue && high > maValue) {
        signalReason = '价格穿过均线，收盘在均线下方'
      } else if (close > maValue) {
        signalReason = '价格在均线上方，但未满足卖出条件'
      } else {
        signalReason = '价格在均线下方，但未满足买入条件'
      }
    }

    signals.push({
      stockCode,
      stockName: bestPeriodInfo.stock_name || '未知',
      bestPeriod,
      lastTradeDate: lastDayData.trade_date,
      signal,
      price: close,
      maValue,
      pricePosition,
      signalReason,
      bestProfitRate: bestPeriodInfo.profit_rate
    })
  })

  return signals
})

// 根据筛选条件过滤信号
const filteredSignals = computed(() => {
  if (filterOption.value === 'all') {
    return signalsList.value
  } else if (filterOption.value === 'buy') {
    return signalsList.value.filter(item => item.signal === '买入')
  } else if (filterOption.value === 'sell') {
    return signalsList.value.filter(item => item.signal === '卖出')
  } else {
    return signalsList.value.filter(item => item.signal === '观望')
  }
})

// 格式化数字
const formatNumber = (value) => {
  if (value === undefined || value === null) return '-'
  return parseFloat(value).toFixed(2)
}

// 获取数值显示的CSS类
const getValueClass = (value) => {
  if (value === undefined || value === null) return ''
  return value > 0 ? 'positive-value' : value < 0 ? 'negative-value' : ''
}

// 获取价格相对均线位置的CSS类
const getPricePositionClass = (value) => {
  if (value === undefined || value === null) return ''

  if (value > 5) return 'very-positive'
  if (value > 0) return 'positive-value'
  if (value < -5) return 'very-negative'
  return 'negative-value'
}

// 复制所有股票代码到剪贴板
const copyStockCodes = () => {
  try {
    // 获取当前筛选后的股票代码列表
    const stockCodes = filteredSignals.value.map(signal => signal.stockCode).join('\n')

    if (!stockCodes) {
      ElMessage.warning('没有可复制的股票代码')
      return
    }

    // 使用 Clipboard API 复制到剪贴板
    navigator.clipboard.writeText(stockCodes)
      .then(() => {
        ElMessage.success(`已复制 ${filteredSignals.value.length} 只股票代码到剪贴板`)
      })
      .catch(err => {
        console.error('复制失败:', err)
        ElMessage.error('复制失败: ' + err.message)
        // 备用方法
        fallbackCopyTextToClipboard(stockCodes)
      })
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败: ' + error.message)
  }
}

// 备用复制方法（兼容性更好）
const fallbackCopyTextToClipboard = (text) => {
  try {
    const textArea = document.createElement('textarea')
    textArea.value = text

    // 设置样式使其不可见
    textArea.style.position = 'fixed'
    textArea.style.top = '0'
    textArea.style.left = '0'
    textArea.style.width = '2em'
    textArea.style.height = '2em'
    textArea.style.padding = '0'
    textArea.style.border = 'none'
    textArea.style.outline = 'none'
    textArea.style.boxShadow = 'none'
    textArea.style.background = 'transparent'

    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)

    if (successful) {
      ElMessage.success(`已复制 ${filteredSignals.value.length} 只股票代码到剪贴板`)
    } else {
      ElMessage.error('复制失败，请手动复制')
    }
  } catch (err) {
    ElMessage.error('复制失败: ' + err.message)
  }
}

// 导出数据
const handleExport = () => {
  try {
    // 创建CSV内容
    let csvContent = "股票代码,股票名称,最优周期,最后交易日,信号,价格,均线值,价格相对均线(%),信号触发原因,历史最佳收益率(%)\n"

    // 添加每行数据
    signalsList.value.forEach(signal => {
      csvContent += `${signal.stockCode},${signal.stockName},MA${signal.bestPeriod},${signal.lastTradeDate},${signal.signal},${formatNumber(signal.price)},${formatNumber(signal.maValue)},${formatNumber(signal.pricePosition)},${signal.signalReason},${formatNumber(signal.bestProfitRate)}\n`
    })

    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })

    // 创建下载链接
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `最新交易信号_${new Date().toISOString().split('T')[0]}.csv`)

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + error.message)
  }
}
</script>

<style scoped>
.latest-signals-table {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.el-icon {
  margin-right: 4px;
}

.stock-info {
  display: flex;
  flex-direction: column;
}

.stock-code {
  font-weight: bold;
}

.stock-name {
  font-size: 0.8em;
  color: #888;
  margin-top: 3px;
}

.positive-value {
  color: #f56c6c;
  font-weight: bold;
}

.negative-value {
  color: #67c23a;
  font-weight: bold;
}

.very-positive {
  color: #ff4949;
  font-weight: bold;
}

.very-negative {
  color: #13ce66;
  font-weight: bold;
}
</style>