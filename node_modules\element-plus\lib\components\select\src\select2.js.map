{"version": 3, "file": "select2.js", "sources": ["../../../../../../packages/components/select/src/select.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"selectRef\"\n    v-click-outside:[popperRef]=\"handleClickOutside\"\n    :class=\"[nsSelect.b(), nsSelect.m(selectSize)]\"\n    @[mouseEnterEventName]=\"states.inputHovering = true\"\n    @mouseleave=\"states.inputHovering = false\"\n  >\n    <el-tooltip\n      ref=\"tooltipRef\"\n      :visible=\"dropdownMenuVisible\"\n      :placement=\"placement\"\n      :teleported=\"teleported\"\n      :popper-class=\"[nsSelect.e('popper'), popperClass]\"\n      :popper-options=\"popperOptions\"\n      :fallback-placements=\"fallbackPlacements\"\n      :effect=\"effect\"\n      pure\n      trigger=\"click\"\n      :transition=\"`${nsSelect.namespace.value}-zoom-in-top`\"\n      :stop-popper-mouse-event=\"false\"\n      :gpu-acceleration=\"false\"\n      :persistent=\"persistent\"\n      :append-to=\"appendTo\"\n      :show-arrow=\"showArrow\"\n      :offset=\"offset\"\n      @before-show=\"handleMenuEnter\"\n      @hide=\"states.isBeforeHide = false\"\n    >\n      <template #default>\n        <div\n          ref=\"wrapperRef\"\n          :class=\"[\n            nsSelect.e('wrapper'),\n            nsSelect.is('focused', isFocused),\n            nsSelect.is('hovering', states.inputHovering),\n            nsSelect.is('filterable', filterable),\n            nsSelect.is('disabled', selectDisabled),\n          ]\"\n          @click.prevent=\"toggleMenu\"\n        >\n          <div\n            v-if=\"$slots.prefix\"\n            ref=\"prefixRef\"\n            :class=\"nsSelect.e('prefix')\"\n          >\n            <slot name=\"prefix\" />\n          </div>\n          <div\n            ref=\"selectionRef\"\n            :class=\"[\n              nsSelect.e('selection'),\n              nsSelect.is(\n                'near',\n                multiple && !$slots.prefix && !!states.selected.length\n              ),\n            ]\"\n          >\n            <slot v-if=\"multiple\" name=\"tag\">\n              <div\n                v-for=\"item in showTagList\"\n                :key=\"getValueKey(item)\"\n                :class=\"nsSelect.e('selected-item')\"\n              >\n                <el-tag\n                  :closable=\"!selectDisabled && !item.isDisabled\"\n                  :size=\"collapseTagSize\"\n                  :type=\"tagType\"\n                  :effect=\"tagEffect\"\n                  disable-transitions\n                  :style=\"tagStyle\"\n                  @close=\"deleteTag($event, item)\"\n                >\n                  <span :class=\"nsSelect.e('tags-text')\">\n                    <slot\n                      name=\"label\"\n                      :label=\"item.currentLabel\"\n                      :value=\"item.value\"\n                    >\n                      {{ item.currentLabel }}\n                    </slot>\n                  </span>\n                </el-tag>\n              </div>\n\n              <el-tooltip\n                v-if=\"collapseTags && states.selected.length > maxCollapseTags\"\n                ref=\"tagTooltipRef\"\n                :disabled=\"dropdownMenuVisible || !collapseTagsTooltip\"\n                :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n                :effect=\"effect\"\n                placement=\"bottom\"\n                :teleported=\"teleported\"\n              >\n                <template #default>\n                  <div\n                    ref=\"collapseItemRef\"\n                    :class=\"nsSelect.e('selected-item')\"\n                  >\n                    <el-tag\n                      :closable=\"false\"\n                      :size=\"collapseTagSize\"\n                      :type=\"tagType\"\n                      :effect=\"tagEffect\"\n                      disable-transitions\n                      :style=\"collapseTagStyle\"\n                    >\n                      <span :class=\"nsSelect.e('tags-text')\">\n                        + {{ states.selected.length - maxCollapseTags }}\n                      </span>\n                    </el-tag>\n                  </div>\n                </template>\n                <template #content>\n                  <div ref=\"tagMenuRef\" :class=\"nsSelect.e('selection')\">\n                    <div\n                      v-for=\"item in collapseTagList\"\n                      :key=\"getValueKey(item)\"\n                      :class=\"nsSelect.e('selected-item')\"\n                    >\n                      <el-tag\n                        class=\"in-tooltip\"\n                        :closable=\"!selectDisabled && !item.isDisabled\"\n                        :size=\"collapseTagSize\"\n                        :type=\"tagType\"\n                        :effect=\"tagEffect\"\n                        disable-transitions\n                        @close=\"deleteTag($event, item)\"\n                      >\n                        <span :class=\"nsSelect.e('tags-text')\">\n                          <slot\n                            name=\"label\"\n                            :label=\"item.currentLabel\"\n                            :value=\"item.value\"\n                          >\n                            {{ item.currentLabel }}\n                          </slot>\n                        </span>\n                      </el-tag>\n                    </div>\n                  </div>\n                </template>\n              </el-tooltip>\n            </slot>\n            <div\n              :class=\"[\n                nsSelect.e('selected-item'),\n                nsSelect.e('input-wrapper'),\n                nsSelect.is('hidden', !filterable),\n              ]\"\n            >\n              <input\n                :id=\"inputId\"\n                ref=\"inputRef\"\n                v-model=\"states.inputValue\"\n                type=\"text\"\n                :name=\"name\"\n                :class=\"[nsSelect.e('input'), nsSelect.is(selectSize)]\"\n                :disabled=\"selectDisabled\"\n                :autocomplete=\"autocomplete\"\n                :style=\"inputStyle\"\n                :tabindex=\"tabindex\"\n                role=\"combobox\"\n                :readonly=\"!filterable\"\n                spellcheck=\"false\"\n                :aria-activedescendant=\"hoverOption?.id || ''\"\n                :aria-controls=\"contentId\"\n                :aria-expanded=\"dropdownMenuVisible\"\n                :aria-label=\"ariaLabel\"\n                aria-autocomplete=\"none\"\n                aria-haspopup=\"listbox\"\n                @keydown.down.stop.prevent=\"navigateOptions('next')\"\n                @keydown.up.stop.prevent=\"navigateOptions('prev')\"\n                @keydown.esc.stop.prevent=\"handleEsc\"\n                @keydown.enter.stop.prevent=\"selectOption\"\n                @keydown.delete.stop=\"deletePrevTag\"\n                @compositionstart=\"handleCompositionStart\"\n                @compositionupdate=\"handleCompositionUpdate\"\n                @compositionend=\"handleCompositionEnd\"\n                @input=\"onInput\"\n                @click.stop=\"toggleMenu\"\n              />\n              <span\n                v-if=\"filterable\"\n                ref=\"calculatorRef\"\n                aria-hidden=\"true\"\n                :class=\"nsSelect.e('input-calculator')\"\n                v-text=\"states.inputValue\"\n              />\n            </div>\n            <div\n              v-if=\"shouldShowPlaceholder\"\n              :class=\"[\n                nsSelect.e('selected-item'),\n                nsSelect.e('placeholder'),\n                nsSelect.is(\n                  'transparent',\n                  !hasModelValue || (expanded && !states.inputValue)\n                ),\n              ]\"\n            >\n              <slot\n                v-if=\"hasModelValue\"\n                name=\"label\"\n                :label=\"currentPlaceholder\"\n                :value=\"modelValue\"\n              >\n                <span>{{ currentPlaceholder }}</span>\n              </slot>\n              <span v-else>{{ currentPlaceholder }}</span>\n            </div>\n          </div>\n          <div ref=\"suffixRef\" :class=\"nsSelect.e('suffix')\">\n            <el-icon\n              v-if=\"iconComponent && !showClose\"\n              :class=\"[nsSelect.e('caret'), nsSelect.e('icon'), iconReverse]\"\n            >\n              <component :is=\"iconComponent\" />\n            </el-icon>\n            <el-icon\n              v-if=\"showClose && clearIcon\"\n              :class=\"[\n                nsSelect.e('caret'),\n                nsSelect.e('icon'),\n                nsSelect.e('clear'),\n              ]\"\n              @click=\"handleClearClick\"\n            >\n              <component :is=\"clearIcon\" />\n            </el-icon>\n            <el-icon\n              v-if=\"validateState && validateIcon && needStatusIcon\"\n              :class=\"[\n                nsInput.e('icon'),\n                nsInput.e('validateIcon'),\n                nsInput.is('loading', validateState === 'validating'),\n              ]\"\n            >\n              <component :is=\"validateIcon\" />\n            </el-icon>\n          </div>\n        </div>\n      </template>\n      <template #content>\n        <el-select-menu ref=\"menuRef\">\n          <div\n            v-if=\"$slots.header\"\n            :class=\"nsSelect.be('dropdown', 'header')\"\n            @click.stop\n          >\n            <slot name=\"header\" />\n          </div>\n          <el-scrollbar\n            v-show=\"states.options.size > 0 && !loading\"\n            :id=\"contentId\"\n            ref=\"scrollbarRef\"\n            tag=\"ul\"\n            :wrap-class=\"nsSelect.be('dropdown', 'wrap')\"\n            :view-class=\"nsSelect.be('dropdown', 'list')\"\n            :class=\"[nsSelect.is('empty', filteredOptionsCount === 0)]\"\n            role=\"listbox\"\n            :aria-label=\"ariaLabel\"\n            aria-orientation=\"vertical\"\n            @scroll=\"popupScroll\"\n          >\n            <el-option\n              v-if=\"showNewOption\"\n              :value=\"states.inputValue\"\n              :created=\"true\"\n            />\n            <el-options>\n              <slot />\n            </el-options>\n          </el-scrollbar>\n          <div\n            v-if=\"$slots.loading && loading\"\n            :class=\"nsSelect.be('dropdown', 'loading')\"\n          >\n            <slot name=\"loading\" />\n          </div>\n          <div\n            v-else-if=\"loading || filteredOptionsCount === 0\"\n            :class=\"nsSelect.be('dropdown', 'empty')\"\n          >\n            <slot name=\"empty\">\n              <span>{{ emptyText }}</span>\n            </slot>\n          </div>\n          <div\n            v-if=\"$slots.footer\"\n            :class=\"nsSelect.be('dropdown', 'footer')\"\n            @click.stop\n          >\n            <slot name=\"footer\" />\n          </div>\n        </el-select-menu>\n      </template>\n    </el-tooltip>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, provide, reactive, toRefs } from 'vue'\nimport { ClickOutside } from '@element-plus/directives'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElTag from '@element-plus/components/tag'\nimport ElIcon from '@element-plus/components/icon'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { isArray } from '@element-plus/utils'\nimport { useCalcInputWidth } from '@element-plus/hooks'\nimport ElOption from './option.vue'\nimport ElSelectMenu from './select-dropdown.vue'\nimport { useSelect } from './useSelect'\nimport { selectKey } from './token'\nimport ElOptions from './options'\n\nimport { SelectProps } from './select'\nimport type { SelectContext } from './token'\n\nconst COMPONENT_NAME = 'ElSelect'\nexport default defineComponent({\n  name: COMPONENT_NAME,\n  componentName: COMPONENT_NAME,\n  components: {\n    ElSelectMenu,\n    ElOption,\n    ElOptions,\n    ElTag,\n    ElScrollbar,\n    ElTooltip,\n    ElIcon,\n  },\n  directives: { ClickOutside },\n  props: SelectProps,\n  emits: [\n    UPDATE_MODEL_EVENT,\n    CHANGE_EVENT,\n    'remove-tag',\n    'clear',\n    'visible-change',\n    'focus',\n    'blur',\n    'popup-scroll',\n  ],\n\n  setup(props, { emit }) {\n    const modelValue = computed(() => {\n      const { modelValue: rawModelValue, multiple } = props\n      const fallback = multiple ? [] : undefined\n      // When it is array, we check if this is multi-select.\n      // Based on the result we get\n      if (isArray(rawModelValue)) {\n        return multiple ? rawModelValue : fallback\n      }\n\n      return multiple ? fallback : rawModelValue\n    })\n\n    const _props = reactive({\n      ...toRefs(props),\n      modelValue,\n    })\n\n    const API = useSelect(_props, emit)\n    const { calculatorRef, inputStyle } = useCalcInputWidth()\n\n    provide(\n      selectKey,\n      reactive({\n        props: _props,\n        states: API.states,\n        optionsArray: API.optionsArray,\n        handleOptionSelect: API.handleOptionSelect,\n        onOptionCreate: API.onOptionCreate,\n        onOptionDestroy: API.onOptionDestroy,\n        selectRef: API.selectRef,\n        setSelected: API.setSelected,\n      }) as unknown as SelectContext\n    )\n\n    const selectedLabel = computed(() => {\n      if (!props.multiple) {\n        return API.states.selectedLabel\n      }\n      return API.states.selected.map((i) => i.currentLabel as string)\n    })\n\n    return {\n      ...API,\n      modelValue,\n      selectedLabel,\n      calculatorRef,\n      inputStyle,\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "ElSelectMenu", "ElOption", "ElOptions", "ElTag", "ElScrollbar", "ElTooltip", "ElIcon", "ClickOutside", "SelectProps", "UPDATE_MODEL_EVENT", "CHANGE_EVENT", "computed", "isArray", "reactive", "toRefs", "useSelect", "useCalcInputWidth", "provide", "<PERSON><PERSON><PERSON>", "_resolveComponent", "_createVNode", "_withCtx", "_createElementVNode", "_normalizeClass", "_withModifiers", "_openBlock", "_createElementBlock", "_renderSlot", "_createCommentVNode", "_Fragment", "_renderList", "_createTextVNode", "_toDisplayString", "_createBlock", "_with<PERSON><PERSON><PERSON>", "_vModelText", "_resolveDynamicComponent", "_withDirectives", "_vShow", "_export_sfc"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAgUA,MAAM,cAAiB,GAAA,UAAA,CAAA;AACvB,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,cAAA;AAAA,EACN,aAAe,EAAA,cAAA;AAAA,EACf,UAAY,EAAA;AAAA,kBACVC,yBAAA;AAAA,cACAC,iBAAA;AAAA,eACAC,kBAAA;AAAA,WACAC,WAAA;AAAA,iBACAC,mBAAA;AAAA,eACAC,iBAAA;AAAA,YACAC,cAAA;AAAA,GACF;AAAA,EACA,UAAA,EAAY,gBAAEC,kBAAa,EAAA;AAAA,EAC3B,KAAO,EAAAC,kBAAA;AAAA,EACP,KAAO,EAAA;AAAA,IACLC,wBAAA;AAAA,IACAC,kBAAA;AAAA,IACA,YAAA;AAAA,IACA,OAAA;AAAA,IACA,gBAAA;AAAA,IACA,OAAA;AAAA,IACA,MAAA;AAAA,IACA,cAAA;AAAA,GACF;AAAA,EAEA,KAAM,CAAA,KAAA,EAAO,EAAE,IAAA,EAAQ,EAAA;AACrB,IAAM,MAAA,UAAA,GAAaC,aAAS,MAAM;AAChC,MAAA,MAAM,EAAE,UAAA,EAAY,aAAe,EAAA,QAAA,EAAa,GAAA,KAAA,CAAA;AAChD,MAAM,MAAA,QAAA,GAAW,QAAW,GAAA,EAAK,GAAA,KAAA,CAAA,CAAA;AAGjC,MAAI,IAAAC,cAAA,CAAQ,aAAa,CAAG,EAAA;AAC1B,QAAA,OAAO,WAAW,aAAgB,GAAA,QAAA,CAAA;AAAA,OACpC;AAEA,MAAA,OAAO,WAAW,QAAW,GAAA,aAAA,CAAA;AAAA,KAC9B,CAAA,CAAA;AAED,IAAA,MAAM,SAASC,YAAS,CAAA;AAAA,MACtB,GAAGC,WAAO,KAAK,CAAA;AAAA,MACf,UAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,GAAA,GAAMC,mBAAU,CAAA,MAAA,EAAQ,IAAI,CAAA,CAAA;AAClC,IAAA,MAAM,EAAE,aAAA,EAAe,UAAW,EAAA,GAAIC,yBAAkB,EAAA,CAAA;AAExD,IAAAC,WAAA,CAAAC,eAAA,EAAAL,YAAA,CAAA;AAAA,MACE,KAAA,EAAA,MAAA;AAAA,MACA,MAAS,EAAA,GAAA,CAAA,MAAA;AAAA,MAAA,YACA,EAAA,GAAA,CAAA,YAAA;AAAA,MAAA,kBACK,EAAA,GAAA,CAAA,kBAAA;AAAA,MAAA,gBACE,GAAI,CAAA,cAAA;AAAA,MAAA,oCACM;AAAA,MAAA,wBACJ;AAAA,MAAA,4BACC;AAAA,KAAA,CAAA,CAAA,CACrB;AAAe,IAAA,mBACE,GAAAF,YAAA,CAAA,MAAA;AAAA,MACnB,IAAC,CAAA,KAAA,CAAA,QAAA,EAAA;AAAA,QACH,OAAA,GAAA,CAAA,MAAA,CAAA,aAAA,CAAA;AAEA,OAAM;AACJ,MAAI,WAAO,MAAU,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,YAAA,CAAA,CAAA;AACnB,KAAA,CAAA,CAAA;AAAkB,IACpB,OAAA;AACA,MAAA,GAAA,GAAA;AAA8D,MAC/D,UAAA;AAED,MAAO,aAAA;AAAA,MACL,aAAG;AAAA,MACH,UAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CACA,CAAA,CAAA;AACF,SACF,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACF,EAAC,MAAA,iBAAA,GAAAQ,oBAAA,CAAA,QAAA,CAAA,CAAA;;;;;;;;;;;8CAlGO,CAAA,GAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAA,aAAA,GAAA,IAAA;AAAA,IAxSJ,YAAI,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AAAA,GAAA;AAEwC,IAC3CC,eAAA,CAAA,qBAAA,EAAA;AAA2C,MAC3C,GAAA,EAAA,YAAY;AAAoB,MAAA,OAAA,EAAA,IAAA,CAAA,mBAAA;MAmSpB,SAAA,EAAA,IAAA,CAAA,SAAA;AAAA,MAhSX,UAAI,EAAA,IAAA,CAAA,UAAA;AAAA,MACH,cAAS,EAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,QAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA;AAAA,MACT,gBAAW,EAAA,IAAA,CAAA,aAAA;AAAA,MACX,qBAAY,EAAA,IAAA,CAAA,kBAAA;AAAA,MACZ,MAAY,EAAA,IAAA,CAAA,MAAY;AAAwB,MAChD,IAAgB,EAAA,EAAA;AAAA,MAChB,OAAqB,EAAA,OAAA;AAAA,MACrB,UAAQ,EAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,SAAA,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,MACT,yBAAA,EAAA,KAAA;AAAA,MACA,kBAAQ,EAAA,KAAA;AAAA,MACP,UAAU,EAAA,IAAc,CAAA,UAAA;AAAe,MACvC,WAAyB,EAAA,IAAA,CAAA,QAAA;AAAA,MACzB,YAAkB,EAAA,IAAA,CAAA,SAAA;AAAA,MAClB,MAAY,EAAA,IAAA,CAAA,MAAA;AAAA,MACZ,YAAW,EAAA,IAAA,CAAA,eAAA;AAAA,MACX,MAAY,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAA,YAAA,GAAA,KAAA;AAAA,KAAA,EACJ;AAAA,MACR,OAAa,EAAAC,WAAA,CAAA,MAAA;AAAA,QACb,IAAA,EAAI,CAAE;AAAmB,QAAA,OAAA;AAEf,UAAAC,sBAoNH,CAAA,KAAA,EAAA;AAAA,YAAA,GAAA,EAAA,YAAA;AAAA,YAlNA,KAAA,EAAAC,kBAAA,CAAA;AAAA,cACE,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,SAAA,CAAA;AAAA,2BAA0B,CAAA,EAAA,CAAA,SAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AAAA,cAAyB,IAAA,CAAA,QAAW,CAAA,EAAA,CAAA,UAAqB,EAAA,IAAA,CAAA,MAAA,CAAA,aAAA,CAAA;AAAA,cAAwB,IAAA,CAAA,QAAA,CAAA,EAAe,CAAA,YAAA,EAAA,IAAA,CAAA,UAAoB,CAAA;AAAA,cAAe,IAAA,CAAA,QAAW,CAAA,EAAA,CAAA,UAAA,EAAe,IAAU,CAAA,cAAA,CAAA;AAAA,aAAe,CAAA;AAAsC,YAAA,OAAA,EAAAC,iBAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,SAAA,CAAA,CAAA;AAO3P,WAAA,EAAA;AAAyB,YAAA,IAAA,CAAA,MAAA,CAAA,MAAA,IAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAGlB,cAAA,GAAA,EAAA,CAAA;AADR,cAMM,GAAA,EAAA,WAAA;AAAA,cAAA,KAAA,EAAAH,kBAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA;AAAA,aAAA,EAAA;cAJJI,cAAI,CAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AAAA,aACH,EAAA,CAAA,CAAA,IAAKC,sBAAE,CAAA,MAAA,EAAA,IAAU,CAAA;AAAA,YAAAN,sBAAA,CAAA,KAAA,EAAA;;cAElB,KAAsB,EAAAC,kBAAA,CAAA;AAAA,gBAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,WAAA,CAAA;;;;AAExB,cAAA,IAAA,CAAA,QAAA,GAAAI,cAAA,CAAA,IAAA,CAAA,MAAA,EAAA,KAAA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,EAAA,MAAA;AAAA,iBAmKMF,aAAA,CAAA,IAAA,CAAA,EAAAC,sBAAA,CAAAG,YAAA,EAAA,IAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,WAAA,EAAA,CAAA,IAAA,KAAA;AAAA,kBAAA,OAAAL,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,oBAlKA,GAAA,EAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AAAA,oBACE,KAAA,EAAAH,kBAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA;AAAA;AAA4B,oBAAsCH,eAAA,CAAA,iBAAA,EAAA;AAAA,sBAAA,QAAA,EAAA,CAAA,IAAA,CAAA,cAAA,IAAA,CAAA,IAAA,CAAA,UAAA;AAA4C,sBAAA,IAAA,EAAA,IAAA,CAAA,eAAoB;AAA4B,sBAAA,IAAA,EAAA,IAAA,CAAA,OAAA;;;;sBAQpK,OAAA,EAAA,CAAA,MAAA;AAqFO,qBApFL,EAAA;AAAA,sBAwBM,OAAA,EAAAC,WAAA,CAAA,MAAA;AAAA,wBAAAC,sBAAA,CAAA,MAAA,EAAA;AAAA,0BAvBW,KAAA,EAAAC,kBAAW,CAAnB,IAAI,CAAA,QAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;AADb,yBAAA,EAAA;AAAA,0BAwBMI,cAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA;AAAA,4BAAA,KAAA,EAAA,IAAA,CAAA,YAAA;AAAA,4BAtBH;AAAqB,2BACrB,EAAA,MAAO;AAAU,4BAAAI,mBAAA,CAAAC,mBAAA,CAAA,IAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;;yBAoBT,EAAA,CAAA,CAAA;AAAA,uBAAA,CAAA;AAjB6B,sBAAA,CAAA,EAAA,CACnC;AAAM,qBAAA,EAAA,IACA,EAAA,CAAA,UAAA,EAAA,MAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,CAAA,CAAA;AAAA,mBAAA,EAAA,CAAA,CAAA,CAAA;AACE,iBACT,CAAA,EAAA,GAAA,CAAA;AAAA,gBACC,IAAA,CAAA,YAAK,gBAAE,QAAQ,CAAA,MAAA,GAAA,IAAA,CAAA,eAAA,IAAAP,aAAA,EAAA,EAAAQ,eAAA,CAAA,qBAAA,EAAA;AAAA,kBAAA,GAAA,EAAA,CAAA;AACc,kBAAA,GAAA,EAAA,eAAA;oDAUvB,IAAA,CAAA,IAAA,CAAA,mBAAA;AAAA,kBARP,qBAAA,EAAA,CAAA,QAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,CAAA;AAAA,kBAQO,MAAA,EAAA,IAAA,CAAA,MAAA;AAAA,kBAAA,SAAA,EAAA,QAAA;AAAA,kBARA,UAAA,EAAA,IAAA,CAAA,UAAO;AAAU,iBAAA,EAAA;;0CAOf,CAAA,KAAA,EAAA;AAAA,sBAAA,GAAA,EAAA,iBAJQ;AAAA,sBAAA,KAAA,EAAAV,kBACA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA;AAAA,qBAAA,EAAA;AAGR,sBAAAH,eAAA,CAAA,iBAAA,EAAA;AADF,wBAAA,QAAA,EAAA,KAAA;AAAiB,wBAAA,IAAA,EAAA,IAAA,CAAA,eAAA;AAAA,wBAAA,IAAA,EAAA,IAAA,CAAA,OAAA;AAAA,wBAAA,MAAA,EAAA,IAAA,CAAA,SAAA;AAAA,wBAAA,qBAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;AAOpB,uBAAAK,aAAA,CAAA,IAAuB,CAAA,EAAAC,sBAAkB,CAAAG,YAAA,EAAA,IAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,eAwDpC,EAAA,CAAA,IAAA,KAAA;AAAA,wBAAA,OAAAL,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;0BAvDP,GAAA,EAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AAAA,0BACH,uCAAkC,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA;AAAA,yBACb,EAAA;AAAA,0BACbN,eAAA,CAAA,iBAAA,EAAA;AAAA,4BACC,KAAA,EAAA,YAAA;AAAA,4BACG,QAAA,EAAA,CAAA,IAAA,CAAA,cAAA,IAAA,CAAA,IAAA,CAAA,UAAA;AAAA,4BAAA,IAAA,EAAA,IAAA,CAAA,eAAA;AAEF,sCAiBH,CAAA,OAAA;AAAA,4BAhBN,MAAA,EAAA,IAAA,CAAA,SAAA;AAAA,4BAgBM,qBAAA,EAAA,EAAA;AAAA,4BAAA,OAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,SAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,2BAfA,EAAA;AAAA,4BACH,OAAO,EAAAC,WAAA,CAAA,MAAA;AAAU,8BAAAC,sBAAA,CAAA,MAAA,EAAA;;+BAaT,EAAA;AAAA,gCAVIK,cAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA;AAAA,kCACJ,KAAA,EAAA,IAAA,CAAA,YAAA;AAAA,kCACA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,iCACE,EAAA,MAAA;AAAA,kCACTI,mBAAA,CAAAC,mBAAA,CAAA,IAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;AAAA,iCACM;AAAkB,+BAAA,EAAA,CAAA,CAAA;;AAIjB,4BAFP,CAAA,EAAA,CAAA;AAAA,2BAEO,EAAA,IAAA,EAAA,CAAA,UAAA,EAAA,MAAA,EAAA,MAAA,EAAA,QAAA,EAAA,SAAA,CAAA,CAAA;AAAA,yBAAA,EAAA,CAAA,CAAA,CAAA;AAAA,uBAFA,CAAA,EAAA,GAAA,CAAA;AAAiB,qBAAA,EAAA,CAAA,CAAA;AAAe,mBAAA,CAAA;AACQ,kBAAA,CAAA,EAAA,CAAA;AAAA,iBAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,EAAA,YAAA,CAAA,CAAA,IAAAJ,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,eAAA,CAAA,GAAAA,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,cAAAN,sBAAA,CAAA,KAAA,EAAA;;;;;;;;;AAK1C,kBAAA,GAAA,EAAA;AA2BH,kBA1BN,qBAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAA,UAAA,GAAA,MAAA;AAAA,kBA0BM,IAAA,EAAA,MAAA;AAAA,kBAAA,IAAA,EAAA,IAAA,CAAA,IAAA;AAAA,kBAAA,KAAA,EA1BGC,kBAAA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,kBAAc,QAAA,EAAA,IAAO,CAAA,cAAA;AAAU,kBAAA,YAAA,EAAA,IAAA,CAAA,YAAA;;AACtC,kBAAA,QAAA,EAAA,IAAA,CAAA,QAAA;AAAA,kBAwBM,IAAA,EAAA,UAAA;AAAA,kBAAA,QAAA,EAAA,CAAA,IAAA,CAAA,UAAA;AAAA,kBAvBW,UAAA,EAAA,OAAA;AADjB,kBAAA,uBAAA,EAAA,CAAA,CAAA,EAAA,GAAA,IAAA,CAAA,WAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,EAAA,KAAA,EAAA;AAAA,kBAwBM,eAAA,EAAA,IAAA,CAAA,SAAA;AAAA,kBAAA,eAAA,EAAA,IAAA,CAAA,mBAAA;AAAA,kBAtBH,YAAA,EAAA,IAAA,CAAK;AAAgB,kBACrB,mBAAA,EAAK,MAAE;AAAU,kBAAA,eAAA,EAAA,SAAA;;kDAoBT,CAAA,CAAA,MAAA,KAAA,IAAA,CAAA,eAAA,CAAA,MAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,oBAAAW,YAAA,CAAAV,iBAjBD,CAAA,CAAA,MAAA,KAAA,IAAA,CAAA,eAAA,CAAA,MAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,CAAA;AAAA,oBAAAU,YAAA,CAAAV,iBACG,CAAG,IAAc,CAAA,SAAA,EAAA,CAAA,MAAA,EAAU,SAAA,CAAA,CAAA,EAAA,CAAA,KAAA,CAAA,CAAA;AAAA,oBAAAU,YAAA,CAAAV,iBAC7B,CAAA,IAAA,CAAA,YAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA;AAAA,oBAAAU,YAAA,CAAAV,iBACA,CAAA,IAAA,CAAA,aAAA,EAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,QAAA,CAAA,CAAA;AAAA,mBAAA;AACE,kBACT,kBAAA,EAAA,IAAA,CAAA,sBAAA;AAAA,kBAAA,mBACM,EAAA,IAAA,CAAA,uBAAY;AAAY,kBAAA,gBAAA,EAAA,IAAA,CAAA,oBAAA;;AAUvB,kBARP,OAAA,EAAAA,iBAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,iBAQO,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,IAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,UAAA,EAAA,cAAA,EAAA,UAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,eAAA,EAAA,eAAA,EAAA,YAAA,EAAA,WAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,kBAAA,EAAA,SAAA,EAAA,SAAA,CAAA,CAAA,EAAA;AAAA,kBAAA,CAAAW,cAAA,EAAA,IAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,iBARA,CAAA;AAAiB,gBAAA,IAAA,CAAA,UAAA,IAAAV,aAAA,EAAA,EAAAC,sBAAA,CAAA,MAAA,EAAA;;;AAOf,kBAAA,aAAA,EAAA,MAAA;AAJQ,kBAAA,KAAA,EAAAH,kBAAA,CAAA,aACA,CAAA,CAAA,CAAA,kBAAA,CAAA,CAAA;AAAA,kBAAA,WAAA,EAAAS,mBAGR,CAAA,IAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,aAAA,CAAA,CAAA,IAAAJ,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AADF,eAAA,EAAA,CAAA,CAAA;AAAiB,cAAA,IAAA,CAAA,qBAAA,IAAAH,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,gBAAA,GAAA,EAAA,CAAA;AAAA,gBAAA,KAAA,EAAAH,kBAAA,CAAA;AAAA,kBAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC,eAAA,EAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,IAAAK,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,cA6CM,IAAA,CAAA,SAAA,IAAA,IAAA,CAAA,SAAA,IAAAH,aAAA,EAAA,EAAAQ,eAAA,CAAA,kBAAA,EAAA;AAAA,gBAAA,GAAA,EAAA,CAAA;AAAA,gBAAA,KA5CE,EAAAV,kBAAA,CAAA;AAAA,kBAAA,gBAA6B,OAAC,CAAA;AAAA,kBAAA,gBAA4C,MAAC,CAAA;AAAA,kBAAmC,IAAA,CAAA,QAAA,CAAA,CAAA,CAAS,OAAE,CAAA;AAAsB,iBAAA,CAAA;;;qCAMrJ,MA8BE;AAAA,mBAAAE,aA7BK,EAAA,EAAAQ,eAAA,CAAAG,2BAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;AAAA,iBAAA,CAAA;AACD,gBAAA,CAAA,EAAA,CAAA;AACsB,eAAA,EAAA,CAAA,EAC1B,CAAK,OAAA,EAAA,SAAA,CAAA,CAAA,IAAAR,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,cAAA,IAAA,CAAA,aACE,IAAA,IAAA,CAAA,YAAA,IAAA,IAAA,CAAA,cAAA,IAAAH,aAAA,EAAA,EAAAQ,eAAA,CAAA,kBAAA,EAAA;AAAA,gBAAA,GAAA;AAC6C,gBAAA,KACzC,EAAAV,kBAAA,CAAA;AAAA,kBAAA,IACI,CAAA,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAAA,kBACd,IAAA,CAAA,wBAAiB,CAAA;AAAA,kBAAA,IACP,CAAA,OAAA,CAAA,EAAA,CAAA,SAAA,EAAA,IAAA,CAAA,aAAA,KAAA,YAAA,CAAA;AAAA,iBAAA,CAAA;AACN,eAAA,EAAA;AACO,gBAAA,OACD,EAAAF,WAAA,CAAA,MAAA;AAAA,mBACVI,aAAA,EAAA,EAAAQ,eAAuB,4BAAe,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA;AAAA,iBAAA,CAAA;AACvB,gBAAA,CAAA,EAAA,CACf;AAAe,eAAA,EAAA,CAAA,EACf,CAAY,OAAA,CAAA,CAAA,IAAAL,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,aAAA,EAAA,CAAA,CAAA;AACK,WAAA,EAAA,EAAA,EAAA,CAAA,SACJ,CAAA,CAAA;AAAA,SAAA,CAAA;AACN,OAAA,CAAA;AAAmC,MAAA,OAAA,EAAAP,WAAA,CAAA,MAAA;AACF,QAAAD,eAAA,CAAA,yBAAA,EACL,EAAA,GAAA,EAAA,SAAA,EAAA,EAAA;AAAA,UAAA,OAAA,EAAAC,WAAA,CAAA,MAAA;AACK,YAAA,IAAA,CAAA,MAAA,CAAA,MAAA,IAAAI,aAAA,EAAA,EACNC,sBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;qBAChBH,kBAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;AAAA,cAAA,OACC,EAAAC,iBAAA,CAAA,MAAA;AAAA,eAAA,EAAA,CAAA,MACH,CAAA,CAAA;AAAA,aAAA,EAAA;AACT,cACPG,cAAA,CAAA,qBAAsB,CAAA;AAAA,aAAA,EAAA,EAAA,EAAA,CAAA,SAAA,CAAA,CAAA,IAAAC,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AA1Bd,YAAAS,kBAAA,CAAAjB,eAAA,CAAA,uBAAiB,EAAA;AAAA,cAAA,EAAA,EAAA,IAAA,CAAA,SAAA;AA6BpB,cAAA,GAAA,EAAA,cAAA;AAKN,cAAA,GAAA,EAAA,IAAA;0BAJI,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,MAAA,CAAA;AAAA,cAAA,YACQ,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,MAAA,CAAA;AAAA,cACX,KAAA,EAAAG,kBAAO,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,OAAU,EAAA,IAAA,CAAA,oBAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAAA,cAClB,IAAA,EAAA,SAAA;AAAyB,cAAA,YAAA,EAAA,IAAA,CAAA,SAAA;;;;;gBAG7B,IAAA,CAAA,aAAA,IAAAE,aAAA,EAAA,EAAAQ,eAAA,CAAA,oBAAA,EAAA;AAAA,kBAoBM,GAAA,EAAA,CAAA;AAAA,kBAAA,KAAA,EAAA,IAAA,CAAA,MAAA,CAAA,UAAA;AAAA,kBAAA,OAAA,EAAA,IAAA;mBAlBE,IAAA,EAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,IAAAL,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,gBAAAR,qCAA8B,EAAA,IAAA,EAAA;AAAA,kBAAA,oBAA6C,CAAA,MAAA;AAAA,oBAAiCO,cAAS,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;AAAA,mBAAA,CAAA;;AAA8F,iBAAA,CAAA;;;;AAUjN,cAAA,CAAAW,SAAA,EAAA,IAAA,CAAA,MAAA,CAAA,OAMD,CAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AAAA,aAAA,CAAA;uBAJG,CAAA,OAAA,IAAA,IAAA,CAAA,OAAA,IAAAb,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CACP;AAAO,cAAA,KAAA,EAJVH,kBAOO,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,SAAA,CAAA,CAAA;AAAA,aADL,EAAA;AAAA,cAAqCI,cAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;AAAA,aAAA,EAAA,CAAA,CAAA,IAAA,IAAA,CAAA,OAAA,IAAA,IAAA,CAAA,oBAAA,KAAA,CAAA,IAAAF,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AAAV,cAAA,KAAA,EAAAH,kBAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,OAAA,CAAA,CAAA;AAAA,aAAA,EAAA;AAAA,cAAAI,cAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA,EAAA,EAAA,MAAA;AAAA,gBAE7BL,sBAAA,CAAA,MAAA,EAAA,IAAA,EAAAU,mBAAA,CAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,CAAA;AAAA,eAA4C,CAAA;AAAA,aAAA,EAAA,CAAA,CAAA,IAAAJ,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,YAAA,IAAA,CAAA,MAAA,CAAA,MAAA,IAAAH,aAAV,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AAAA,cAAA,KAAA,EAAAH,kBAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;AAAA,cAAA,OAAA,EAAAC,iBAAA,CAAA,MAAA;AAAA,eAAA,EAAA,CAAA,MAAA,CAAA,CAAA;;;;;;;;AAGtC,MAAA,CAAA,EAAA,CAAA;AAAA,KA4BM,EAAA,CAAA,EAAA,CAAA,SAAA,EAAA,WAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,YAAA,EAAA,YAAA,EAAA,WAAA,EAAA,YAAA,EAAA,QAAA,EAAA,cAAA,EAAA,QAAA,CAAA,CAAA;AAAA,GAAA,EAAA,EAAA,EAAA,CAAA,cAAA,CAAA,CAAA,GAAA;AAAA,IAAA,CAAA,wBA5BG,EAAA,IAAA,CAAA,kBAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AAAA,GAAa,CAAA,CAAA;AAAiB,CAAA;aAEhB,gBAAAe,oEAIX,CAAA,EAAA,CAAA,QAAA,EAAA,YAAA,CAAA,CAAA,CAAA;;;;"}