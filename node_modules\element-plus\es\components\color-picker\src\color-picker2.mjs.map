{"version": 3, "file": "color-picker2.mjs", "sources": ["../../../../../../packages/components/color-picker/src/color-picker.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"popper\"\n    :visible=\"showPicker\"\n    :show-arrow=\"false\"\n    :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n    :offset=\"0\"\n    :gpu-acceleration=\"false\"\n    :popper-class=\"[ns.be('picker', 'panel'), ns.b('dropdown'), popperClass]\"\n    :stop-popper-mouse-event=\"false\"\n    effect=\"light\"\n    trigger=\"click\"\n    :teleported=\"teleported\"\n    :transition=\"`${ns.namespace.value}-zoom-in-top`\"\n    persistent\n    @hide=\"setShowPicker(false)\"\n  >\n    <template #content>\n      <div\n        v-click-outside:[triggerRef]=\"handleClickOutside\"\n        @keydown.esc=\"handleEsc\"\n      >\n        <div :class=\"ns.be('dropdown', 'main-wrapper')\">\n          <hue-slider ref=\"hue\" class=\"hue-slider\" :color=\"color\" vertical />\n          <sv-panel ref=\"sv\" :color=\"color\" />\n        </div>\n        <alpha-slider v-if=\"showAlpha\" ref=\"alpha\" :color=\"color\" />\n        <predefine\n          v-if=\"predefine\"\n          ref=\"predefine\"\n          :enable-alpha=\"showAlpha\"\n          :color=\"color\"\n          :colors=\"predefine\"\n        />\n        <div :class=\"ns.be('dropdown', 'btns')\">\n          <span :class=\"ns.be('dropdown', 'value')\">\n            <el-input\n              ref=\"inputRef\"\n              v-model=\"customInput\"\n              :validate-event=\"false\"\n              size=\"small\"\n              @keyup.enter=\"handleConfirm\"\n              @blur=\"handleConfirm\"\n            />\n          </span>\n          <el-button\n            :class=\"ns.be('dropdown', 'link-btn')\"\n            text\n            size=\"small\"\n            @click=\"clear\"\n          >\n            {{ t('el.colorpicker.clear') }}\n          </el-button>\n          <el-button\n            plain\n            size=\"small\"\n            :class=\"ns.be('dropdown', 'btn')\"\n            @click=\"confirmValue\"\n          >\n            {{ t('el.colorpicker.confirm') }}\n          </el-button>\n        </div>\n      </div>\n    </template>\n    <template #default>\n      <div\n        :id=\"buttonId\"\n        ref=\"triggerRef\"\n        v-bind=\"$attrs\"\n        :class=\"btnKls\"\n        role=\"button\"\n        :aria-label=\"buttonAriaLabel\"\n        :aria-labelledby=\"buttonAriaLabelledby\"\n        :aria-description=\"\n          t('el.colorpicker.description', { color: modelValue || '' })\n        \"\n        :aria-disabled=\"colorDisabled\"\n        :tabindex=\"colorDisabled ? -1 : tabindex\"\n        @keydown=\"handleKeyDown\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n      >\n        <div v-if=\"colorDisabled\" :class=\"ns.be('picker', 'mask')\" />\n        <div :class=\"ns.be('picker', 'trigger')\" @click=\"handleTrigger\">\n          <span :class=\"[ns.be('picker', 'color'), ns.is('alpha', showAlpha)]\">\n            <span\n              :class=\"ns.be('picker', 'color-inner')\"\n              :style=\"{\n                backgroundColor: displayedColor,\n              }\"\n            >\n              <el-icon\n                v-show=\"modelValue || showPanelColor\"\n                :class=\"[ns.be('picker', 'icon'), ns.is('icon-arrow-down')]\"\n              >\n                <arrow-down />\n              </el-icon>\n              <el-icon\n                v-show=\"!modelValue && !showPanelColor\"\n                :class=\"[ns.be('picker', 'empty'), ns.is('icon-close')]\"\n              >\n                <close />\n              </el-icon>\n            </span>\n          </span>\n        </div>\n      </div>\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  watch,\n} from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { ElButton } from '@element-plus/components/button'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { ClickOutside as vClickOutside } from '@element-plus/directives'\nimport { ElTooltip } from '@element-plus/components/tooltip'\nimport { ElInput } from '@element-plus/components/input'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\nimport {\n  useFocusController,\n  useLocale,\n  useNamespace,\n} from '@element-plus/hooks'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { debugWarn } from '@element-plus/utils'\nimport { ArrowDown, Close } from '@element-plus/icons-vue'\nimport AlphaSlider from './components/alpha-slider.vue'\nimport HueSlider from './components/hue-slider.vue'\nimport Predefine from './components/predefine.vue'\nimport SvPanel from './components/sv-panel.vue'\nimport Color from './utils/color'\nimport {\n  colorPickerContextKey,\n  colorPickerEmits,\n  colorPickerProps,\n} from './color-picker'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\ndefineOptions({\n  name: 'ElColorPicker',\n})\nconst props = defineProps(colorPickerProps)\nconst emit = defineEmits(colorPickerEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('color')\nconst { formItem } = useFormItem()\nconst colorSize = useFormSize()\nconst colorDisabled = useFormDisabled()\n\nconst { inputId: buttonId, isLabeledByFormItem } = useFormItemInputId(props, {\n  formItemContext: formItem,\n})\n\nconst hue = ref<InstanceType<typeof HueSlider>>()\nconst sv = ref<InstanceType<typeof SvPanel>>()\nconst alpha = ref<InstanceType<typeof AlphaSlider>>()\nconst popper = ref<TooltipInstance>()\nconst triggerRef = ref()\nconst inputRef = ref()\n\nconst { isFocused, handleFocus, handleBlur } = useFocusController(triggerRef, {\n  beforeFocus() {\n    return colorDisabled.value\n  },\n  beforeBlur(event) {\n    return popper.value?.isFocusInsideContent(event)\n  },\n  afterBlur() {\n    setShowPicker(false)\n    resetColor()\n  },\n})\n\n// active-change is used to prevent modelValue changes from triggering.\nlet shouldActiveChange = true\n\nconst color = reactive(\n  new Color({\n    enableAlpha: props.showAlpha,\n    format: props.colorFormat || '',\n    value: props.modelValue,\n  })\n) as Color\n\nconst showPicker = ref(false)\nconst showPanelColor = ref(false)\nconst customInput = ref('')\n\nconst displayedColor = computed(() => {\n  if (!props.modelValue && !showPanelColor.value) {\n    return 'transparent'\n  }\n  return displayedRgb(color, props.showAlpha)\n})\n\nconst currentColor = computed(() => {\n  return !props.modelValue && !showPanelColor.value ? '' : color.value\n})\n\nconst buttonAriaLabel = computed<string | undefined>(() => {\n  return !isLabeledByFormItem.value\n    ? props.ariaLabel || t('el.colorpicker.defaultLabel')\n    : undefined\n})\n\nconst buttonAriaLabelledby = computed<string | undefined>(() => {\n  return isLabeledByFormItem.value ? formItem?.labelId : undefined\n})\n\nconst btnKls = computed(() => {\n  return [\n    ns.b('picker'),\n    ns.is('disabled', colorDisabled.value),\n    ns.bm('picker', colorSize.value),\n    ns.is('focused', isFocused.value),\n  ]\n})\n\nfunction displayedRgb(color: Color, showAlpha: boolean) {\n  if (!(color instanceof Color)) {\n    throw new TypeError('color should be instance of _color Class')\n  }\n\n  const { r, g, b } = color.toRgb()\n  return showAlpha\n    ? `rgba(${r}, ${g}, ${b}, ${color.get('alpha') / 100})`\n    : `rgb(${r}, ${g}, ${b})`\n}\n\nfunction setShowPicker(value: boolean) {\n  showPicker.value = value\n}\n\nconst debounceSetShowPicker = debounce(setShowPicker, 100, { leading: true })\nfunction show() {\n  if (colorDisabled.value) return\n  setShowPicker(true)\n}\n\nfunction hide() {\n  debounceSetShowPicker(false)\n  resetColor()\n}\n\nfunction resetColor() {\n  nextTick(() => {\n    if (props.modelValue) {\n      color.fromString(props.modelValue)\n    } else {\n      color.value = ''\n      nextTick(() => {\n        showPanelColor.value = false\n      })\n    }\n  })\n}\n\nfunction handleTrigger() {\n  if (colorDisabled.value) return\n  if (showPicker.value) {\n    resetColor()\n  }\n  debounceSetShowPicker(!showPicker.value)\n}\n\nfunction handleConfirm() {\n  color.fromString(customInput.value)\n}\n\nfunction confirmValue() {\n  const value = color.value\n  emit(UPDATE_MODEL_EVENT, value)\n  emit(CHANGE_EVENT, value)\n  if (props.validateEvent) {\n    formItem?.validate('change').catch((err) => debugWarn(err))\n  }\n  debounceSetShowPicker(false)\n  // check if modelValue change, if not change, then reset color.\n  nextTick(() => {\n    const newColor = new Color({\n      enableAlpha: props.showAlpha,\n      format: props.colorFormat || '',\n      value: props.modelValue,\n    })\n    if (!color.compare(newColor)) {\n      resetColor()\n    }\n  })\n}\n\nfunction clear() {\n  debounceSetShowPicker(false)\n  emit(UPDATE_MODEL_EVENT, null)\n  emit(CHANGE_EVENT, null)\n  if (props.modelValue !== null && props.validateEvent) {\n    formItem?.validate('change').catch((err) => debugWarn(err))\n  }\n  resetColor()\n}\n\nfunction handleClickOutside() {\n  if (!showPicker.value) return\n  hide()\n  isFocused.value && focus()\n}\n\nfunction handleEsc(event: KeyboardEvent) {\n  event.preventDefault()\n  event.stopPropagation()\n  setShowPicker(false)\n  resetColor()\n}\n\nfunction handleKeyDown(event: KeyboardEvent) {\n  switch (event.code) {\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n    case EVENT_CODE.space:\n      event.preventDefault()\n      event.stopPropagation()\n      show()\n      inputRef.value.focus()\n      break\n    case EVENT_CODE.esc:\n      handleEsc(event)\n      break\n  }\n}\n\nfunction focus() {\n  triggerRef.value.focus()\n}\n\nfunction blur() {\n  triggerRef.value.blur()\n}\n\nonMounted(() => {\n  if (props.modelValue) {\n    customInput.value = currentColor.value\n  }\n})\n\nwatch(\n  () => props.modelValue,\n  (newVal) => {\n    if (!newVal) {\n      showPanelColor.value = false\n    } else if (newVal && newVal !== color.value) {\n      shouldActiveChange = false\n      color.fromString(newVal)\n    }\n  }\n)\n\nwatch(\n  () => [props.colorFormat, props.showAlpha],\n  () => {\n    color.enableAlpha = props.showAlpha\n    color.format = props.colorFormat || color.format\n    color.doOnChange()\n    emit(UPDATE_MODEL_EVENT, color.value)\n  }\n)\n\nwatch(\n  () => currentColor.value,\n  (val) => {\n    customInput.value = val\n    shouldActiveChange && emit('activeChange', val)\n    shouldActiveChange = true\n  }\n)\n\nwatch(\n  () => color.value,\n  () => {\n    if (!props.modelValue && !showPanelColor.value) {\n      showPanelColor.value = true\n    }\n  }\n)\n\nwatch(\n  () => showPicker.value,\n  () => {\n    nextTick(() => {\n      hue.value?.update()\n      sv.value?.update()\n      alpha.value?.update()\n    })\n  }\n)\n\nprovide(colorPickerContextKey, {\n  currentColor,\n})\n\ndefineExpose({\n  /**\n   * @description current color object\n   */\n  color,\n  /**\n   * @description manually show ColorPicker\n   */\n  show,\n  /**\n   * @description manually hide ColorPicker\n   */\n  hide,\n  /**\n   * @description focus the input element\n   */\n  focus,\n  /**\n   * @description blur the input element\n   */\n  blur,\n})\n</script>\n"], "names": ["color", "_openBlock", "_createBlock", "_unref", "_withCtx", "_withDirectives", "_createElementBlock", "_with<PERSON><PERSON><PERSON>", "_createElementVNode", "_normalizeClass", "_createVNode"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;mCA6Jc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,OAAO,CAAA,CAAA;AAC/B,IAAM,MAAA,EAAE,QAAS,EAAA,GAAI,WAAY,EAAA,CAAA;AACjC,IAAA,MAAM,YAAY,WAAY,EAAA,CAAA;AAC9B,IAAA,MAAM,gBAAgB,eAAgB,EAAA,CAAA;AAEtC,IAAA,MAAM,EAAE,OAAS,EAAA,QAAA,EAAU,mBAAoB,EAAA,GAAI,mBAAmB,KAAO,EAAA;AAAA,MAC3E,eAAiB,EAAA,QAAA;AAAA,KAClB,CAAA,CAAA;AAED,IAAA,MAAM,MAAM,GAAoC,EAAA,CAAA;AAChD,IAAA,MAAM,KAAK,GAAkC,EAAA,CAAA;AAC7C,IAAA,MAAM,QAAQ,GAAsC,EAAA,CAAA;AACpD,IAAA,MAAM,SAAS,GAAqB,EAAA,CAAA;AACpC,IAAA,MAAM,aAAa,GAAI,EAAA,CAAA;AACvB,IAAA,MAAM,WAAW,GAAI,EAAA,CAAA;AAErB,IAAA,MAAM,EAAE,SAAW,EAAA,WAAA,EAAa,UAAW,EAAA,GAAI,mBAAmB,UAAY,EAAA;AAAA,MAC5E,WAAc,GAAA;AACZ,QAAA,OAAO,aAAc,CAAA,KAAA,CAAA;AAAA,OACvB;AAAA,MACA,WAAW,KAAO,EAAA;AAChB,QAAO,IAAA,EAAA,CAAA;AAAwC,QACjD,OAAA,CAAA,EAAA,GAAA,MAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACY;AACV,MAAA,SAAA,GAAA;AACA,QAAW,aAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACb,UAAA,EAAA,CAAA;AAAA,OACD;AAGD,KAAA,CAAA,CAAA;AAEA,IAAA,IAAA,kBAAc,GAAA,IAAA,CAAA;AAAA,IAAA,MACR,KAAM,GAAA,QAAA,CAAA,IAAA,KAAA,CAAA;AAAA,MAAA,kBACW,CAAA,SAAA;AAAA,MACnB,MAAA,EAAA,iBAA6B,IAAA,EAAA;AAAA,MAAA,YAChB,CAAA,UAAA;AAAA,KAAA,CACf,CAAC,CAAA;AAAA,IACH,MAAA,UAAA,GAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AAEA,IAAM,MAAA,iBAAiB,GAAK,CAAA,KAAA,CAAA,CAAA;AAC5B,IAAM,MAAA,WAAA,GAAA,GAAiB,IAAI,CAAK;AAChC,IAAM,MAAA,cAAc,WAAM,CAAA,MAAA;AAE1B,MAAM,IAAA,CAAA,KAAA,CAAA,UAAiB,mBAAe,CAAA,KAAA,EAAA;AACpC,QAAA,OAAK,aAAoB,CAAA;AACvB,OAAO;AAAA,MACT,OAAA,YAAA,CAAA,KAAA,EAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AACA,KAAO,CAAA,CAAA;AAAmC,IAC5C,MAAC,YAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAM,OAAA,CAAA,KAAA,CAAA,cAAwB,CAAM,cAAA,CAAA,KAAA,GAAA,EAAA,GAAA,KAAA,CAAA,KAAA,CAAA;AAClC,KAAA,CAAA,CAAA;AAA+D,IACjE,MAAC,eAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAM,OAAA,CAAA,yBAAqD,GAAA,KAAA,CAAA,SAAA,IAAA,CAAA,CAAA,6BAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AACzD,KAAA,CAAA,CAAA;AAEI,IACN,MAAC,oBAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAM,OAAA,mBAAA,CAAuB,gBAAmC,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,OAAA,GAAA,KAAA,CAAA,CAAA;AAC9D,KAAO,CAAA,CAAA;AAAgD,IACzD,MAAC,MAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAM,OAAA;AACJ,QAAO,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA;AAAA,QACL,EAAA,CAAG,EAAE,CAAQ,UAAA,EAAA,aAAA,CAAA,KAAA,CAAA;AAAA,QACb,EAAG,CAAA,EAAA,CAAG,QAAY,EAAA,SAAA,CAAA,KAAA,CAAc;AAAK,QACrC,EAAG,CAAA,EAAA,CAAG,SAAU,EAAA,SAAU,CAAK,KAAA,CAAA;AAAA,OAAA,CAC/B;AAAgC,KAClC,CAAA,CAAA;AAAA,IACF,SAAC,YAAA,CAAA,MAAA,EAAA,SAAA,EAAA;AAED,MAAS,IAAA,EAAA,MAAA,iBAA+C,CAAA,EAAA;AACtD,QAAI,8DAA2B,CAAA,CAAA;AAC7B,OAAM;AAAwD,MAChE,MAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,GAAA,MAAA,CAAA,KAAA,EAAA,CAAA;AAEA,MAAA,gBAAgB,GAAA,CAAIA,OAAM,CAAM,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,EAAA,MAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,GAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAChC,KAAA;AAEwB,IAC1B,SAAA,aAAA,CAAA,KAAA,EAAA;AAEA,MAAA,wBAAuC,CAAA;AACrC,KAAA;AAAmB,IACrB,MAAA,qBAAA,GAAA,QAAA,CAAA,aAAA,EAAA,GAAA,EAAA,EAAA,OAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAEA,IAAA;AACA,MAAA,IAAA,aAAgB,CAAA,KAAA;AACd,QAAA;AACA,MAAA,aAAA,CAAc,IAAI,CAAA,CAAA;AAAA,KACpB;AAEA,IAAA,SAAS,IAAO,GAAA;AACd,MAAA,qBAAA,CAAsB,KAAK,CAAA,CAAA;AAC3B,MAAW,UAAA,EAAA,CAAA;AAAA,KACb;AAEA,IAAA,SAAS,UAAa,GAAA;AACpB,MAAA,QAAA,CAAS,MAAM;AACb,QAAA,IAAI,MAAM,UAAY,EAAA;AACpB,UAAM,KAAA,CAAA,UAAA,CAAW,MAAM,UAAU,CAAA,CAAA;AAAA,SAC5B,MAAA;AACL,UAAA,KAAA,CAAM,KAAQ,GAAA,EAAA,CAAA;AACd,UAAA,QAAA,CAAS,MAAM;AACb,YAAA,cAAA,CAAe,KAAQ,GAAA,KAAA,CAAA;AAAA,WACxB,CAAA,CAAA;AAAA,SACH;AAAA,OACD,CAAA,CAAA;AAAA,KACH;AAEA,IAAA,SAAS,aAAgB,GAAA;AACvB,MAAA,IAAI,cAAc,KAAO;AACzB,QAAA;AACE,MAAW,IAAA,UAAA,CAAA,KAAA,EAAA;AAAA,QACb,UAAA,EAAA,CAAA;AACA,OAAsB;AAAiB,MACzC,qBAAA,CAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAA;AACE,IAAM,SAAA;AAA4B,MACpC,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAA;AACE,IAAA,qBAAoB,GAAA;AACpB,MAAA,yBAAyB,CAAK;AAC9B,MAAA,IAAA,CAAK,kBAAmB,EAAA,KAAA,CAAA,CAAA;AACxB,MAAA,IAAI,aAAqB,EAAA,KAAA,CAAA,CAAA;AACvB,MAAU,IAAA,KAAA,CAAA;AAAgD,QAC5D,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA,OAAA;AAEA,MAAA,qBAAe,CAAA,KAAA,CAAA,CAAA;AACb,MAAM,QAAA,CAAA,MAAA;AAAqB,QAAA,iBACN,IAAA,KAAA,CAAA;AAAA,UACnB,kBAA6B,CAAA,SAAA;AAAA,UAC7B,QAAa,KAAA,CAAA,WAAA,IAAA,EAAA;AAAA,UACd,KAAA,EAAA,KAAA,CAAA,UAAA;AACD,SAAA,CAAA,CAAA;AACE,QAAW,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,QAAA,CAAA,EAAA;AAAA,UACb,UAAA,EAAA,CAAA;AAAA,SACD;AAAA,OACH,CAAA,CAAA;AAEA,KAAA;AACE,IAAA,SAAA,KAAA,GAAA;AACA,MAAA,2BAA6B,CAAA,CAAA;AAC7B,MAAA,IAAA,CAAK,kBAAkB,EAAA,IAAA,CAAA,CAAA;AACvB,MAAA,IAAI,CAAM,YAAA,EAAA,IAAA,CAAA,CAAe;AACvB,MAAU,IAAA,KAAA,CAAA,UAAA,SAAiB,IAAE,KAAO,CAAQ,aAAA,EAAA;AAAc,QAC5D,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA,OAAW;AAAA,MACb,UAAA,EAAA,CAAA;AAEA,KAAA;AACE,IAAI,2BAAmB,GAAA;AACvB,MAAK,IAAA,CAAA,UAAA,CAAA,KAAA;AACL,QAAA,OAAA;AAAyB,MAC3B,IAAA,EAAA,CAAA;AAEA,MAAA,mBAAyC,KAAA,EAAA,CAAA;AACvC,KAAA;AACA,IAAA,SAAsB,SAAA,CAAA,KAAA,EAAA;AACtB,MAAA,KAAA,CAAA,cAAmB,EAAA,CAAA;AACnB,MAAW,KAAA,CAAA,eAAA,EAAA,CAAA;AAAA,MACb,aAAA,CAAA,KAAA,CAAA,CAAA;AAEA,MAAA;AACE,KAAA;AAAoB,IAAA,SACb,aAAW,CAAA,KAAA,EAAA;AAAA,MAAA,QACA,KAAA,CAAA,IAAA;AAAA,QAChB,KAAK,UAAW,CAAA,KAAA,CAAA;AACd,QAAA,KAAA,UAAqB,CAAA,WAAA,CAAA;AACrB,QAAA,KAAA,UAAsB,CAAA,KAAA;AACtB,UAAK,KAAA,CAAA,cAAA,EAAA,CAAA;AACL,UAAA,KAAA,CAAA,eAAqB,EAAA,CAAA;AACrB,UAAA,IAAA,EAAA,CAAA;AAAA,kBACc,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA;AACd,UAAA,MAAA;AACA,QAAA,KAAA,UAAA,CAAA,GAAA;AAAA,UACJ,SAAA,CAAA,KAAA,CAAA,CAAA;AAAA,UACF,MAAA;AAEA,OAAA;AACE,KAAA;AAAuB,IACzB,SAAA,KAAA,GAAA;AAEA,MAAA,UAAgB,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA;AACd,KAAA;AAAsB,IACxB,SAAA,IAAA,GAAA;AAEA,MAAA,UAAgB,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA;AACd,KAAA;AACE,IAAA,SAAA,CAAA,MAAY;AAAqB,MACnC,IAAA,KAAA,CAAA,UAAA,EAAA;AAAA,QACD,WAAA,CAAA,KAAA,GAAA,YAAA,CAAA,KAAA,CAAA;AAED,OAAA;AAAA,KAAA,CACE;AAAY,IAAA,KACA,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,CAAA,MAAA,KAAA;AACV,MAAA,IAAA,CAAA,MAAa,EAAA;AACX,QAAA,cAAA,CAAA,KAAuB,GAAA,KAAA,CAAA;AAAA,OAAA,MACd,IAAA,MAAA,IAAA,MAAqB,KAAA,KAAA,CAAA,KAAa,EAAA;AAC3C,QAAqB,kBAAA,GAAA,KAAA,CAAA;AACrB,QAAA,KAAA,CAAA,iBAAuB,CAAA,CAAA;AAAA,OACzB;AAAA,KACF,CAAA,CAAA;AAAA,IACF,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,WAAA,EAAA,KAAA,CAAA,SAAA,CAAA,EAAA,MAAA;AAEA,MAAA,KAAA,CAAA,WAAA,GAAA,KAAA,CAAA,SAAA,CAAA;AAAA,MACE,KAAM,CAAA,MAAO,GAAA,KAAA,CAAA,WAAmB,IAAS,KAAA,CAAA,MAAA,CAAA;AAAA,MACzC,KAAM,CAAA,UAAA,EAAA,CAAA;AACJ,MAAA,IAAA,CAAA,kBAA0B,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AAC1B,KAAM,CAAA,CAAA;AACN,IAAA,KAAA,CAAA,MAAiB,YAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AACjB,MAAK,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AAA+B,MACtC,kBAAA,IAAA,IAAA,CAAA,cAAA,EAAA,GAAA,CAAA,CAAA;AAAA,MACF,kBAAA,GAAA,IAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AAAA,IAAA,YACqB,KAAA,CAAA,KAAA,EAAA,MAAA;AAAA,MACnB,IAAS,CAAA,KAAA,CAAA,UAAA,IAAA,CAAA,cAAA,CAAA,KAAA,EAAA;AACP,QAAA,cAAoB,CAAA,KAAA,GAAA,IAAA,CAAA;AACpB,OAAsB;AACtB,KAAqB,CAAA,CAAA;AAAA,IACvB,KAAA,CAAA,MAAA,UAAA,CAAA,KAAA,EAAA,MAAA;AAAA,MACF,QAAA,CAAA,MAAA;AAEA,QAAA,IAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AAAA,cACc,GAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AAAA,QACN,CAAA,EAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AACJ,QAAA,CAAA,EAAA,GAAK,KAAM,CAAc,KAAA,KAAA,IAAC,eAAe,MAAO,EAAA,CAAA;AAC9C,OAAA,CAAA,CAAA;AAAuB,KACzB,CAAA,CAAA;AAAA,IACF,OAAA,CAAA,qBAAA,EAAA;AAAA,MACF,YAAA;AAEA,KAAA,CAAA,CAAA;AAAA,IAAA;AACmB,MACjB,KAAM;AACJ,MAAA,IAAA;AACE,MAAA,IAAA;AACA,MAAA,KAAA;AACA,MAAA,IAAA;AAAoB,KAAA,CAAA,CAAA;AACrB,IACH,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACF,OAAAC,SAAA,EAAA,EAAAC,WAAA,CAAAC,KAAA,CAAA,SAAA,CAAA,EAAA;AAEA,QAAA,OAA+B,EAAA,QAAA;AAAA,QAC7B,GAAA,EAAA,MAAA;AAAA,QACD,OAAA,EAAA,UAAA,CAAA,KAAA;AAED,QAAa,YAAA,EAAA,KAAA;AAAA,QAAA,qBAAA,EAAA,CAAA,QAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,CAAA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,kBAAA,EAAA,KAAA;AAAA,QAIX,cAAA,EAAA,CAAAA,KAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,QAAA,EAAA,OAAA,CAAA,EAAAA,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA;AAAA,QAAA,yBAAA,EAAA,KAAA;AAAA,QAAA,MAAA,EAAA,OAAA;AAAA,QAAA,OAAA,EAAA,OAAA;AAAA,QAIA,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,QAAA,UAAA,EAAA,CAAA,EAAAA,KAAA,CAAA,EAAA,CAAA,CAAA,SAAA,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,QAAA,UAAA,EAAA,EAAA;AAAA,QAAA,MAAA,EAAA,CAAA,MAAA,KAAA,aAAA,CAAA,KAAA,CAAA;AAAA,OAIA,EAAA;AAAA,QAAA,OAAA,EAAAC,OAAA,CAAA,MAAA;AAAA,UAAAC,cAAA,EAAAJ,SAAA,EAAA,EAAAK,kBAAA,CAAA,KAAA,EAAA;AAAA,YAAA,SAAA,EAAAC,QAAA,CAAA,SAAA,EAAA,CAAA,KAAA,CAAA,CAAA;AAAA,WAIA,EAAA;AAAA,YAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,KAAA,EAAAC,cAAA,CAAAN,KAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,UAAA,EAAA,cAAA,CAAA,CAAA;AAAA,aAAA,EAAA;AAAA,cAIAO,WAAA,CAAA,SAAA,EAAA;AAAA,gBACD,OAAA,EAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}