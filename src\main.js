// 载入基础样式
// import './assets/main.css'  // 注释掉不存在的CSS文件引用
import 'element-plus/dist/index.css'  // 添加Element Plus样式

// 更新加载状态
if (window.updateLoadingStatus) {
  window.updateLoadingStatus('Vue模块已导入')
}

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import './assets/styles/global.css'
import App from './App.vue'
import ElementPlus from 'element-plus'  // 导入Element Plus
import * as ElementPlusIconsVue from '@element-plus/icons-vue'  // 导入所有图标

// 更新加载状态
if (window.updateLoadingStatus) {
  window.updateLoadingStatus('创建Vue应用实例')
}

// 创建Vue应用实例
const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus)  // 使用Element Plus

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue错误:', err)
  console.error('错误信息:', info)
  
  if (window.updateLoadingStatus) {
    window.updateLoadingStatus(`Vue错误: ${err.message}`)
  }
}

// 更新加载状态
if (window.updateLoadingStatus) {
  window.updateLoadingStatus('准备挂载Vue应用')
}

// 挂载应用
app.mount('#app')

// 调试信息
console.log('Vue应用已挂载')

// 隐藏加载状态
if (window.hideLoading) {
  window.hideLoading()
} 