<template>
  <div class="page-header">
    <div class="header-content">
      <h1 class="title">{{ title }}</h1>
      <p class="subtitle" v-if="subtitle">{{ subtitle }}</p>
    </div>
    <div class="action-area" v-if="$slots.actions">
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.header-content {
  flex: 1;
}

.title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.subtitle {
  font-size: 14px;
  color: #606266;
  margin: 0;
  line-height: 1.5;
}

.action-area {
  margin-left: 20px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .action-area {
    margin-left: 0;
    margin-top: 15px;
    align-self: flex-end;
  }
}
</style> 